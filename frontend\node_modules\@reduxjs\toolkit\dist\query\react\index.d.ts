import { reactHooksModule, reactHooksModuleName } from './module';
export * from '@reduxjs/toolkit/query';
export { ApiProvider } from './ApiProvider';
declare const createApi: import("@reduxjs/toolkit/query").CreateApi<typeof import("@reduxjs/toolkit/query").coreModuleName | typeof reactHooksModuleName>;
export type { TypedUseQueryHookResult, TypedUseQueryStateResult, TypedUseQuerySubscriptionResult, TypedUseMutationResult, } from './buildHooks';
export { createApi, reactHooksModule, reactHooksModuleName };
