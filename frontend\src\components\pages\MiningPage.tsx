import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Button, Card, Progress, Statistic, Spin, message } from 'antd-mobile';
import { 
  PlayCircleOutline, 
  PauseCircleOutline, 
  TrendingUpOutline,
  WalletOutline 
} from 'antd-mobile-icons';
import { RootState } from '../../store';
import { MiningStatus, RealTimeData } from '../../types';
import { useMining } from '../../hooks/useMining';
import { useWebSocket } from '../../hooks/useWebSocket';
import { formatNumber, formatHashRate } from '../../utils/formatters';
import './MiningPage.scss';

const MiningPage: React.FC = () => {
  const dispatch = useDispatch();
  const user = useSelector((state: RootState) => state.auth.user);
  const [isStarting, setIsStarting] = useState(false);
  const [isStopping, setIsStopping] = useState(false);
  
  // 使用自定义Hook
  const { 
    miningStatus, 
    startMining, 
    stopMining, 
    isLoading, 
    error 
  } = useMining();
  
  // WebSocket连接
  const { data: realTimeData, isConnected } = useWebSocket<RealTimeData>('/ws/mining');

  // 处理开始挖矿
  const handleStartMining = async () => {
    if (!user) {
      message.error('请先登录');
      return;
    }

    setIsStarting(true);
    try {
      await startMining();
      message.success('挖矿已开始');
    } catch (error) {
      message.error('启动挖矿失败');
    } finally {
      setIsStarting(false);
    }
  };

  // 处理停止挖矿
  const handleStopMining = async () => {
    setIsStopping(true);
    try {
      await stopMining();
      message.success('挖矿已停止');
    } catch (error) {
      message.error('停止挖矿失败');
    } finally {
      setIsStopping(false);
    }
  };

  // 计算挖矿进度
  const calculateProgress = () => {
    if (!miningStatus?.startTime) return 0;
    
    const startTime = new Date(miningStatus.startTime).getTime();
    const now = Date.now();
    const elapsed = now - startTime;
    
    // 假设挖矿会话持续24小时
    const sessionDuration = 24 * 60 * 60 * 1000;
    return Math.min((elapsed / sessionDuration) * 100, 100);
  };

  // 格式化时间
  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 计算挖矿时间
  const getMiningTime = () => {
    if (!miningStatus?.startTime) return 0;
    
    const startTime = new Date(miningStatus.startTime).getTime();
    const now = Date.now();
    return Math.floor((now - startTime) / 1000);
  };

  if (isLoading) {
    return (
      <div className="mining-page loading">
        <Spin size="large" />
        <p>加载中...</p>
      </div>
    );
  }

  return (
    <div className="mining-page">
      {/* 头部状态卡片 */}
      <Card className="status-card">
        <div className="status-header">
          <h2>挖矿状态</h2>
          <div className={`status-indicator ${miningStatus?.isActive ? 'active' : 'inactive'}`}>
            {miningStatus?.isActive ? '运行中' : '已停止'}
          </div>
        </div>
        
        {miningStatus?.isActive && (
          <div className="mining-progress">
            <Progress 
              percent={calculateProgress()} 
              text={`${calculateProgress().toFixed(1)}%`}
              style={{ '--track-width': '8px' }}
            />
            <p className="mining-time">
              挖矿时间: {formatTime(getMiningTime())}
            </p>
          </div>
        )}
      </Card>

      {/* 实时数据卡片 */}
      <Card className="stats-card">
        <div className="stats-grid">
          <div className="stat-item">
            <TrendingUpOutline />
            <div className="stat-content">
              <h3>哈希率</h3>
              <p className="stat-value">
                {formatHashRate(realTimeData?.hashRate || miningStatus?.hashRate || 0)}
              </p>
            </div>
          </div>
          
          <div className="stat-item">
            <WalletOutline />
            <div className="stat-content">
              <h3>总哈希数</h3>
              <p className="stat-value">
                {formatNumber(realTimeData?.totalHashes || miningStatus?.totalHashes || 0)}
              </p>
            </div>
          </div>
          
          <div className="stat-item">
            <WalletOutline />
            <div className="stat-content">
              <h3>今日收益</h3>
              <p className="stat-value">
                {formatNumber(realTimeData?.earnings || 0)} USDT
              </p>
            </div>
          </div>
          
          <div className="stat-item">
            <TrendingUpOutline />
            <div className="stat-content">
              <h3>在线用户</h3>
              <p className="stat-value">
                {formatNumber(realTimeData?.activeUsers || 0)}
              </p>
            </div>
          </div>
        </div>
      </Card>

      {/* 控制按钮 */}
      <Card className="control-card">
        <div className="control-buttons">
          {!miningStatus?.isActive ? (
            <Button
              block
              color="primary"
              size="large"
              loading={isStarting}
              onClick={handleStartMining}
              disabled={!user}
            >
              <PlayCircleOutline />
              开始挖矿
            </Button>
          ) : (
            <Button
              block
              color="danger"
              size="large"
              loading={isStopping}
              onClick={handleStopMining}
            >
              <PauseCircleOutline />
              停止挖矿
            </Button>
          )}
        </div>
        
        {!user && (
          <p className="login-tip">
            请先登录后再开始挖矿
          </p>
        )}
      </Card>

      {/* 挖矿信息 */}
      {miningStatus?.isActive && (
        <Card className="info-card">
          <h3>挖矿信息</h3>
          <div className="info-list">
            <div className="info-item">
              <span>会话ID:</span>
              <span>{miningStatus.sessionId}</span>
            </div>
            <div className="info-item">
              <span>开始时间:</span>
              <span>{new Date(miningStatus.startTime!).toLocaleString()}</span>
            </div>
            <div className="info-item">
              <span>连接状态:</span>
              <span className={isConnected ? 'connected' : 'disconnected'}>
                {isConnected ? '已连接' : '未连接'}
              </span>
            </div>
          </div>
        </Card>
      )}

      {/* 错误提示 */}
      {error && (
        <Card className="error-card">
          <p className="error-message">{error}</p>
        </Card>
      )}
    </div>
  );
};

export default MiningPage; 