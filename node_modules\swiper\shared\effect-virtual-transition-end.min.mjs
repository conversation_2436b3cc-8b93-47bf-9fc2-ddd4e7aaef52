import{l as elementTransitionEnd}from"./utils.min.mjs";function effectVirtualTransitionEnd(e){let{swiper:n,duration:t,transformElements:r,allSlides:i}=e;const{activeIndex:a}=n;if(n.params.virtualTranslate&&0!==t){let e,t=!1;e=i?r:r.filter((e=>{const t=e.classList.contains("swiper-slide-transform")?(e=>{if(!e.parentElement)return n.slides.find((n=>n.shadowRoot&&n.shadowRoot===e.parentNode));return e.parentElement})(e):e;return n.getSlideIndex(t)===a})),e.forEach((e=>{elementTransitionEnd(e,(()=>{if(t)return;if(!n||n.destroyed)return;t=!0,n.animating=!1;const e=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0});n.wrapperEl.dispatchEvent(e)}))}))}}export{effectVirtualTransitionEnd as e};
//# sourceMappingURL=effect-virtual-transition-end.min.mjs.map