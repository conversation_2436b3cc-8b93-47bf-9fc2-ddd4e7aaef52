{"version": 3, "file": "match-sorter.umd.js", "sources": ["../node_modules/remove-accents/index.js", "../src/index.ts"], "sourcesContent": ["var characterMap = {\n\t\"À\": \"A\",\n\t\"Á\": \"A\",\n\t\"Â\": \"A\",\n\t\"Ã\": \"A\",\n\t\"Ä\": \"A\",\n\t\"Å\": \"A\",\n\t\"Ấ\": \"A\",\n\t\"Ắ\": \"A\",\n\t\"Ẳ\": \"A\",\n\t\"Ẵ\": \"A\",\n\t\"Ặ\": \"A\",\n\t\"Æ\": \"AE\",\n\t\"Ầ\": \"A\",\n\t\"Ằ\": \"A\",\n\t\"Ȃ\": \"A\",\n\t\"Ả\": \"A\",\n\t\"Ạ\": \"A\",\n\t\"Ẩ\": \"A\",\n\t\"Ẫ\": \"A\",\n\t\"Ậ\": \"A\",\n\t\"Ç\": \"C\",\n\t\"Ḉ\": \"C\",\n\t\"È\": \"E\",\n\t\"É\": \"E\",\n\t\"Ê\": \"E\",\n\t\"Ë\": \"E\",\n\t\"Ế\": \"E\",\n\t\"Ḗ\": \"E\",\n\t\"Ề\": \"E\",\n\t\"Ḕ\": \"E\",\n\t\"Ḝ\": \"E\",\n\t\"Ȇ\": \"E\",\n\t\"Ẻ\": \"E\",\n\t\"Ẽ\": \"E\",\n\t\"Ẹ\": \"E\",\n\t\"Ể\": \"E\",\n\t\"Ễ\": \"E\",\n\t\"Ệ\": \"E\",\n\t\"Ì\": \"I\",\n\t\"Í\": \"I\",\n\t\"Î\": \"I\",\n\t\"Ï\": \"I\",\n\t\"Ḯ\": \"I\",\n\t\"Ȋ\": \"I\",\n\t\"Ỉ\": \"I\",\n\t\"Ị\": \"I\",\n\t\"Ð\": \"D\",\n\t\"Ñ\": \"N\",\n\t\"Ò\": \"O\",\n\t\"Ó\": \"O\",\n\t\"Ô\": \"O\",\n\t\"Õ\": \"O\",\n\t\"Ö\": \"O\",\n\t\"Ø\": \"O\",\n\t\"Ố\": \"O\",\n\t\"Ṍ\": \"O\",\n\t\"Ṓ\": \"O\",\n\t\"Ȏ\": \"O\",\n\t\"Ỏ\": \"O\",\n\t\"Ọ\": \"O\",\n\t\"Ổ\": \"O\",\n\t\"Ỗ\": \"O\",\n\t\"Ộ\": \"O\",\n\t\"Ờ\": \"O\",\n\t\"Ở\": \"O\",\n\t\"Ỡ\": \"O\",\n\t\"Ớ\": \"O\",\n\t\"Ợ\": \"O\",\n\t\"Ù\": \"U\",\n\t\"Ú\": \"U\",\n\t\"Û\": \"U\",\n\t\"Ü\": \"U\",\n\t\"Ủ\": \"U\",\n\t\"Ụ\": \"U\",\n\t\"Ử\": \"U\",\n\t\"Ữ\": \"U\",\n\t\"Ự\": \"U\",\n\t\"Ý\": \"Y\",\n\t\"à\": \"a\",\n\t\"á\": \"a\",\n\t\"â\": \"a\",\n\t\"ã\": \"a\",\n\t\"ä\": \"a\",\n\t\"å\": \"a\",\n\t\"ấ\": \"a\",\n\t\"ắ\": \"a\",\n\t\"ẳ\": \"a\",\n\t\"ẵ\": \"a\",\n\t\"ặ\": \"a\",\n\t\"æ\": \"ae\",\n\t\"ầ\": \"a\",\n\t\"ằ\": \"a\",\n\t\"ȃ\": \"a\",\n\t\"ả\": \"a\",\n\t\"ạ\": \"a\",\n\t\"ẩ\": \"a\",\n\t\"ẫ\": \"a\",\n\t\"ậ\": \"a\",\n\t\"ç\": \"c\",\n\t\"ḉ\": \"c\",\n\t\"è\": \"e\",\n\t\"é\": \"e\",\n\t\"ê\": \"e\",\n\t\"ë\": \"e\",\n\t\"ế\": \"e\",\n\t\"ḗ\": \"e\",\n\t\"ề\": \"e\",\n\t\"ḕ\": \"e\",\n\t\"ḝ\": \"e\",\n\t\"ȇ\": \"e\",\n\t\"ẻ\": \"e\",\n\t\"ẽ\": \"e\",\n\t\"ẹ\": \"e\",\n\t\"ể\": \"e\",\n\t\"ễ\": \"e\",\n\t\"ệ\": \"e\",\n\t\"ì\": \"i\",\n\t\"í\": \"i\",\n\t\"î\": \"i\",\n\t\"ï\": \"i\",\n\t\"ḯ\": \"i\",\n\t\"ȋ\": \"i\",\n\t\"ỉ\": \"i\",\n\t\"ị\": \"i\",\n\t\"ð\": \"d\",\n\t\"ñ\": \"n\",\n\t\"ò\": \"o\",\n\t\"ó\": \"o\",\n\t\"ô\": \"o\",\n\t\"õ\": \"o\",\n\t\"ö\": \"o\",\n\t\"ø\": \"o\",\n\t\"ố\": \"o\",\n\t\"ṍ\": \"o\",\n\t\"ṓ\": \"o\",\n\t\"ȏ\": \"o\",\n\t\"ỏ\": \"o\",\n\t\"ọ\": \"o\",\n\t\"ổ\": \"o\",\n\t\"ỗ\": \"o\",\n\t\"ộ\": \"o\",\n\t\"ờ\": \"o\",\n\t\"ở\": \"o\",\n\t\"ỡ\": \"o\",\n\t\"ớ\": \"o\",\n\t\"ợ\": \"o\",\n\t\"ù\": \"u\",\n\t\"ú\": \"u\",\n\t\"û\": \"u\",\n\t\"ü\": \"u\",\n\t\"ủ\": \"u\",\n\t\"ụ\": \"u\",\n\t\"ử\": \"u\",\n\t\"ữ\": \"u\",\n\t\"ự\": \"u\",\n\t\"ý\": \"y\",\n\t\"ÿ\": \"y\",\n\t\"Ā\": \"A\",\n\t\"ā\": \"a\",\n\t\"Ă\": \"A\",\n\t\"ă\": \"a\",\n\t\"Ą\": \"A\",\n\t\"ą\": \"a\",\n\t\"Ć\": \"C\",\n\t\"ć\": \"c\",\n\t\"Ĉ\": \"C\",\n\t\"ĉ\": \"c\",\n\t\"Ċ\": \"C\",\n\t\"ċ\": \"c\",\n\t\"Č\": \"C\",\n\t\"č\": \"c\",\n\t\"C̆\": \"C\",\n\t\"c̆\": \"c\",\n\t\"Ď\": \"D\",\n\t\"ď\": \"d\",\n\t\"Đ\": \"D\",\n\t\"đ\": \"d\",\n\t\"Ē\": \"E\",\n\t\"ē\": \"e\",\n\t\"Ĕ\": \"E\",\n\t\"ĕ\": \"e\",\n\t\"Ė\": \"E\",\n\t\"ė\": \"e\",\n\t\"Ę\": \"E\",\n\t\"ę\": \"e\",\n\t\"Ě\": \"E\",\n\t\"ě\": \"e\",\n\t\"Ĝ\": \"G\",\n\t\"Ǵ\": \"G\",\n\t\"ĝ\": \"g\",\n\t\"ǵ\": \"g\",\n\t\"Ğ\": \"G\",\n\t\"ğ\": \"g\",\n\t\"Ġ\": \"G\",\n\t\"ġ\": \"g\",\n\t\"Ģ\": \"G\",\n\t\"ģ\": \"g\",\n\t\"Ĥ\": \"H\",\n\t\"ĥ\": \"h\",\n\t\"Ħ\": \"H\",\n\t\"ħ\": \"h\",\n\t\"Ḫ\": \"H\",\n\t\"ḫ\": \"h\",\n\t\"Ĩ\": \"I\",\n\t\"ĩ\": \"i\",\n\t\"Ī\": \"I\",\n\t\"ī\": \"i\",\n\t\"Ĭ\": \"I\",\n\t\"ĭ\": \"i\",\n\t\"Į\": \"I\",\n\t\"į\": \"i\",\n\t\"İ\": \"I\",\n\t\"ı\": \"i\",\n\t\"Ĳ\": \"IJ\",\n\t\"ĳ\": \"ij\",\n\t\"Ĵ\": \"J\",\n\t\"ĵ\": \"j\",\n\t\"Ķ\": \"K\",\n\t\"ķ\": \"k\",\n\t\"Ḱ\": \"K\",\n\t\"ḱ\": \"k\",\n\t\"K̆\": \"K\",\n\t\"k̆\": \"k\",\n\t\"Ĺ\": \"L\",\n\t\"ĺ\": \"l\",\n\t\"Ļ\": \"L\",\n\t\"ļ\": \"l\",\n\t\"Ľ\": \"L\",\n\t\"ľ\": \"l\",\n\t\"Ŀ\": \"L\",\n\t\"ŀ\": \"l\",\n\t\"Ł\": \"l\",\n\t\"ł\": \"l\",\n\t\"Ḿ\": \"M\",\n\t\"ḿ\": \"m\",\n\t\"M̆\": \"M\",\n\t\"m̆\": \"m\",\n\t\"Ń\": \"N\",\n\t\"ń\": \"n\",\n\t\"Ņ\": \"N\",\n\t\"ņ\": \"n\",\n\t\"Ň\": \"N\",\n\t\"ň\": \"n\",\n\t\"ŉ\": \"n\",\n\t\"N̆\": \"N\",\n\t\"n̆\": \"n\",\n\t\"Ō\": \"O\",\n\t\"ō\": \"o\",\n\t\"Ŏ\": \"O\",\n\t\"ŏ\": \"o\",\n\t\"Ő\": \"O\",\n\t\"ő\": \"o\",\n\t\"Œ\": \"OE\",\n\t\"œ\": \"oe\",\n\t\"P̆\": \"P\",\n\t\"p̆\": \"p\",\n\t\"Ŕ\": \"R\",\n\t\"ŕ\": \"r\",\n\t\"Ŗ\": \"R\",\n\t\"ŗ\": \"r\",\n\t\"Ř\": \"R\",\n\t\"ř\": \"r\",\n\t\"R̆\": \"R\",\n\t\"r̆\": \"r\",\n\t\"Ȓ\": \"R\",\n\t\"ȓ\": \"r\",\n\t\"Ś\": \"S\",\n\t\"ś\": \"s\",\n\t\"Ŝ\": \"S\",\n\t\"ŝ\": \"s\",\n\t\"Ş\": \"S\",\n\t\"Ș\": \"S\",\n\t\"ș\": \"s\",\n\t\"ş\": \"s\",\n\t\"Š\": \"S\",\n\t\"š\": \"s\",\n\t\"Ţ\": \"T\",\n\t\"ţ\": \"t\",\n\t\"ț\": \"t\",\n\t\"Ț\": \"T\",\n\t\"Ť\": \"T\",\n\t\"ť\": \"t\",\n\t\"Ŧ\": \"T\",\n\t\"ŧ\": \"t\",\n\t\"T̆\": \"T\",\n\t\"t̆\": \"t\",\n\t\"Ũ\": \"U\",\n\t\"ũ\": \"u\",\n\t\"Ū\": \"U\",\n\t\"ū\": \"u\",\n\t\"Ŭ\": \"U\",\n\t\"ŭ\": \"u\",\n\t\"Ů\": \"U\",\n\t\"ů\": \"u\",\n\t\"Ű\": \"U\",\n\t\"ű\": \"u\",\n\t\"Ų\": \"U\",\n\t\"ų\": \"u\",\n\t\"Ȗ\": \"U\",\n\t\"ȗ\": \"u\",\n\t\"V̆\": \"V\",\n\t\"v̆\": \"v\",\n\t\"Ŵ\": \"W\",\n\t\"ŵ\": \"w\",\n\t\"Ẃ\": \"W\",\n\t\"ẃ\": \"w\",\n\t\"X̆\": \"X\",\n\t\"x̆\": \"x\",\n\t\"Ŷ\": \"Y\",\n\t\"ŷ\": \"y\",\n\t\"Ÿ\": \"Y\",\n\t\"Y̆\": \"Y\",\n\t\"y̆\": \"y\",\n\t\"Ź\": \"Z\",\n\t\"ź\": \"z\",\n\t\"Ż\": \"Z\",\n\t\"ż\": \"z\",\n\t\"Ž\": \"Z\",\n\t\"ž\": \"z\",\n\t\"ſ\": \"s\",\n\t\"ƒ\": \"f\",\n\t\"Ơ\": \"O\",\n\t\"ơ\": \"o\",\n\t\"Ư\": \"U\",\n\t\"ư\": \"u\",\n\t\"Ǎ\": \"A\",\n\t\"ǎ\": \"a\",\n\t\"Ǐ\": \"I\",\n\t\"ǐ\": \"i\",\n\t\"Ǒ\": \"O\",\n\t\"ǒ\": \"o\",\n\t\"Ǔ\": \"U\",\n\t\"ǔ\": \"u\",\n\t\"Ǖ\": \"U\",\n\t\"ǖ\": \"u\",\n\t\"Ǘ\": \"U\",\n\t\"ǘ\": \"u\",\n\t\"Ǚ\": \"U\",\n\t\"ǚ\": \"u\",\n\t\"Ǜ\": \"U\",\n\t\"ǜ\": \"u\",\n\t\"Ứ\": \"U\",\n\t\"ứ\": \"u\",\n\t\"Ṹ\": \"U\",\n\t\"ṹ\": \"u\",\n\t\"Ǻ\": \"A\",\n\t\"ǻ\": \"a\",\n\t\"Ǽ\": \"AE\",\n\t\"ǽ\": \"ae\",\n\t\"Ǿ\": \"O\",\n\t\"ǿ\": \"o\",\n\t\"Þ\": \"TH\",\n\t\"þ\": \"th\",\n\t\"Ṕ\": \"P\",\n\t\"ṕ\": \"p\",\n\t\"Ṥ\": \"S\",\n\t\"ṥ\": \"s\",\n\t\"X́\": \"X\",\n\t\"x́\": \"x\",\n\t\"Ѓ\": \"Г\",\n\t\"ѓ\": \"г\",\n\t\"Ќ\": \"К\",\n\t\"ќ\": \"к\",\n\t\"A̋\": \"A\",\n\t\"a̋\": \"a\",\n\t\"E̋\": \"E\",\n\t\"e̋\": \"e\",\n\t\"I̋\": \"I\",\n\t\"i̋\": \"i\",\n\t\"Ǹ\": \"N\",\n\t\"ǹ\": \"n\",\n\t\"Ồ\": \"O\",\n\t\"ồ\": \"o\",\n\t\"Ṑ\": \"O\",\n\t\"ṑ\": \"o\",\n\t\"Ừ\": \"U\",\n\t\"ừ\": \"u\",\n\t\"Ẁ\": \"W\",\n\t\"ẁ\": \"w\",\n\t\"Ỳ\": \"Y\",\n\t\"ỳ\": \"y\",\n\t\"Ȁ\": \"A\",\n\t\"ȁ\": \"a\",\n\t\"Ȅ\": \"E\",\n\t\"ȅ\": \"e\",\n\t\"Ȉ\": \"I\",\n\t\"ȉ\": \"i\",\n\t\"Ȍ\": \"O\",\n\t\"ȍ\": \"o\",\n\t\"Ȑ\": \"R\",\n\t\"ȑ\": \"r\",\n\t\"Ȕ\": \"U\",\n\t\"ȕ\": \"u\",\n\t\"B̌\": \"B\",\n\t\"b̌\": \"b\",\n\t\"Č̣\": \"C\",\n\t\"č̣\": \"c\",\n\t\"Ê̌\": \"E\",\n\t\"ê̌\": \"e\",\n\t\"F̌\": \"F\",\n\t\"f̌\": \"f\",\n\t\"Ǧ\": \"G\",\n\t\"ǧ\": \"g\",\n\t\"Ȟ\": \"H\",\n\t\"ȟ\": \"h\",\n\t\"J̌\": \"J\",\n\t\"ǰ\": \"j\",\n\t\"Ǩ\": \"K\",\n\t\"ǩ\": \"k\",\n\t\"M̌\": \"M\",\n\t\"m̌\": \"m\",\n\t\"P̌\": \"P\",\n\t\"p̌\": \"p\",\n\t\"Q̌\": \"Q\",\n\t\"q̌\": \"q\",\n\t\"Ř̩\": \"R\",\n\t\"ř̩\": \"r\",\n\t\"Ṧ\": \"S\",\n\t\"ṧ\": \"s\",\n\t\"V̌\": \"V\",\n\t\"v̌\": \"v\",\n\t\"W̌\": \"W\",\n\t\"w̌\": \"w\",\n\t\"X̌\": \"X\",\n\t\"x̌\": \"x\",\n\t\"Y̌\": \"Y\",\n\t\"y̌\": \"y\",\n\t\"A̧\": \"A\",\n\t\"a̧\": \"a\",\n\t\"B̧\": \"B\",\n\t\"b̧\": \"b\",\n\t\"Ḑ\": \"D\",\n\t\"ḑ\": \"d\",\n\t\"Ȩ\": \"E\",\n\t\"ȩ\": \"e\",\n\t\"Ɛ̧\": \"E\",\n\t\"ɛ̧\": \"e\",\n\t\"Ḩ\": \"H\",\n\t\"ḩ\": \"h\",\n\t\"I̧\": \"I\",\n\t\"i̧\": \"i\",\n\t\"Ɨ̧\": \"I\",\n\t\"ɨ̧\": \"i\",\n\t\"M̧\": \"M\",\n\t\"m̧\": \"m\",\n\t\"O̧\": \"O\",\n\t\"o̧\": \"o\",\n\t\"Q̧\": \"Q\",\n\t\"q̧\": \"q\",\n\t\"U̧\": \"U\",\n\t\"u̧\": \"u\",\n\t\"X̧\": \"X\",\n\t\"x̧\": \"x\",\n\t\"Z̧\": \"Z\",\n\t\"z̧\": \"z\",\n\t\"й\":\"и\",\n\t\"Й\":\"И\",\n\t\"ё\":\"е\",\n\t\"Ё\":\"Е\",\n};\n\nvar chars = Object.keys(characterMap).join('|');\nvar allAccents = new RegExp(chars, 'g');\nvar firstAccent = new RegExp(chars, '');\n\nfunction matcher(match) {\n\treturn characterMap[match];\n}\n\nvar removeAccents = function(string) {\n\treturn string.replace(allAccents, matcher);\n};\n\nvar hasAccents = function(string) {\n\treturn !!string.match(firstAccent);\n};\n\nmodule.exports = removeAccents;\nmodule.exports.has = hasAccents;\nmodule.exports.remove = removeAccents;\n", "/**\n * @name match-sorter\n * @license MIT license.\n * @copyright (c) 2020 Kent <PERSON>\n * <AUTHOR> <<EMAIL>> (https://kentcdodds.com)\n */\nimport removeAccents from 'remove-accents'\n\ntype KeyAttributes = {\n  threshold?: Ranking\n  maxRanking: Ranking\n  minRanking: Ranking\n}\ninterface RankingInfo {\n  rankedValue: string\n  rank: Ranking\n  keyIndex: number\n  keyThreshold: Ranking | undefined\n}\n\ninterface ValueGetterKey<ItemType> {\n  (item: ItemType): string | Array<string>\n}\ninterface IndexedItem<ItemType> {\n  item: ItemType\n  index: number\n}\ninterface RankedItem<ItemType> extends RankingInfo, IndexedItem<ItemType> {}\n\ninterface BaseSorter<ItemType> {\n  (a: RankedItem<ItemType>, b: RankedItem<ItemType>): number\n}\n\ninterface Sorter<ItemType> {\n  (matchItems: Array<RankedItem<ItemType>>): Array<RankedItem<ItemType>>\n}\n\ninterface KeyAttributesOptions<ItemType> {\n  key?: string | ValueGetterKey<ItemType>\n  threshold?: Ranking\n  maxRanking?: Ranking\n  minRanking?: Ranking\n}\n\ntype KeyOption<ItemType> =\n  | KeyAttributesOptions<ItemType>\n  | ValueGetterKey<ItemType>\n  | string\n\ninterface MatchSorterOptions<ItemType = unknown> {\n  keys?: ReadonlyArray<KeyOption<ItemType>>\n  threshold?: Ranking\n  baseSort?: BaseSorter<ItemType>\n  keepDiacritics?: boolean\n  sorter?: Sorter<ItemType>\n}\ntype IndexableByString = Record<string, unknown>\n\nconst rankings = {\n  CASE_SENSITIVE_EQUAL: 7,\n  EQUAL: 6,\n  STARTS_WITH: 5,\n  WORD_STARTS_WITH: 4,\n  CONTAINS: 3,\n  ACRONYM: 2,\n  MATCHES: 1,\n  NO_MATCH: 0,\n} as const\n\ntype Ranking = typeof rankings[keyof typeof rankings]\n\nconst defaultBaseSortFn: BaseSorter<unknown> = (a, b) =>\n  String(a.rankedValue).localeCompare(String(b.rankedValue))\n\n/**\n * Takes an array of items and a value and returns a new array with the items that match the given value\n * @param {Array} items - the items to sort\n * @param {String} value - the value to use for ranking\n * @param {Object} options - Some options to configure the sorter\n * @return {Array} - the new sorted array\n */\nfunction matchSorter<ItemType = string>(\n  items: ReadonlyArray<ItemType>,\n  value: string,\n  options: MatchSorterOptions<ItemType> = {},\n): Array<ItemType> {\n  const {\n    keys,\n    threshold = rankings.MATCHES,\n    baseSort = defaultBaseSortFn,\n    sorter = matchedItems =>\n      matchedItems.sort((a, b) => sortRankedValues(a, b, baseSort)),\n  } = options\n  const matchedItems = items.reduce(reduceItemsToRanked, [])\n  return sorter(matchedItems).map(({item}) => item)\n\n  function reduceItemsToRanked(\n    matches: Array<RankedItem<ItemType>>,\n    item: ItemType,\n    index: number,\n  ): Array<RankedItem<ItemType>> {\n    const rankingInfo = getHighestRanking(item, keys, value, options)\n    const {rank, keyThreshold = threshold} = rankingInfo\n    if (rank >= keyThreshold) {\n      matches.push({...rankingInfo, item, index})\n    }\n    return matches\n  }\n}\n\nmatchSorter.rankings = rankings\n\n/**\n * Gets the highest ranking for value for the given item based on its values for the given keys\n * @param {*} item - the item to rank\n * @param {Array} keys - the keys to get values from the item for the ranking\n * @param {String} value - the value to rank against\n * @param {Object} options - options to control the ranking\n * @return {{rank: Number, keyIndex: Number, keyThreshold: Number}} - the highest ranking\n */\nfunction getHighestRanking<ItemType>(\n  item: ItemType,\n  keys: ReadonlyArray<KeyOption<ItemType>> | undefined,\n  value: string,\n  options: MatchSorterOptions<ItemType>,\n): RankingInfo {\n  if (!keys) {\n    // if keys is not specified, then we assume the item given is ready to be matched\n    const stringItem = (item as unknown) as string\n    return {\n      // ends up being duplicate of 'item' in matches but consistent\n      rankedValue: stringItem,\n      rank: getMatchRanking(stringItem, value, options),\n      keyIndex: -1,\n      keyThreshold: options.threshold,\n    }\n  }\n  const valuesToRank = getAllValuesToRank(item, keys)\n  return valuesToRank.reduce(\n    (\n      {rank, rankedValue, keyIndex, keyThreshold},\n      {itemValue, attributes},\n      i,\n    ) => {\n      let newRank = getMatchRanking(itemValue, value, options)\n      let newRankedValue = rankedValue\n      const {minRanking, maxRanking, threshold} = attributes\n      if (newRank < minRanking && newRank >= rankings.MATCHES) {\n        newRank = minRanking\n      } else if (newRank > maxRanking) {\n        newRank = maxRanking\n      }\n      if (newRank > rank) {\n        rank = newRank\n        keyIndex = i\n        keyThreshold = threshold\n        newRankedValue = itemValue\n      }\n      return {rankedValue: newRankedValue, rank, keyIndex, keyThreshold}\n    },\n    {\n      rankedValue: (item as unknown) as string,\n      rank: rankings.NO_MATCH as Ranking,\n      keyIndex: -1,\n      keyThreshold: options.threshold,\n    },\n  )\n}\n\n/**\n * Gives a rankings score based on how well the two strings match.\n * @param {String} testString - the string to test against\n * @param {String} stringToRank - the string to rank\n * @param {Object} options - options for the match (like keepDiacritics for comparison)\n * @returns {Number} the ranking for how well stringToRank matches testString\n */\nfunction getMatchRanking<ItemType>(\n  testString: string,\n  stringToRank: string,\n  options: MatchSorterOptions<ItemType>,\n): Ranking {\n  testString = prepareValueForComparison(testString, options)\n  stringToRank = prepareValueForComparison(stringToRank, options)\n\n  // too long\n  if (stringToRank.length > testString.length) {\n    return rankings.NO_MATCH\n  }\n\n  // case sensitive equals\n  if (testString === stringToRank) {\n    return rankings.CASE_SENSITIVE_EQUAL\n  }\n\n  // Lower casing before further comparison\n  testString = testString.toLowerCase()\n  stringToRank = stringToRank.toLowerCase()\n\n  // case insensitive equals\n  if (testString === stringToRank) {\n    return rankings.EQUAL\n  }\n\n  // starts with\n  if (testString.startsWith(stringToRank)) {\n    return rankings.STARTS_WITH\n  }\n\n  // word starts with\n  if (testString.includes(` ${stringToRank}`)) {\n    return rankings.WORD_STARTS_WITH\n  }\n\n  // contains\n  if (testString.includes(stringToRank)) {\n    return rankings.CONTAINS\n  } else if (stringToRank.length === 1) {\n    // If the only character in the given stringToRank\n    //   isn't even contained in the testString, then\n    //   it's definitely not a match.\n    return rankings.NO_MATCH\n  }\n\n  // acronym\n  if (getAcronym(testString).includes(stringToRank)) {\n    return rankings.ACRONYM\n  }\n\n  // will return a number between rankings.MATCHES and\n  // rankings.MATCHES + 1 depending  on how close of a match it is.\n  return getClosenessRanking(testString, stringToRank)\n}\n\n/**\n * Generates an acronym for a string.\n *\n * @param {String} string the string for which to produce the acronym\n * @returns {String} the acronym\n */\nfunction getAcronym(string: string): string {\n  let acronym = ''\n  const wordsInString = string.split(' ')\n  wordsInString.forEach(wordInString => {\n    const splitByHyphenWords = wordInString.split('-')\n    splitByHyphenWords.forEach(splitByHyphenWord => {\n      acronym += splitByHyphenWord.substr(0, 1)\n    })\n  })\n  return acronym\n}\n\n/**\n * Returns a score based on how spread apart the\n * characters from the stringToRank are within the testString.\n * A number close to rankings.MATCHES represents a loose match. A number close\n * to rankings.MATCHES + 1 represents a tighter match.\n * @param {String} testString - the string to test against\n * @param {String} stringToRank - the string to rank\n * @returns {Number} the number between rankings.MATCHES and\n * rankings.MATCHES + 1 for how well stringToRank matches testString\n */\nfunction getClosenessRanking(\n  testString: string,\n  stringToRank: string,\n): Ranking {\n  let matchingInOrderCharCount = 0\n  let charNumber = 0\n  function findMatchingCharacter(\n    matchChar: string,\n    string: string,\n    index: number,\n  ) {\n    for (let j = index, J = string.length; j < J; j++) {\n      const stringChar = string[j]\n      if (stringChar === matchChar) {\n        matchingInOrderCharCount += 1\n        return j + 1\n      }\n    }\n    return -1\n  }\n  function getRanking(spread: number) {\n    const spreadPercentage = 1 / spread\n    const inOrderPercentage = matchingInOrderCharCount / stringToRank.length\n    const ranking = rankings.MATCHES + inOrderPercentage * spreadPercentage\n    return ranking as Ranking\n  }\n  const firstIndex = findMatchingCharacter(stringToRank[0], testString, 0)\n  if (firstIndex < 0) {\n    return rankings.NO_MATCH\n  }\n  charNumber = firstIndex\n  for (let i = 1, I = stringToRank.length; i < I; i++) {\n    const matchChar = stringToRank[i]\n    charNumber = findMatchingCharacter(matchChar, testString, charNumber)\n    const found = charNumber > -1\n    if (!found) {\n      return rankings.NO_MATCH\n    }\n  }\n\n  const spread = charNumber - firstIndex\n  return getRanking(spread)\n}\n\n/**\n * Sorts items that have a rank, index, and keyIndex\n * @param {Object} a - the first item to sort\n * @param {Object} b - the second item to sort\n * @return {Number} -1 if a should come first, 1 if b should come first, 0 if equal\n */\nfunction sortRankedValues<ItemType>(\n  a: RankedItem<ItemType>,\n  b: RankedItem<ItemType>,\n  baseSort: BaseSorter<ItemType>,\n): number {\n  const aFirst = -1\n  const bFirst = 1\n  const {rank: aRank, keyIndex: aKeyIndex} = a\n  const {rank: bRank, keyIndex: bKeyIndex} = b\n  const same = aRank === bRank\n  if (same) {\n    if (aKeyIndex === bKeyIndex) {\n      // use the base sort function as a tie-breaker\n      return baseSort(a, b)\n    } else {\n      return aKeyIndex < bKeyIndex ? aFirst : bFirst\n    }\n  } else {\n    return aRank > bRank ? aFirst : bFirst\n  }\n}\n\n/**\n * Prepares value for comparison by stringifying it, removing diacritics (if specified)\n * @param {String} value - the value to clean\n * @param {Object} options - {keepDiacritics: whether to remove diacritics}\n * @return {String} the prepared value\n */\nfunction prepareValueForComparison<ItemType>(\n  value: string,\n  {keepDiacritics}: MatchSorterOptions<ItemType>,\n): string {\n  // value might not actually be a string at this point (we don't get to choose)\n  // so part of preparing the value for comparison is ensure that it is a string\n  value = `${value}` // toString\n  if (!keepDiacritics) {\n    value = removeAccents(value)\n  }\n  return value\n}\n\n/**\n * Gets value for key in item at arbitrarily nested keypath\n * @param {Object} item - the item\n * @param {Object|Function} key - the potentially nested keypath or property callback\n * @return {Array} - an array containing the value(s) at the nested keypath\n */\nfunction getItemValues<ItemType>(\n  item: ItemType,\n  key: KeyOption<ItemType>,\n): Array<string> {\n  if (typeof key === 'object') {\n    key = key.key as string\n  }\n  let value: string | Array<string> | null | unknown\n  if (typeof key === 'function') {\n    value = key(item)\n  } else if (item == null) {\n    value = null\n  } else if (Object.hasOwnProperty.call(item, key)) {\n    value = (item as IndexableByString)[key]\n  } else if (key.includes('.')) {\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-call\n    return getNestedValues<ItemType>(key, item)\n  } else {\n    value = null\n  }\n\n  // because `value` can also be undefined\n  if (value == null) {\n    return []\n  }\n  if (Array.isArray(value)) {\n    return value\n  }\n  return [String(value)]\n}\n\n/**\n * Given path: \"foo.bar.baz\"\n * And item: {foo: {bar: {baz: 'buzz'}}}\n *   -> 'buzz'\n * @param path a dot-separated set of keys\n * @param item the item to get the value from\n */\nfunction getNestedValues<ItemType>(\n  path: string,\n  item: ItemType,\n): Array<string> {\n  const keys = path.split('.')\n\n  type ValueA = Array<ItemType | IndexableByString | string>\n  let values: ValueA = [item]\n\n  for (let i = 0, I = keys.length; i < I; i++) {\n    const nestedKey = keys[i]\n    let nestedValues: ValueA = []\n\n    for (let j = 0, J = values.length; j < J; j++) {\n      const nestedItem = values[j]\n\n      if (nestedItem == null) continue\n\n      if (Object.hasOwnProperty.call(nestedItem, nestedKey)) {\n        const nestedValue = (nestedItem as IndexableByString)[nestedKey]\n        if (nestedValue != null) {\n          nestedValues.push(nestedValue as IndexableByString | string)\n        }\n      } else if (nestedKey === '*') {\n        // ensure that values is an array\n        nestedValues = nestedValues.concat(nestedItem)\n      }\n    }\n\n    values = nestedValues\n  }\n\n  if (Array.isArray(values[0])) {\n    // keep allowing the implicit wildcard for an array of strings at the end of\n    // the path; don't use `.flat()` because that's not available in node.js v10\n    const result: Array<string> = []\n    return result.concat(...(values as Array<string>))\n  }\n  // Based on our logic it should be an array of strings by now...\n  // assuming the user's path terminated in strings\n  return values as Array<string>\n}\n\n/**\n * Gets all the values for the given keys in the given item and returns an array of those values\n * @param item - the item from which the values will be retrieved\n * @param keys - the keys to use to retrieve the values\n * @return objects with {itemValue, attributes}\n */\nfunction getAllValuesToRank<ItemType>(\n  item: ItemType,\n  keys: ReadonlyArray<KeyOption<ItemType>>,\n) {\n  const allValues: Array<{itemValue: string; attributes: KeyAttributes}> = []\n  for (let j = 0, J = keys.length; j < J; j++) {\n    const key = keys[j]\n    const attributes = getKeyAttributes(key)\n    const itemValues = getItemValues(item, key)\n    for (let i = 0, I = itemValues.length; i < I; i++) {\n      allValues.push({\n        itemValue: itemValues[i],\n        attributes,\n      })\n    }\n  }\n  return allValues\n}\n\nconst defaultKeyAttributes = {\n  maxRanking: Infinity as Ranking,\n  minRanking: -Infinity as Ranking,\n}\n/**\n * Gets all the attributes for the given key\n * @param key - the key from which the attributes will be retrieved\n * @return object containing the key's attributes\n */\nfunction getKeyAttributes<ItemType>(key: KeyOption<ItemType>): KeyAttributes {\n  if (typeof key === 'string') {\n    return defaultKeyAttributes\n  }\n  return {...defaultKeyAttributes, ...key}\n}\n\nexport {matchSorter, rankings, defaultBaseSortFn}\n\nexport type {\n  MatchSorterOptions,\n  KeyAttributesOptions,\n  KeyOption,\n  KeyAttributes,\n  RankingInfo,\n  ValueGetterKey,\n}\n\n/*\neslint\n  no-continue: \"off\",\n*/\n"], "names": ["characterMap", "chars", "Object", "keys", "join", "allAccents", "RegExp", "firstAccent", "matcher", "match", "removeAccents", "string", "replace", "hasAccents", "removeAccentsModule", "exports", "removeAccents_1", "has", "remove", "rankings", "CASE_SENSITIVE_EQUAL", "EQUAL", "STARTS_WITH", "WORD_STARTS_WITH", "CONTAINS", "ACRONYM", "MATCHES", "NO_MATCH", "defaultBaseSortFn", "a", "b", "String", "rankedValue", "localeCompare", "matchSorter", "items", "value", "options", "threshold", "baseSort", "sorter", "matchedItems", "sort", "sortRankedValues", "reduce", "reduceItemsToRanked", "map", "_ref", "item", "matches", "index", "rankingInfo", "getHighestRanking", "rank", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "push", "stringItem", "getMatchRanking", "keyIndex", "valuesToRank", "getAllValuesToRank", "_ref2", "_ref3", "i", "itemValue", "attributes", "newRank", "newRankedValue", "minRanking", "maxRanking", "testString", "stringToRank", "prepareValueForComparison", "length", "toLowerCase", "startsWith", "includes", "getAcronym", "getClosenessRanking", "acronym", "wordsInString", "split", "for<PERSON>ach", "wordInString", "splitByHyphenWords", "splitByHyphenWord", "substr", "matchingInOrderCharCount", "char<PERSON><PERSON>ber", "findMatchingCharacter", "matchChar", "j", "J", "stringChar", "getRanking", "spread", "spreadPercentage", "inOrderPercentage", "ranking", "firstIndex", "I", "found", "<PERSON><PERSON><PERSON><PERSON>", "b<PERSON><PERSON><PERSON>", "aRank", "aKeyIndex", "bRank", "bKeyIndex", "same", "_ref4", "keepDiacritics", "getItemValues", "key", "hasOwnProperty", "call", "getNestedValues", "Array", "isArray", "path", "values", "nested<PERSON><PERSON>", "nested<PERSON><PERSON><PERSON>", "nestedItem", "nestedV<PERSON>ue", "concat", "result", "allValues", "getKeyAttributes", "itemValues", "defaultKeyAttributes", "Infinity"], "mappings": ";;;;;;;;CAAA,IAAIA,YAAY,GAAG;CAClB,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,IAAI;CACT,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,IAAI;CACT,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,IAAI;CACT,EAAA,GAAG,EAAE,IAAI;CACT,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,IAAI;CACT,EAAA,GAAG,EAAE,IAAI;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,IAAI;CACT,EAAA,GAAG,EAAE,IAAI;CACT,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,IAAI;CACT,EAAA,GAAG,EAAE,IAAI;CACT,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,GAAG,EAAE,GAAG;CACR,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,IAAI,EAAE,GAAG;CACT,EAAA,GAAG,EAAC,GAAG;CACP,EAAA,GAAG,EAAC,GAAG;CACP,EAAA,GAAG,EAAC,GAAG;CACP,EAAA,GAAG,EAAC,GAAA;CACL,CAAC,CAAA;CAED,IAAIC,KAAK,GAAGC,MAAM,CAACC,IAAI,CAACH,YAAY,CAAC,CAACI,IAAI,CAAC,GAAG,CAAC,CAAA;CAC/C,IAAIC,UAAU,GAAG,IAAIC,MAAM,CAACL,KAAK,EAAE,GAAG,CAAC,CAAA;CACvC,IAAIM,WAAW,GAAG,IAAID,MAAM,CAACL,KAAK,EAAE,EAAE,CAAC,CAAA;CAEvC,SAASO,OAAOA,CAACC,KAAK,EAAE;GACvB,OAAOT,YAAY,CAACS,KAAK,CAAC,CAAA;CAC3B,CAAA;CAEA,IAAIC,aAAa,GAAG,UAASC,MAAM,EAAE;CACpC,EAAA,OAAOA,MAAM,CAACC,OAAO,CAACP,UAAU,EAAEG,OAAO,CAAC,CAAA;CAC3C,CAAC,CAAA;CAED,IAAIK,UAAU,GAAG,UAASF,MAAM,EAAE;CACjC,EAAA,OAAO,CAAC,CAACA,MAAM,CAACF,KAAK,CAACF,WAAW,CAAC,CAAA;CACnC,CAAC,CAAA;AAEDO,gBAAc,CAAAC,OAAA,GAAGL,aAAa,CAAA;AACZM,wBAAA,CAAAC,GAAA,GAAGJ,WAAU;AAC/BG,wBAAA,CAAAE,MAAqB,GAAGR;;CChexB;CACA;CACA;CACA;CACA;CACA;AAqDA,OAAMS,QAAQ,GAAG;CACfC,EAAAA,oBAAoB,EAAE,CAAC;CACvBC,EAAAA,KAAK,EAAE,CAAC;CACRC,EAAAA,WAAW,EAAE,CAAC;CACdC,EAAAA,gBAAgB,EAAE,CAAC;CACnBC,EAAAA,QAAQ,EAAE,CAAC;CACXC,EAAAA,OAAO,EAAE,CAAC;CACVC,EAAAA,OAAO,EAAE,CAAC;CACVC,EAAAA,QAAQ,EAAE,CAAA;CACZ,EAAU;AAIJC,OAAAA,iBAAsC,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAClDC,MAAM,CAACF,CAAC,CAACG,WAAW,CAAC,CAACC,aAAa,CAACF,MAAM,CAACD,CAAC,CAACE,WAAW,CAAC,EAAC;;CAE5D;CACA;CACA;CACA;CACA;CACA;CACA;CACA,SAASE,WAAWA,CAClBC,KAA8B,EAC9BC,KAAa,EACbC,OAAqC,EACpB;CAAA,EAAA,IADjBA,OAAqC,KAAA,KAAA,CAAA,EAAA;KAArCA,OAAqC,GAAG,EAAE,CAAA;CAAA,GAAA;GAE1C,MAAM;KACJlC,IAAI;KACJmC,SAAS,GAAGnB,QAAQ,CAACO,OAAO;CAC5Ba,IAAAA,QAAQ,GAAGX,iBAAiB;CAC5BY,IAAAA,MAAM,GAAGC,YAAY,IACnBA,YAAY,CAACC,IAAI,CAAC,CAACb,CAAC,EAAEC,CAAC,KAAKa,gBAAgB,CAACd,CAAC,EAAEC,CAAC,EAAES,QAAQ,CAAC,CAAA;CAChE,GAAC,GAAGF,OAAO,CAAA;GACX,MAAMI,YAAY,GAAGN,KAAK,CAACS,MAAM,CAACC,mBAAmB,EAAE,EAAE,CAAC,CAAA;CAC1D,EAAA,OAAOL,MAAM,CAACC,YAAY,CAAC,CAACK,GAAG,CAACC,IAAA,IAAA;KAAA,IAAC;CAACC,MAAAA,IAAAA;CAAI,KAAC,GAAAD,IAAA,CAAA;CAAA,IAAA,OAAKC,IAAI,CAAA;IAAC,CAAA,CAAA;CAEjD,EAAA,SAASH,mBAAmBA,CAC1BI,OAAoC,EACpCD,IAAc,EACdE,KAAa,EACgB;KAC7B,MAAMC,WAAW,GAAGC,iBAAiB,CAACJ,IAAI,EAAE7C,IAAI,EAAEiC,KAAK,EAAEC,OAAO,CAAC,CAAA;KACjE,MAAM;OAACgB,IAAI;CAAEC,MAAAA,YAAY,GAAGhB,SAAAA;CAAS,KAAC,GAAGa,WAAW,CAAA;KACpD,IAAIE,IAAI,IAAIC,YAAY,EAAE;OACxBL,OAAO,CAACM,IAAI,CAAC;CAAC,QAAA,GAAGJ,WAAW;SAAEH,IAAI;CAAEE,QAAAA,KAAAA;CAAK,OAAC,CAAC,CAAA;CAC7C,KAAA;CACA,IAAA,OAAOD,OAAO,CAAA;CAChB,GAAA;CACF,CAAA;CAEAf,WAAW,CAACf,QAAQ,GAAGA,QAAQ,CAAA;;CAE/B;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,SAASiC,iBAAiBA,CACxBJ,IAAc,EACd7C,IAAoD,EACpDiC,KAAa,EACbC,OAAqC,EACxB;GACb,IAAI,CAAClC,IAAI,EAAE;CACT;KACA,MAAMqD,UAAU,GAAIR,IAA0B,CAAA;KAC9C,OAAO;CACL;CACAhB,MAAAA,WAAW,EAAEwB,UAAU;OACvBH,IAAI,EAAEI,eAAe,CAACD,UAAU,EAAEpB,KAAK,EAAEC,OAAO,CAAC;OACjDqB,QAAQ,EAAE,CAAC,CAAC;OACZJ,YAAY,EAAEjB,OAAO,CAACC,SAAAA;MACvB,CAAA;CACH,GAAA;CACA,EAAA,MAAMqB,YAAY,GAAGC,kBAAkB,CAACZ,IAAI,EAAE7C,IAAI,CAAC,CAAA;GACnD,OAAOwD,YAAY,CAACf,MAAM,CACxB,CAAAiB,KAAA,EAAAC,KAAA,EAGEC,CAAC,KACE;KAAA,IAHH;OAACV,IAAI;OAAErB,WAAW;OAAE0B,QAAQ;CAAEJ,MAAAA,YAAAA;CAAY,KAAC,GAAAO,KAAA,CAAA;KAAA,IAC3C;OAACG,SAAS;CAAEC,MAAAA,UAAAA;CAAU,KAAC,GAAAH,KAAA,CAAA;KAGvB,IAAII,OAAO,GAAGT,eAAe,CAACO,SAAS,EAAE5B,KAAK,EAAEC,OAAO,CAAC,CAAA;KACxD,IAAI8B,cAAc,GAAGnC,WAAW,CAAA;KAChC,MAAM;OAACoC,UAAU;OAAEC,UAAU;CAAE/B,MAAAA,SAAAA;CAAS,KAAC,GAAG2B,UAAU,CAAA;KACtD,IAAIC,OAAO,GAAGE,UAAU,IAAIF,OAAO,IAAI/C,QAAQ,CAACO,OAAO,EAAE;CACvDwC,MAAAA,OAAO,GAAGE,UAAU,CAAA;CACtB,KAAC,MAAM,IAAIF,OAAO,GAAGG,UAAU,EAAE;CAC/BH,MAAAA,OAAO,GAAGG,UAAU,CAAA;CACtB,KAAA;KACA,IAAIH,OAAO,GAAGb,IAAI,EAAE;CAClBA,MAAAA,IAAI,GAAGa,OAAO,CAAA;CACdR,MAAAA,QAAQ,GAAGK,CAAC,CAAA;CACZT,MAAAA,YAAY,GAAGhB,SAAS,CAAA;CACxB6B,MAAAA,cAAc,GAAGH,SAAS,CAAA;CAC5B,KAAA;KACA,OAAO;CAAChC,MAAAA,WAAW,EAAEmC,cAAc;OAAEd,IAAI;OAAEK,QAAQ;CAAEJ,MAAAA,YAAAA;MAAa,CAAA;CACpE,GAAC,EACD;CACEtB,IAAAA,WAAW,EAAGgB,IAA0B;KACxCK,IAAI,EAAElC,QAAQ,CAACQ,QAAmB;KAClC+B,QAAQ,EAAE,CAAC,CAAC;KACZJ,YAAY,EAAEjB,OAAO,CAACC,SAAAA;CACxB,GACF,CAAC,CAAA;CACH,CAAA;;CAEA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,SAASmB,eAAeA,CACtBa,UAAkB,EAClBC,YAAoB,EACpBlC,OAAqC,EAC5B;CACTiC,EAAAA,UAAU,GAAGE,yBAAyB,CAACF,UAAU,EAAEjC,OAAO,CAAC,CAAA;CAC3DkC,EAAAA,YAAY,GAAGC,yBAAyB,CAACD,YAAY,EAAElC,OAAO,CAAC,CAAA;;CAE/D;CACA,EAAA,IAAIkC,YAAY,CAACE,MAAM,GAAGH,UAAU,CAACG,MAAM,EAAE;KAC3C,OAAOtD,QAAQ,CAACQ,QAAQ,CAAA;CAC1B,GAAA;;CAEA;GACA,IAAI2C,UAAU,KAAKC,YAAY,EAAE;KAC/B,OAAOpD,QAAQ,CAACC,oBAAoB,CAAA;CACtC,GAAA;;CAEA;CACAkD,EAAAA,UAAU,GAAGA,UAAU,CAACI,WAAW,EAAE,CAAA;CACrCH,EAAAA,YAAY,GAAGA,YAAY,CAACG,WAAW,EAAE,CAAA;;CAEzC;GACA,IAAIJ,UAAU,KAAKC,YAAY,EAAE;KAC/B,OAAOpD,QAAQ,CAACE,KAAK,CAAA;CACvB,GAAA;;CAEA;CACA,EAAA,IAAIiD,UAAU,CAACK,UAAU,CAACJ,YAAY,CAAC,EAAE;KACvC,OAAOpD,QAAQ,CAACG,WAAW,CAAA;CAC7B,GAAA;;CAEA;GACA,IAAIgD,UAAU,CAACM,QAAQ,CAAE,IAAGL,YAAa,CAAA,CAAC,CAAC,EAAE;KAC3C,OAAOpD,QAAQ,CAACI,gBAAgB,CAAA;CAClC,GAAA;;CAEA;CACA,EAAA,IAAI+C,UAAU,CAACM,QAAQ,CAACL,YAAY,CAAC,EAAE;KACrC,OAAOpD,QAAQ,CAACK,QAAQ,CAAA;CAC1B,GAAC,MAAM,IAAI+C,YAAY,CAACE,MAAM,KAAK,CAAC,EAAE;CACpC;CACA;CACA;KACA,OAAOtD,QAAQ,CAACQ,QAAQ,CAAA;CAC1B,GAAA;;CAEA;GACA,IAAIkD,UAAU,CAACP,UAAU,CAAC,CAACM,QAAQ,CAACL,YAAY,CAAC,EAAE;KACjD,OAAOpD,QAAQ,CAACM,OAAO,CAAA;CACzB,GAAA;;CAEA;CACA;CACA,EAAA,OAAOqD,mBAAmB,CAACR,UAAU,EAAEC,YAAY,CAAC,CAAA;CACtD,CAAA;;CAEA;CACA;CACA;CACA;CACA;CACA;CACA,SAASM,UAAUA,CAAClE,MAAc,EAAU;GAC1C,IAAIoE,OAAO,GAAG,EAAE,CAAA;CAChB,EAAA,MAAMC,aAAa,GAAGrE,MAAM,CAACsE,KAAK,CAAC,GAAG,CAAC,CAAA;CACvCD,EAAAA,aAAa,CAACE,OAAO,CAACC,YAAY,IAAI;CACpC,IAAA,MAAMC,kBAAkB,GAAGD,YAAY,CAACF,KAAK,CAAC,GAAG,CAAC,CAAA;CAClDG,IAAAA,kBAAkB,CAACF,OAAO,CAACG,iBAAiB,IAAI;OAC9CN,OAAO,IAAIM,iBAAiB,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;CAC3C,KAAC,CAAC,CAAA;CACJ,GAAC,CAAC,CAAA;CACF,EAAA,OAAOP,OAAO,CAAA;CAChB,CAAA;;CAEA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,SAASD,mBAAmBA,CAC1BR,UAAkB,EAClBC,YAAoB,EACX;GACT,IAAIgB,wBAAwB,GAAG,CAAC,CAAA;GAChC,IAAIC,UAAU,GAAG,CAAC,CAAA;CAClB,EAAA,SAASC,qBAAqBA,CAC5BC,SAAiB,EACjB/E,MAAc,EACduC,KAAa,EACb;CACA,IAAA,KAAK,IAAIyC,CAAC,GAAGzC,KAAK,EAAE0C,CAAC,GAAGjF,MAAM,CAAC8D,MAAM,EAAEkB,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;CACjD,MAAA,MAAME,UAAU,GAAGlF,MAAM,CAACgF,CAAC,CAAC,CAAA;OAC5B,IAAIE,UAAU,KAAKH,SAAS,EAAE;CAC5BH,QAAAA,wBAAwB,IAAI,CAAC,CAAA;SAC7B,OAAOI,CAAC,GAAG,CAAC,CAAA;CACd,OAAA;CACF,KAAA;CACA,IAAA,OAAO,CAAC,CAAC,CAAA;CACX,GAAA;GACA,SAASG,UAAUA,CAACC,MAAc,EAAE;CAClC,IAAA,MAAMC,gBAAgB,GAAG,CAAC,GAAGD,MAAM,CAAA;CACnC,IAAA,MAAME,iBAAiB,GAAGV,wBAAwB,GAAGhB,YAAY,CAACE,MAAM,CAAA;KACxE,MAAMyB,OAAO,GAAG/E,QAAQ,CAACO,OAAO,GAAGuE,iBAAiB,GAAGD,gBAAgB,CAAA;CACvE,IAAA,OAAOE,OAAO,CAAA;CAChB,GAAA;CACA,EAAA,MAAMC,UAAU,GAAGV,qBAAqB,CAAClB,YAAY,CAAC,CAAC,CAAC,EAAED,UAAU,EAAE,CAAC,CAAC,CAAA;GACxE,IAAI6B,UAAU,GAAG,CAAC,EAAE;KAClB,OAAOhF,QAAQ,CAACQ,QAAQ,CAAA;CAC1B,GAAA;CACA6D,EAAAA,UAAU,GAAGW,UAAU,CAAA;CACvB,EAAA,KAAK,IAAIpC,CAAC,GAAG,CAAC,EAAEqC,CAAC,GAAG7B,YAAY,CAACE,MAAM,EAAEV,CAAC,GAAGqC,CAAC,EAAErC,CAAC,EAAE,EAAE;CACnD,IAAA,MAAM2B,SAAS,GAAGnB,YAAY,CAACR,CAAC,CAAC,CAAA;KACjCyB,UAAU,GAAGC,qBAAqB,CAACC,SAAS,EAAEpB,UAAU,EAAEkB,UAAU,CAAC,CAAA;CACrE,IAAA,MAAMa,KAAK,GAAGb,UAAU,GAAG,CAAC,CAAC,CAAA;KAC7B,IAAI,CAACa,KAAK,EAAE;OACV,OAAOlF,QAAQ,CAACQ,QAAQ,CAAA;CAC1B,KAAA;CACF,GAAA;CAEA,EAAA,MAAMoE,MAAM,GAAGP,UAAU,GAAGW,UAAU,CAAA;GACtC,OAAOL,UAAU,CAACC,MAAM,CAAC,CAAA;CAC3B,CAAA;;CAEA;CACA;CACA;CACA;CACA;CACA;CACA,SAASpD,gBAAgBA,CACvBd,CAAuB,EACvBC,CAAuB,EACvBS,QAA8B,EACtB;GACR,MAAM+D,MAAM,GAAG,CAAC,CAAC,CAAA;GACjB,MAAMC,MAAM,GAAG,CAAC,CAAA;GAChB,MAAM;CAAClD,IAAAA,IAAI,EAAEmD,KAAK;CAAE9C,IAAAA,QAAQ,EAAE+C,SAAAA;CAAS,GAAC,GAAG5E,CAAC,CAAA;GAC5C,MAAM;CAACwB,IAAAA,IAAI,EAAEqD,KAAK;CAAEhD,IAAAA,QAAQ,EAAEiD,SAAAA;CAAS,GAAC,GAAG7E,CAAC,CAAA;CAC5C,EAAA,MAAM8E,IAAI,GAAGJ,KAAK,KAAKE,KAAK,CAAA;CAC5B,EAAA,IAAIE,IAAI,EAAE;KACR,IAAIH,SAAS,KAAKE,SAAS,EAAE;CAC3B;CACA,MAAA,OAAOpE,QAAQ,CAACV,CAAC,EAAEC,CAAC,CAAC,CAAA;CACvB,KAAC,MAAM;CACL,MAAA,OAAO2E,SAAS,GAAGE,SAAS,GAAGL,MAAM,GAAGC,MAAM,CAAA;CAChD,KAAA;CACF,GAAC,MAAM;CACL,IAAA,OAAOC,KAAK,GAAGE,KAAK,GAAGJ,MAAM,GAAGC,MAAM,CAAA;CACxC,GAAA;CACF,CAAA;;CAEA;CACA;CACA;CACA;CACA;CACA;CACA,SAAS/B,yBAAyBA,CAChCpC,KAAa,EAAAyE,KAAA,EAEL;GAAA,IADR;CAACC,IAAAA,cAAAA;CAA4C,GAAC,GAAAD,KAAA,CAAA;CAE9C;CACA;CACAzE,EAAAA,KAAK,GAAI,CAAA,EAAEA,KAAM,CAAA,CAAC,CAAC;GACnB,IAAI,CAAC0E,cAAc,EAAE;CACnB1E,IAAAA,KAAK,GAAG1B,uBAAa,CAAC0B,KAAK,CAAC,CAAA;CAC9B,GAAA;CACA,EAAA,OAAOA,KAAK,CAAA;CACd,CAAA;;CAEA;CACA;CACA;CACA;CACA;CACA;CACA,SAAS2E,aAAaA,CACpB/D,IAAc,EACdgE,GAAwB,EACT;CACf,EAAA,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;KAC3BA,GAAG,GAAGA,GAAG,CAACA,GAAa,CAAA;CACzB,GAAA;CACA,EAAA,IAAI5E,KAA8C,CAAA;CAClD,EAAA,IAAI,OAAO4E,GAAG,KAAK,UAAU,EAAE;CAC7B5E,IAAAA,KAAK,GAAG4E,GAAG,CAAChE,IAAI,CAAC,CAAA;CACnB,GAAC,MAAM,IAAIA,IAAI,IAAI,IAAI,EAAE;CACvBZ,IAAAA,KAAK,GAAG,IAAI,CAAA;CACd,GAAC,MAAM,IAAIlC,MAAM,CAAC+G,cAAc,CAACC,IAAI,CAAClE,IAAI,EAAEgE,GAAG,CAAC,EAAE;CAChD5E,IAAAA,KAAK,GAAIY,IAAI,CAAuBgE,GAAG,CAAC,CAAA;IACzC,MAAM,IAAIA,GAAG,CAACpC,QAAQ,CAAC,GAAG,CAAC,EAAE;CAC5B;CACA,IAAA,OAAOuC,eAAe,CAAWH,GAAG,EAAEhE,IAAI,CAAC,CAAA;CAC7C,GAAC,MAAM;CACLZ,IAAAA,KAAK,GAAG,IAAI,CAAA;CACd,GAAA;;CAEA;GACA,IAAIA,KAAK,IAAI,IAAI,EAAE;CACjB,IAAA,OAAO,EAAE,CAAA;CACX,GAAA;CACA,EAAA,IAAIgF,KAAK,CAACC,OAAO,CAACjF,KAAK,CAAC,EAAE;CACxB,IAAA,OAAOA,KAAK,CAAA;CACd,GAAA;CACA,EAAA,OAAO,CAACL,MAAM,CAACK,KAAK,CAAC,CAAC,CAAA;CACxB,CAAA;;CAEA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,SAAS+E,eAAeA,CACtBG,IAAY,EACZtE,IAAc,EACC;CACf,EAAA,MAAM7C,IAAI,GAAGmH,IAAI,CAACrC,KAAK,CAAC,GAAG,CAAC,CAAA;CAG5B,EAAA,IAAIsC,MAAc,GAAG,CAACvE,IAAI,CAAC,CAAA;CAE3B,EAAA,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEqC,CAAC,GAAGjG,IAAI,CAACsE,MAAM,EAAEV,CAAC,GAAGqC,CAAC,EAAErC,CAAC,EAAE,EAAE;CAC3C,IAAA,MAAMyD,SAAS,GAAGrH,IAAI,CAAC4D,CAAC,CAAC,CAAA;KACzB,IAAI0D,YAAoB,GAAG,EAAE,CAAA;CAE7B,IAAA,KAAK,IAAI9B,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG2B,MAAM,CAAC9C,MAAM,EAAEkB,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;CAC7C,MAAA,MAAM+B,UAAU,GAAGH,MAAM,CAAC5B,CAAC,CAAC,CAAA;OAE5B,IAAI+B,UAAU,IAAI,IAAI,EAAE,SAAA;OAExB,IAAIxH,MAAM,CAAC+G,cAAc,CAACC,IAAI,CAACQ,UAAU,EAAEF,SAAS,CAAC,EAAE;CACrD,QAAA,MAAMG,WAAW,GAAID,UAAU,CAAuBF,SAAS,CAAC,CAAA;SAChE,IAAIG,WAAW,IAAI,IAAI,EAAE;CACvBF,UAAAA,YAAY,CAAClE,IAAI,CAACoE,WAAyC,CAAC,CAAA;CAC9D,SAAA;CACF,OAAC,MAAM,IAAIH,SAAS,KAAK,GAAG,EAAE;CAC5B;CACAC,QAAAA,YAAY,GAAGA,YAAY,CAACG,MAAM,CAACF,UAAU,CAAC,CAAA;CAChD,OAAA;CACF,KAAA;CAEAH,IAAAA,MAAM,GAAGE,YAAY,CAAA;CACvB,GAAA;GAEA,IAAIL,KAAK,CAACC,OAAO,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;CAC5B;CACA;KACA,MAAMM,MAAqB,GAAG,EAAE,CAAA;CAChC,IAAA,OAAOA,MAAM,CAACD,MAAM,CAAC,GAAIL,MAAwB,CAAC,CAAA;CACpD,GAAA;CACA;CACA;CACA,EAAA,OAAOA,MAAM,CAAA;CACf,CAAA;;CAEA;CACA;CACA;CACA;CACA;CACA;CACA,SAAS3D,kBAAkBA,CACzBZ,IAAc,EACd7C,IAAwC,EACxC;GACA,MAAM2H,SAAgE,GAAG,EAAE,CAAA;CAC3E,EAAA,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGzF,IAAI,CAACsE,MAAM,EAAEkB,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;CAC3C,IAAA,MAAMqB,GAAG,GAAG7G,IAAI,CAACwF,CAAC,CAAC,CAAA;CACnB,IAAA,MAAM1B,UAAU,GAAG8D,gBAAgB,CAACf,GAAG,CAAC,CAAA;CACxC,IAAA,MAAMgB,UAAU,GAAGjB,aAAa,CAAC/D,IAAI,EAAEgE,GAAG,CAAC,CAAA;CAC3C,IAAA,KAAK,IAAIjD,CAAC,GAAG,CAAC,EAAEqC,CAAC,GAAG4B,UAAU,CAACvD,MAAM,EAAEV,CAAC,GAAGqC,CAAC,EAAErC,CAAC,EAAE,EAAE;OACjD+D,SAAS,CAACvE,IAAI,CAAC;CACbS,QAAAA,SAAS,EAAEgE,UAAU,CAACjE,CAAC,CAAC;CACxBE,QAAAA,UAAAA;CACF,OAAC,CAAC,CAAA;CACJ,KAAA;CACF,GAAA;CACA,EAAA,OAAO6D,SAAS,CAAA;CAClB,CAAA;CAEA,MAAMG,oBAAoB,GAAG;CAC3B5D,EAAAA,UAAU,EAAE6D,QAAmB;CAC/B9D,EAAAA,UAAU,EAAE,CAAC8D,QAAAA;CACf,CAAC,CAAA;CACD;CACA;CACA;CACA;CACA;CACA,SAASH,gBAAgBA,CAAWf,GAAwB,EAAiB;CAC3E,EAAA,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;CAC3B,IAAA,OAAOiB,oBAAoB,CAAA;CAC7B,GAAA;GACA,OAAO;CAAC,IAAA,GAAGA,oBAAoB;KAAE,GAAGjB,GAAAA;IAAI,CAAA;CAC1C,CAAA;;CAaA;CACA;CACA;CACA;;;;;;;;;;;;"}