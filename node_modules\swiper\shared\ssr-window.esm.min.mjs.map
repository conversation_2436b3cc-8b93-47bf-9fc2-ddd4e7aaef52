{"version": 3, "file": "ssr-window.esm.mjs.mjs", "names": ["isObject", "obj", "constructor", "Object", "extend", "target", "src", "noExtend", "keys", "filter", "key", "indexOf", "for<PERSON>ach", "length", "ssrDocument", "body", "addEventListener", "removeEventListener", "activeElement", "blur", "nodeName", "querySelector", "querySelectorAll", "getElementById", "createEvent", "initEvent", "createElement", "children", "childNodes", "style", "setAttribute", "getElementsByTagName", "createElementNS", "importNode", "location", "hash", "host", "hostname", "href", "origin", "pathname", "protocol", "search", "getDocument", "doc", "document", "ssrWindow", "navigator", "userAgent", "history", "replaceState", "pushState", "go", "back", "CustomEvent", "this", "getComputedStyle", "getPropertyValue", "Image", "Date", "screen", "setTimeout", "clearTimeout", "matchMedia", "requestAnimationFrame", "callback", "cancelAnimationFrame", "id", "getWindow", "win", "window"], "sources": ["0"], "mappings": "AAYA,SAASA,SAASC,GAChB,OAAe,OAARA,GAA+B,iBAARA,GAAoB,gBAAiBA,GAAOA,EAAIC,cAAgBC,MAChG,CACA,SAASC,OAAOC,EAAQC,QACP,IAAXD,IACFA,EAAS,CAAC,QAEA,IAARC,IACFA,EAAM,CAAC,GAET,MAAMC,EAAW,CAAC,YAAa,cAAe,aAC9CJ,OAAOK,KAAKF,GAAKG,QAAOC,GAAOH,EAASI,QAAQD,GAAO,IAAGE,SAAQF,SACrC,IAAhBL,EAAOK,GAAsBL,EAAOK,GAAOJ,EAAII,GAAcV,SAASM,EAAII,KAASV,SAASK,EAAOK,KAASP,OAAOK,KAAKF,EAAII,IAAMG,OAAS,GACpJT,OAAOC,EAAOK,GAAMJ,EAAII,GAC1B,GAEJ,CACA,MAAMI,YAAc,CAClBC,KAAM,CAAC,EACP,gBAAAC,GAAoB,EACpB,mBAAAC,GAAuB,EACvBC,cAAe,CACb,IAAAC,GAAQ,EACRC,SAAU,IAEZC,cAAa,IACJ,KAETC,iBAAgB,IACP,GAETC,eAAc,IACL,KAETC,YAAW,KACF,CACL,SAAAC,GAAa,IAGjBC,cAAa,KACJ,CACLC,SAAU,GACVC,WAAY,GACZC,MAAO,CAAC,EACR,YAAAC,GAAgB,EAChBC,qBAAoB,IACX,KAIbC,gBAAe,KACN,CAAC,GAEVC,WAAU,IACD,KAETC,SAAU,CACRC,KAAM,GACNC,KAAM,GACNC,SAAU,GACVC,KAAM,GACNC,OAAQ,GACRC,SAAU,GACVC,SAAU,GACVC,OAAQ,KAGZ,SAASC,cACP,MAAMC,EAA0B,oBAAbC,SAA2BA,SAAW,CAAC,EAE1D,OADAzC,OAAOwC,EAAK9B,aACL8B,CACT,CACA,MAAME,UAAY,CAChBD,SAAU/B,YACViC,UAAW,CACTC,UAAW,IAEbd,SAAU,CACRC,KAAM,GACNC,KAAM,GACNC,SAAU,GACVC,KAAM,GACNC,OAAQ,GACRC,SAAU,GACVC,SAAU,GACVC,OAAQ,IAEVO,QAAS,CACP,YAAAC,GAAgB,EAChB,SAAAC,GAAa,EACb,EAAAC,GAAM,EACN,IAAAC,GAAQ,GAEVC,YAAa,WACX,OAAOC,IACT,EACA,gBAAAvC,GAAoB,EACpB,mBAAAC,GAAuB,EACvBuC,iBAAgB,KACP,CACLC,iBAAgB,IACP,KAIb,KAAAC,GAAS,EACT,IAAAC,GAAQ,EACRC,OAAQ,CAAC,EACT,UAAAC,GAAc,EACd,YAAAC,GAAgB,EAChBC,WAAU,KACD,CAAC,GAEVC,sBAAsBC,GACM,oBAAfJ,YACTI,IACO,MAEFJ,WAAWI,EAAU,GAE9B,oBAAAC,CAAqBC,GACO,oBAAfN,YAGXC,aAAaK,EACf,GAEF,SAASC,YACP,MAAMC,EAAwB,oBAAXC,OAAyBA,OAAS,CAAC,EAEtD,OADAlE,OAAOiE,EAAKvB,WACLuB,CACT,QAESD,eAAgBzB"}