.footer {
  position: fixed;
  z-index: 9;
  bottom: 23px;
  left: 0;
  right: 0;
  margin-left: auto;
  margin-right: auto;
  max-width: 500px;
  width: 100%;

  .footer__nav {
    display: flex;
    align-items: center;
    padding: 10px;
    border-radius: 26px;
    background: rgba(47, 47, 47, 0.8);
    backdrop-filter: blur(10px);
  }

  .footer__list {
    display: flex;
    align-items: center;
    list-style: none;
    gap: 8px;
    width: 100%;
  }

  .footer__item {
    width: calc((100% - 8px * 4) / 4);
  }

  .footer__link {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0.5;
    background: transparent;
    border: none;
    color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      opacity: 0.8;
    }

    &.footer__link--active {
      opacity: 1;
      transform: scale(1.15);
    }
  }

  .footer__icon {
    width: 25px;
    margin-bottom: 2px;
  }

  .footer__text {
    color: #fff;
    font-size: 10px;
    font-style: normal;
    font-weight: 400;
    line-height: 17.913px;
  }
} 