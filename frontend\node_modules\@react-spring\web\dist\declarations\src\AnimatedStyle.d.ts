import { AnimatedObject } from '@react-spring/animated';
import { Lookup } from '@react-spring/types';
/**
 * This AnimatedStyle will simplify animated components transforms by
 * interpolating all transform function passed as keys in the style object
 * including shortcuts such as x, y and z for translateX/Y/Z
 */
export declare class AnimatedStyle extends AnimatedObject {
    constructor({ x, y, z, ...style }: Lookup);
}
