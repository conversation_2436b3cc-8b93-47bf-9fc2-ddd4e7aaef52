{"name": "chalk", "version": "3.0.0", "description": "Terminal string styling done right", "license": "MIT", "repository": "chalk/chalk", "main": "source", "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava && tsd", "bench": "matcha benchmark.js"}, "files": ["source", "index.d.ts"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "devDependencies": {"ava": "^2.4.0", "coveralls": "^3.0.7", "execa": "^3.2.0", "import-fresh": "^3.1.0", "matcha": "^0.7.0", "nyc": "^14.1.1", "resolve-from": "^5.0.0", "tsd": "^0.7.4", "xo": "^0.25.3"}, "xo": {"rules": {"unicorn/prefer-string-slice": "off", "unicorn/prefer-includes": "off"}}}