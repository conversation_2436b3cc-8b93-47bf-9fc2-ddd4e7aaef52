{"version": 3, "file": "update-on-virtual-data.mjs.mjs", "names": ["extend", "paramsList", "isObject", "needsNavigation", "needsPagination", "needsScrollbar", "defaults", "getParams", "obj", "splitEvents", "params", "on", "events", "passedParams", "_emitClasses", "init", "rest", "allowedParams", "map", "key", "replace", "plainObj", "Object", "assign", "keys", "for<PERSON>ach", "indexOf", "search", "toLowerCase", "substr", "mountSwiper", "_ref", "swiperParams", "el", "nextEl", "prevEl", "paginationEl", "scrollbarEl", "swiper", "navigation", "originalParams", "pagination", "scrollbar", "getChangedParams", "oldParams", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "children<PERSON>eys", "join", "length", "filter", "newKeys", "oldKeys", "new<PERSON>ey", "<PERSON><PERSON><PERSON>", "updateOnVirtualData", "destroyed", "virtual", "enabled", "updateSlides", "updateProgress", "updateSlidesClasses", "emit", "parallax", "setTranslate"], "sources": ["0"], "mappings": "YAAcA,YAAaC,gBAAiBC,cAAeC,qBAAsBC,qBAAsBC,mBAAsB,sCAC/GC,aAAgB,wBAE9B,SAASC,UAAUC,EAAKC,QACV,IAARD,IACFA,EAAM,CAAC,QAEW,IAAhBC,IACFA,GAAc,GAEhB,MAAMC,EAAS,CACbC,GAAI,CAAC,GAEDC,EAAS,CAAC,EACVC,EAAe,CAAC,EACtBb,OAAOU,EAAQJ,UACfI,EAAOI,cAAe,EACtBJ,EAAOK,MAAO,EACd,MAAMC,EAAO,CAAC,EACRC,EAAgBhB,WAAWiB,KAAIC,GAAOA,EAAIC,QAAQ,IAAK,MACvDC,EAAWC,OAAOC,OAAO,CAAC,EAAGf,GA2BnC,OA1BAc,OAAOE,KAAKH,GAAUI,SAAQN,SACJ,IAAbX,EAAIW,KACXF,EAAcS,QAAQP,IAAQ,EAC5BjB,SAASM,EAAIW,KACfT,EAAOS,GAAO,CAAC,EACfN,EAAaM,GAAO,CAAC,EACrBnB,OAAOU,EAAOS,GAAMX,EAAIW,IACxBnB,OAAOa,EAAaM,GAAMX,EAAIW,MAE9BT,EAAOS,GAAOX,EAAIW,GAClBN,EAAaM,GAAOX,EAAIW,IAES,IAA1BA,EAAIQ,OAAO,YAAwC,mBAAbnB,EAAIW,GAC/CV,EACFG,EAAO,GAAGO,EAAI,GAAGS,gBAAgBT,EAAIU,OAAO,MAAQrB,EAAIW,GAExDT,EAAOC,GAAG,GAAGQ,EAAI,GAAGS,gBAAgBT,EAAIU,OAAO,MAAQrB,EAAIW,GAG7DH,EAAKG,GAAOX,EAAIW,GAClB,IAEF,CAAC,aAAc,aAAc,aAAaM,SAAQN,KAC5B,IAAhBT,EAAOS,KAAeT,EAAOS,GAAO,CAAC,IACrB,IAAhBT,EAAOS,WAAuBT,EAAOS,EAAI,IAExC,CACLT,SACAG,eACAG,OACAJ,SAEJ,CAEA,SAASkB,YAAYC,EAAMC,GACzB,IAAIC,GACFA,EAAEC,OACFA,EAAMC,OACNA,EAAMC,aACNA,EAAYC,YACZA,EAAWC,OACXA,GACEP,EACA5B,gBAAgB6B,IAAiBE,GAAUC,IAC7CG,EAAO5B,OAAO6B,WAAWL,OAASA,EAClCI,EAAOE,eAAeD,WAAWL,OAASA,EAC1CI,EAAO5B,OAAO6B,WAAWJ,OAASA,EAClCG,EAAOE,eAAeD,WAAWJ,OAASA,GAExC/B,gBAAgB4B,IAAiBI,IACnCE,EAAO5B,OAAO+B,WAAWR,GAAKG,EAC9BE,EAAOE,eAAeC,WAAWR,GAAKG,GAEpC/B,eAAe2B,IAAiBK,IAClCC,EAAO5B,OAAOgC,UAAUT,GAAKI,EAC7BC,EAAOE,eAAeE,UAAUT,GAAKI,GAEvCC,EAAOvB,KAAKkB,EACd,CAEA,SAASU,iBAAiBX,EAAcY,EAAWC,EAAUC,EAAaC,GACxE,MAAMvB,EAAO,GACb,IAAKoB,EAAW,OAAOpB,EACvB,MAAMwB,EAAS7B,IACTK,EAAKE,QAAQP,GAAO,GAAGK,EAAKyB,KAAK9B,EAAI,EAE3C,GAAI0B,GAAYC,EAAa,CAC3B,MAAMI,EAAkBJ,EAAY5B,IAAI6B,GAClCI,EAAeN,EAAS3B,IAAI6B,GAC9BG,EAAgBE,KAAK,MAAQD,EAAaC,KAAK,KAAKJ,EAAO,YAC3DF,EAAYO,SAAWR,EAASQ,QAAQL,EAAO,WACrD,CAwBA,OAvBoB/C,WAAWqD,QAAOnC,GAAkB,MAAXA,EAAI,KAAYD,KAAIC,GAAOA,EAAIC,QAAQ,IAAK,MAC7EK,SAAQN,IAClB,GAAIA,KAAOa,GAAgBb,KAAOyB,EAChC,GAAI1C,SAAS8B,EAAab,KAASjB,SAAS0C,EAAUzB,IAAO,CAC3D,MAAMoC,EAAUjC,OAAOE,KAAKQ,EAAab,IACnCqC,EAAUlC,OAAOE,KAAKoB,EAAUzB,IAClCoC,EAAQF,SAAWG,EAAQH,OAC7BL,EAAO7B,IAEPoC,EAAQ9B,SAAQgC,IACVzB,EAAab,GAAKsC,KAAYb,EAAUzB,GAAKsC,IAC/CT,EAAO7B,EACT,IAEFqC,EAAQ/B,SAAQiC,IACV1B,EAAab,GAAKuC,KAAYd,EAAUzB,GAAKuC,IAASV,EAAO7B,EAAI,IAG3E,MAAWa,EAAab,KAASyB,EAAUzB,IACzC6B,EAAO7B,EAEX,IAEKK,CACT,CAEA,MAAMmC,oBAAsBrB,KACrBA,GAAUA,EAAOsB,YAActB,EAAO5B,OAAOmD,SAAWvB,EAAO5B,OAAOmD,UAAYvB,EAAO5B,OAAOmD,QAAQC,UAC7GxB,EAAOyB,eACPzB,EAAO0B,iBACP1B,EAAO2B,sBACP3B,EAAO4B,KAAK,mBACR5B,EAAO6B,UAAY7B,EAAO5B,OAAOyD,UAAY7B,EAAO5B,OAAOyD,SAASL,SACtExB,EAAO6B,SAASC,eAClB,SAGOzB,sBAAuBpC,eAAgBuB,iBAAkB6B"}