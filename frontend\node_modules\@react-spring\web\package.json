{"name": "@react-spring/web", "version": "9.6.1", "main": "dist/react-spring-web.cjs.js", "module": "dist/react-spring-web.esm.js", "files": ["dist/*", "README.md", "LICENSE"], "repository": "pmndrs/react-spring", "homepage": "https://github.com/pmndrs/react-spring#readme", "keywords": ["animated", "animation", "hooks", "motion", "react", "react-native", "spring", "typescript", "velocity"], "license": "MIT", "author": "<PERSON>", "maintainers": ["<PERSON> (https://github.com/joshua<PERSON>s)"], "dependencies": {"@react-spring/animated": "~9.6.1", "@react-spring/core": "~9.6.1", "@react-spring/shared": "~9.6.1", "@react-spring/types": "~9.6.1"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0"}}