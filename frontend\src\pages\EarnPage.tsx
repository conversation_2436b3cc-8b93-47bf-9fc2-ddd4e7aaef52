import React from 'react';
import { <PERSON>, Button } from 'antd-mobile';
import { 
  GiftOutline
} from 'antd-mobile-icons';
import { formatNumber } from '../utils/formatters';
import './EarnPage.scss';

const EarnPage: React.FC = () => {
  // 模拟数据
  const earningsData = {
    totalEarned: 0.000771,
    todayEarned: 0.000123,
    referralEarnings: 0.000045,
    pendingWithdrawal: 0.000234,
  };

  const referralData = {
    totalReferrals: 1,
    activeReferrals: 1,
    referralCode: '1392053',
    rewardPerReferral: 450,
  };

  const tasks = [
    {
      id: 1,
      title: 'Complete Profile',
      description: 'Complete your profile information',
      reward: 50,
      completed: true,
    },
    {
      id: 2,
      title: 'First Mining Session',
      description: 'Start your first mining session',
      reward: 100,
      completed: true,
    },
    {
      id: 3,
      title: 'Invite 3 Friends',
      description: 'Invite 3 friends to join',
      reward: 450,
      completed: false,
      progress: 1,
      target: 3,
    },
  ];

  const handleShareReferral = () => {
    const shareText = `Cloud mining in just 1 click!\nMine real USDT — withdraw easily anytime!\nUse my referral code: ${referralData.referralCode}`;
    const shareUrl = `https://t.me/core_xbot?start=${referralData.referralCode}`;
    
    if (navigator.share) {
      navigator.share({
        title: 'CoreX MINER',
        text: shareText,
        url: shareUrl,
      });
    } else {
      // 复制到剪贴板
      navigator.clipboard.writeText(`${shareText}\n${shareUrl}`);
    }
  };

  return (
    <div className="earn-page">
      {/* 总收益卡片 */}
      <Card className="earnings-card">
        <div className="earnings__header">
          <div className="earnings-icon">💰</div>
          <h2>Total Earnings</h2>
        </div>
        <div className="earnings__amount">
          <span className="amount">{formatNumber(earningsData.totalEarned, 6)}</span>
          <span className="currency">USDT</span>
        </div>
        <div className="earnings__stats">
          <div className="stat-item">
            <span className="stat-label">Today</span>
            <span className="stat-value">{formatNumber(earningsData.todayEarned, 6)} USDT</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Pending</span>
            <span className="stat-value">{formatNumber(earningsData.pendingWithdrawal, 6)} USDT</span>
          </div>
        </div>
      </Card>

      {/* 推荐系统 */}
      <Card className="referral-card">
        <div className="referral__header">
          <div className="referral-icon">📤</div>
          <h3>Referral Program</h3>
        </div>
        
        <div className="referral__code">
          <span className="code-label">Your Referral Code</span>
          <div className="code-display">
            <span className="code">{referralData.referralCode}</span>
            <Button
              size="small"
              onClick={() => navigator.clipboard.writeText(referralData.referralCode)}
            >
              Copy
            </Button>
          </div>
        </div>

        <div className="referral__stats">
          <div className="referral-stat">
            <span className="stat-number">{referralData.totalReferrals}</span>
            <span className="stat-label">Total Referrals</span>
          </div>
          <div className="referral-stat">
            <span className="stat-number">{referralData.activeReferrals}</span>
            <span className="stat-label">Active Referrals</span>
          </div>
          <div className="referral-stat">
            <span className="stat-number">{referralData.rewardPerReferral}</span>
            <span className="stat-label">GPU per Referral</span>
          </div>
        </div>

        <Button
          className="share-button"
          color="primary"
          onClick={handleShareReferral}
        >
          📤 Share & Earn
        </Button>
      </Card>

      {/* 任务列表 */}
      <Card className="tasks-card">
        <div className="tasks__header">
          <GiftOutline className="tasks-icon" />
          <h3>Daily Tasks</h3>
        </div>
        
        <div className="tasks__list">
          {tasks.map((task) => (
            <div key={task.id} className="task-item">
              <div className="task__info">
                <h4>{task.title}</h4>
                <p>{task.description}</p>
                <div className="task__reward">
                  <GiftOutline />
                  <span>{task.reward} GPU</span>
                </div>
              </div>
              
              <div className="task__status">
                {task.completed ? (
                  <div className="task-completed">
                    <span>✓ Completed</span>
                  </div>
                ) : (
                  <div className="task-progress">
                    <div className="progress-bar">
                      <div 
                        className="progress-fill" 
                        style={{ width: `${task.progress ? (task.progress / task.target) * 100 : 0}%` }}
                      ></div>
                      <span className="progress-text">{task.progress || 0}/{task.target}</span>
                    </div>
                    <Button size="small" color="primary">
                      Claim
                    </Button>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* 收益图表 */}
      <Card className="chart-card">
        <div className="chart__header">
          <div className="chart-icon">📊</div>
          <h3>Earnings Chart</h3>
        </div>
        <div className="chart__content">
          <p>Coming soon...</p>
        </div>
      </Card>
    </div>
  );
};

export default EarnPage; 