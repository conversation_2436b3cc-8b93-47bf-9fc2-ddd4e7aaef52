// 用户相关类型
export interface User {
  id: number;
  username: string;
  email: string;
  balance: number;
  totalEarned: number;
  referralCode: string;
  referredBy?: number;
  status: 'active' | 'suspended' | 'banned';
  createdAt: string;
  updatedAt: string;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// 挖矿相关类型
export interface MiningStatus {
  isActive: boolean;
  hashRate: number;
  totalHashes: number;
  startTime?: string;
  sessionId?: number;
}

export interface MiningSession {
  id: number;
  userId: number;
  startTime: string;
  endTime?: string;
  hashRate: number;
  totalHashes: number;
  status: 'active' | 'completed' | 'stopped';
}

// 收益相关类型
export interface EarningsStats {
  totalEarned: number;
  todayEarned: number;
  thisWeekEarned: number;
  thisMonthEarned: number;
  pendingBalance: number;
}

export interface Transaction {
  id: number;
  userId: number;
  type: 'mining' | 'withdraw' | 'referral' | 'task' | 'bonus';
  amount: number;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  referenceId?: string;
  description?: string;
  createdAt: string;
}

// 任务相关类型
export interface Task {
  id: number;
  title: string;
  description: string;
  rewardAmount: number;
  taskType: 'daily' | 'weekly' | 'referral' | 'special';
  requirements: Record<string, any>;
  status: 'active' | 'inactive';
  isCompleted: boolean;
  progress: number;
  maxProgress: number;
}

export interface TaskProgress {
  taskId: number;
  userId: number;
  progress: number;
  isCompleted: boolean;
  completedAt?: string;
}

// 提现相关类型
export interface WithdrawMethod {
  id: number;
  name: string;
  code: string;
  icon: string;
  minAmount: number;
  maxAmount: number;
  fee: number;
  feeType: 'fixed' | 'percentage';
  processingTime: string;
  status: 'active' | 'inactive';
}

export interface WithdrawRequest {
  id: number;
  userId: number;
  methodId: number;
  amount: number;
  fee: number;
  netAmount: number;
  address: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  txHash?: string;
  createdAt: string;
  updatedAt: string;
}

// 推荐相关类型
export interface ReferralStats {
  totalReferrals: number;
  activeReferrals: number;
  totalEarnings: number;
  pendingEarnings: number;
  referralCode: string;
  referralLink: string;
}

export interface ReferralUser {
  id: number;
  username: string;
  status: 'active' | 'inactive';
  joinedAt: string;
  totalEarned: number;
  commissionEarned: number;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// 实时数据类型
export interface RealTimeData {
  hashRate: number;
  totalHashes: number;
  earnings: number;
  activeUsers: number;
}

// 通知类型
export interface Notification {
  id: number;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  isRead: boolean;
  createdAt: string;
}

// 设置类型
export interface UserSettings {
  language: string;
  theme: 'light' | 'dark' | 'auto';
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
  privacy: {
    showBalance: boolean;
    showEarnings: boolean;
  };
}

// 错误类型
export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

// 表单类型
export interface LoginForm {
  username: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterForm {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  referralCode?: string;
  agreeToTerms: boolean;
}

export interface WithdrawForm {
  methodId: number;
  amount: number;
  address: string;
  confirmAddress: string;
}

// 状态管理类型
export interface RootState {
  auth: AuthState;
  mining: MiningState;
  earnings: EarningsState;
  tasks: TasksState;
  withdraw: WithdrawState;
  ui: UIState;
}

export interface MiningState {
  status: MiningStatus | null;
  sessions: MiningSession[];
  isLoading: boolean;
  error: string | null;
}

export interface EarningsState {
  stats: EarningsStats | null;
  transactions: Transaction[];
  isLoading: boolean;
  error: string | null;
}

export interface TasksState {
  tasks: Task[];
  progress: Record<number, TaskProgress>;
  isLoading: boolean;
  error: string | null;
}

export interface WithdrawState {
  methods: WithdrawMethod[];
  requests: WithdrawRequest[];
  isLoading: boolean;
  error: string | null;
}

export interface UIState {
  theme: 'light' | 'dark';
  language: string;
  notifications: Notification[];
  isLoading: boolean;
  sidebarOpen: boolean;
} 