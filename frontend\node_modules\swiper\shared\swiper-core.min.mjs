import{a as getWindow,g as getDocument}from"./ssr-window.esm.min.mjs";import{b as elementParents,q as elementStyle,e as elementChildren,a as setCSSProperty,h as elementOuterSize,r as elementNextAll,t as elementPrevAll,k as getTranslate,u as animateCSSModeScroll,n as nextTick,v as showWarning,c as createElement,w as elementIsChildOf,f as now,x as extend,i as elementIndex,y as deleteProps}from"./utils.min.mjs";let support,deviceCached,browser;function calcSupport(){const e=getWindow(),t=getDocument();return{smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}}function getSupport(){return support||(support=calcSupport()),support}function calcDevice(e){let{userAgent:t}=void 0===e?{}:e;const s=getSupport(),i=getWindow(),r=i.navigator.platform,a=t||i.navigator.userAgent,n={ios:!1,android:!1},l=i.screen.width,o=i.screen.height,d=a.match(/(Android);?[\s\/]+([\d.]+)?/);let c=a.match(/(iPad).*OS\s([\d_]+)/);const p=a.match(/(iPod)(.*OS\s([\d_]+))?/),u=!c&&a.match(/(iPhone\sOS|iOS)\s([\d_]+)/),h="Win32"===r;let m="MacIntel"===r;return!c&&m&&s.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(`${l}x${o}`)>=0&&(c=a.match(/(Version)\/([\d.]+)/),c||(c=[0,1,"13_0_0"]),m=!1),d&&!h&&(n.os="android",n.android=!0),(c||u||p)&&(n.os="ios",n.ios=!0),n}function getDevice(e){return void 0===e&&(e={}),deviceCached||(deviceCached=calcDevice(e)),deviceCached}function calcBrowser(){const e=getWindow(),t=getDevice();let s=!1;function i(){const t=e.navigator.userAgent.toLowerCase();return t.indexOf("safari")>=0&&t.indexOf("chrome")<0&&t.indexOf("android")<0}if(i()){const t=String(e.navigator.userAgent);if(t.includes("Version/")){const[e,i]=t.split("Version/")[1].split(" ")[0].split(".").map((e=>Number(e)));s=e<16||16===e&&i<2}}const r=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent),a=i();return{isSafari:s||a,needPerspectiveFix:s,need3dFix:a||r&&t.ios,isWebView:r}}function getBrowser(){return browser||(browser=calcBrowser()),browser}function Resize(e){let{swiper:t,on:s,emit:i}=e;const r=getWindow();let a=null,n=null;const l=()=>{t&&!t.destroyed&&t.initialized&&(i("beforeResize"),i("resize"))},o=()=>{t&&!t.destroyed&&t.initialized&&i("orientationchange")};s("init",(()=>{t.params.resizeObserver&&void 0!==r.ResizeObserver?t&&!t.destroyed&&t.initialized&&(a=new ResizeObserver((e=>{n=r.requestAnimationFrame((()=>{const{width:s,height:i}=t;let r=s,a=i;e.forEach((e=>{let{contentBoxSize:s,contentRect:i,target:n}=e;n&&n!==t.el||(r=i?i.width:(s[0]||s).inlineSize,a=i?i.height:(s[0]||s).blockSize)})),r===s&&a===i||l()}))})),a.observe(t.el)):(r.addEventListener("resize",l),r.addEventListener("orientationchange",o))})),s("destroy",(()=>{n&&r.cancelAnimationFrame(n),a&&a.unobserve&&t.el&&(a.unobserve(t.el),a=null),r.removeEventListener("resize",l),r.removeEventListener("orientationchange",o)}))}function Observer(e){let{swiper:t,extendParams:s,on:i,emit:r}=e;const a=[],n=getWindow(),l=function(e,s){void 0===s&&(s={});const i=new(n.MutationObserver||n.WebkitMutationObserver)((e=>{if(t.__preventObserver__)return;if(1===e.length)return void r("observerUpdate",e[0]);const s=function(){r("observerUpdate",e[0])};n.requestAnimationFrame?n.requestAnimationFrame(s):n.setTimeout(s,0)}));i.observe(e,{attributes:void 0===s.attributes||s.attributes,childList:t.isElement||(void 0===s.childList||s).childList,characterData:void 0===s.characterData||s.characterData}),a.push(i)};s({observer:!1,observeParents:!1,observeSlideChildren:!1}),i("init",(()=>{if(t.params.observer){if(t.params.observeParents){const e=elementParents(t.hostEl);for(let t=0;t<e.length;t+=1)l(e[t])}l(t.hostEl,{childList:t.params.observeSlideChildren}),l(t.wrapperEl,{attributes:!1})}})),i("destroy",(()=>{a.forEach((e=>{e.disconnect()})),a.splice(0,a.length)}))}var eventsEmitter={on(e,t,s){const i=this;if(!i.eventsListeners||i.destroyed)return i;if("function"!=typeof t)return i;const r=s?"unshift":"push";return e.split(" ").forEach((e=>{i.eventsListeners[e]||(i.eventsListeners[e]=[]),i.eventsListeners[e][r](t)})),i},once(e,t,s){const i=this;if(!i.eventsListeners||i.destroyed)return i;if("function"!=typeof t)return i;function r(){i.off(e,r),r.__emitterProxy&&delete r.__emitterProxy;for(var s=arguments.length,a=new Array(s),n=0;n<s;n++)a[n]=arguments[n];t.apply(i,a)}return r.__emitterProxy=t,i.on(e,r,s)},onAny(e,t){const s=this;if(!s.eventsListeners||s.destroyed)return s;if("function"!=typeof e)return s;const i=t?"unshift":"push";return s.eventsAnyListeners.indexOf(e)<0&&s.eventsAnyListeners[i](e),s},offAny(e){const t=this;if(!t.eventsListeners||t.destroyed)return t;if(!t.eventsAnyListeners)return t;const s=t.eventsAnyListeners.indexOf(e);return s>=0&&t.eventsAnyListeners.splice(s,1),t},off(e,t){const s=this;return!s.eventsListeners||s.destroyed?s:s.eventsListeners?(e.split(" ").forEach((e=>{void 0===t?s.eventsListeners[e]=[]:s.eventsListeners[e]&&s.eventsListeners[e].forEach(((i,r)=>{(i===t||i.__emitterProxy&&i.__emitterProxy===t)&&s.eventsListeners[e].splice(r,1)}))})),s):s},emit(){const e=this;if(!e.eventsListeners||e.destroyed)return e;if(!e.eventsListeners)return e;let t,s,i;for(var r=arguments.length,a=new Array(r),n=0;n<r;n++)a[n]=arguments[n];"string"==typeof a[0]||Array.isArray(a[0])?(t=a[0],s=a.slice(1,a.length),i=e):(t=a[0].events,s=a[0].data,i=a[0].context||e),s.unshift(i);return(Array.isArray(t)?t:t.split(" ")).forEach((t=>{e.eventsAnyListeners&&e.eventsAnyListeners.length&&e.eventsAnyListeners.forEach((e=>{e.apply(i,[t,...s])})),e.eventsListeners&&e.eventsListeners[t]&&e.eventsListeners[t].forEach((e=>{e.apply(i,s)}))})),e}};function updateSize(){const e=this;let t,s;const i=e.el;t=void 0!==e.params.width&&null!==e.params.width?e.params.width:i.clientWidth,s=void 0!==e.params.height&&null!==e.params.height?e.params.height:i.clientHeight,0===t&&e.isHorizontal()||0===s&&e.isVertical()||(t=t-parseInt(elementStyle(i,"padding-left")||0,10)-parseInt(elementStyle(i,"padding-right")||0,10),s=s-parseInt(elementStyle(i,"padding-top")||0,10)-parseInt(elementStyle(i,"padding-bottom")||0,10),Number.isNaN(t)&&(t=0),Number.isNaN(s)&&(s=0),Object.assign(e,{width:t,height:s,size:e.isHorizontal()?t:s}))}function updateSlides(){const e=this;function t(t,s){return parseFloat(t.getPropertyValue(e.getDirectionLabel(s))||0)}const s=e.params,{wrapperEl:i,slidesEl:r,size:a,rtlTranslate:n,wrongRTL:l}=e,o=e.virtual&&s.virtual.enabled,d=o?e.virtual.slides.length:e.slides.length,c=elementChildren(r,`.${e.params.slideClass}, swiper-slide`),p=o?e.virtual.slides.length:c.length;let u=[];const h=[],m=[];let f=s.slidesOffsetBefore;"function"==typeof f&&(f=s.slidesOffsetBefore.call(e));let v=s.slidesOffsetAfter;"function"==typeof v&&(v=s.slidesOffsetAfter.call(e));const g=e.snapGrid.length,w=e.slidesGrid.length;let S=s.spaceBetween,T=-f,b=0,x=0;if(void 0===a)return;"string"==typeof S&&S.indexOf("%")>=0?S=parseFloat(S.replace("%",""))/100*a:"string"==typeof S&&(S=parseFloat(S)),e.virtualSize=-S,c.forEach((e=>{n?e.style.marginLeft="":e.style.marginRight="",e.style.marginBottom="",e.style.marginTop=""})),s.centeredSlides&&s.cssMode&&(setCSSProperty(i,"--swiper-centered-offset-before",""),setCSSProperty(i,"--swiper-centered-offset-after",""));const y=s.grid&&s.grid.rows>1&&e.grid;let E;y?e.grid.initSlides(c):e.grid&&e.grid.unsetSlides();const C="auto"===s.slidesPerView&&s.breakpoints&&Object.keys(s.breakpoints).filter((e=>void 0!==s.breakpoints[e].slidesPerView)).length>0;for(let i=0;i<p;i+=1){let r;if(E=0,c[i]&&(r=c[i]),y&&e.grid.updateSlide(i,r,c),!c[i]||"none"!==elementStyle(r,"display")){if("auto"===s.slidesPerView){C&&(c[i].style[e.getDirectionLabel("width")]="");const a=getComputedStyle(r),n=r.style.transform,l=r.style.webkitTransform;if(n&&(r.style.transform="none"),l&&(r.style.webkitTransform="none"),s.roundLengths)E=e.isHorizontal()?elementOuterSize(r,"width",!0):elementOuterSize(r,"height",!0);else{const e=t(a,"width"),s=t(a,"padding-left"),i=t(a,"padding-right"),n=t(a,"margin-left"),l=t(a,"margin-right"),o=a.getPropertyValue("box-sizing");if(o&&"border-box"===o)E=e+n+l;else{const{clientWidth:t,offsetWidth:a}=r;E=e+s+i+n+l+(a-t)}}n&&(r.style.transform=n),l&&(r.style.webkitTransform=l),s.roundLengths&&(E=Math.floor(E))}else E=(a-(s.slidesPerView-1)*S)/s.slidesPerView,s.roundLengths&&(E=Math.floor(E)),c[i]&&(c[i].style[e.getDirectionLabel("width")]=`${E}px`);c[i]&&(c[i].swiperSlideSize=E),m.push(E),s.centeredSlides?(T=T+E/2+b/2+S,0===b&&0!==i&&(T=T-a/2-S),0===i&&(T=T-a/2-S),Math.abs(T)<.001&&(T=0),s.roundLengths&&(T=Math.floor(T)),x%s.slidesPerGroup==0&&u.push(T),h.push(T)):(s.roundLengths&&(T=Math.floor(T)),(x-Math.min(e.params.slidesPerGroupSkip,x))%e.params.slidesPerGroup==0&&u.push(T),h.push(T),T=T+E+S),e.virtualSize+=E+S,b=E,x+=1}}if(e.virtualSize=Math.max(e.virtualSize,a)+v,n&&l&&("slide"===s.effect||"coverflow"===s.effect)&&(i.style.width=`${e.virtualSize+S}px`),s.setWrapperSize&&(i.style[e.getDirectionLabel("width")]=`${e.virtualSize+S}px`),y&&e.grid.updateWrapperSize(E,u),!s.centeredSlides){const t=[];for(let i=0;i<u.length;i+=1){let r=u[i];s.roundLengths&&(r=Math.floor(r)),u[i]<=e.virtualSize-a&&t.push(r)}u=t,Math.floor(e.virtualSize-a)-Math.floor(u[u.length-1])>1&&u.push(e.virtualSize-a)}if(o&&s.loop){const t=m[0]+S;if(s.slidesPerGroup>1){const i=Math.ceil((e.virtual.slidesBefore+e.virtual.slidesAfter)/s.slidesPerGroup),r=t*s.slidesPerGroup;for(let e=0;e<i;e+=1)u.push(u[u.length-1]+r)}for(let i=0;i<e.virtual.slidesBefore+e.virtual.slidesAfter;i+=1)1===s.slidesPerGroup&&u.push(u[u.length-1]+t),h.push(h[h.length-1]+t),e.virtualSize+=t}if(0===u.length&&(u=[0]),0!==S){const t=e.isHorizontal()&&n?"marginLeft":e.getDirectionLabel("marginRight");c.filter(((e,t)=>!(s.cssMode&&!s.loop)||t!==c.length-1)).forEach((e=>{e.style[t]=`${S}px`}))}if(s.centeredSlides&&s.centeredSlidesBounds){let e=0;m.forEach((t=>{e+=t+(S||0)})),e-=S;const t=e>a?e-a:0;u=u.map((e=>e<=0?-f:e>t?t+v:e))}if(s.centerInsufficientSlides){let e=0;m.forEach((t=>{e+=t+(S||0)})),e-=S;const t=(s.slidesOffsetBefore||0)+(s.slidesOffsetAfter||0);if(e+t<a){const s=(a-e-t)/2;u.forEach(((e,t)=>{u[t]=e-s})),h.forEach(((e,t)=>{h[t]=e+s}))}}if(Object.assign(e,{slides:c,snapGrid:u,slidesGrid:h,slidesSizesGrid:m}),s.centeredSlides&&s.cssMode&&!s.centeredSlidesBounds){setCSSProperty(i,"--swiper-centered-offset-before",-u[0]+"px"),setCSSProperty(i,"--swiper-centered-offset-after",e.size/2-m[m.length-1]/2+"px");const t=-e.snapGrid[0],s=-e.slidesGrid[0];e.snapGrid=e.snapGrid.map((e=>e+t)),e.slidesGrid=e.slidesGrid.map((e=>e+s))}if(p!==d&&e.emit("slidesLengthChange"),u.length!==g&&(e.params.watchOverflow&&e.checkOverflow(),e.emit("snapGridLengthChange")),h.length!==w&&e.emit("slidesGridLengthChange"),s.watchSlidesProgress&&e.updateSlidesOffset(),e.emit("slidesUpdated"),!(o||s.cssMode||"slide"!==s.effect&&"fade"!==s.effect)){const t=`${s.containerModifierClass}backface-hidden`,i=e.el.classList.contains(t);p<=s.maxBackfaceHiddenSlides?i||e.el.classList.add(t):i&&e.el.classList.remove(t)}}function updateAutoHeight(e){const t=this,s=[],i=t.virtual&&t.params.virtual.enabled;let r,a=0;"number"==typeof e?t.setTransition(e):!0===e&&t.setTransition(t.params.speed);const n=e=>i?t.slides[t.getSlideIndexByData(e)]:t.slides[e];if("auto"!==t.params.slidesPerView&&t.params.slidesPerView>1)if(t.params.centeredSlides)(t.visibleSlides||[]).forEach((e=>{s.push(e)}));else for(r=0;r<Math.ceil(t.params.slidesPerView);r+=1){const e=t.activeIndex+r;if(e>t.slides.length&&!i)break;s.push(n(e))}else s.push(n(t.activeIndex));for(r=0;r<s.length;r+=1)if(void 0!==s[r]){const e=s[r].offsetHeight;a=e>a?e:a}(a||0===a)&&(t.wrapperEl.style.height=`${a}px`)}function updateSlidesOffset(){const e=this,t=e.slides,s=e.isElement?e.isHorizontal()?e.wrapperEl.offsetLeft:e.wrapperEl.offsetTop:0;for(let i=0;i<t.length;i+=1)t[i].swiperSlideOffset=(e.isHorizontal()?t[i].offsetLeft:t[i].offsetTop)-s-e.cssOverflowAdjustment()}const toggleSlideClasses$1=(e,t,s)=>{t&&!e.classList.contains(s)?e.classList.add(s):!t&&e.classList.contains(s)&&e.classList.remove(s)};function updateSlidesProgress(e){void 0===e&&(e=this&&this.translate||0);const t=this,s=t.params,{slides:i,rtlTranslate:r,snapGrid:a}=t;if(0===i.length)return;void 0===i[0].swiperSlideOffset&&t.updateSlidesOffset();let n=-e;r&&(n=e),t.visibleSlidesIndexes=[],t.visibleSlides=[];let l=s.spaceBetween;"string"==typeof l&&l.indexOf("%")>=0?l=parseFloat(l.replace("%",""))/100*t.size:"string"==typeof l&&(l=parseFloat(l));for(let e=0;e<i.length;e+=1){const o=i[e];let d=o.swiperSlideOffset;s.cssMode&&s.centeredSlides&&(d-=i[0].swiperSlideOffset);const c=(n+(s.centeredSlides?t.minTranslate():0)-d)/(o.swiperSlideSize+l),p=(n-a[0]+(s.centeredSlides?t.minTranslate():0)-d)/(o.swiperSlideSize+l),u=-(n-d),h=u+t.slidesSizesGrid[e],m=u>=0&&u<=t.size-t.slidesSizesGrid[e],f=u>=0&&u<t.size-1||h>1&&h<=t.size||u<=0&&h>=t.size;f&&(t.visibleSlides.push(o),t.visibleSlidesIndexes.push(e)),toggleSlideClasses$1(o,f,s.slideVisibleClass),toggleSlideClasses$1(o,m,s.slideFullyVisibleClass),o.progress=r?-c:c,o.originalProgress=r?-p:p}}function updateProgress(e){const t=this;if(void 0===e){const s=t.rtlTranslate?-1:1;e=t&&t.translate&&t.translate*s||0}const s=t.params,i=t.maxTranslate()-t.minTranslate();let{progress:r,isBeginning:a,isEnd:n,progressLoop:l}=t;const o=a,d=n;if(0===i)r=0,a=!0,n=!0;else{r=(e-t.minTranslate())/i;const s=Math.abs(e-t.minTranslate())<1,l=Math.abs(e-t.maxTranslate())<1;a=s||r<=0,n=l||r>=1,s&&(r=0),l&&(r=1)}if(s.loop){const s=t.getSlideIndexByData(0),i=t.getSlideIndexByData(t.slides.length-1),r=t.slidesGrid[s],a=t.slidesGrid[i],n=t.slidesGrid[t.slidesGrid.length-1],o=Math.abs(e);l=o>=r?(o-r)/n:(o+n-a)/n,l>1&&(l-=1)}Object.assign(t,{progress:r,progressLoop:l,isBeginning:a,isEnd:n}),(s.watchSlidesProgress||s.centeredSlides&&s.autoHeight)&&t.updateSlidesProgress(e),a&&!o&&t.emit("reachBeginning toEdge"),n&&!d&&t.emit("reachEnd toEdge"),(o&&!a||d&&!n)&&t.emit("fromEdge"),t.emit("progress",r)}const toggleSlideClasses=(e,t,s)=>{t&&!e.classList.contains(s)?e.classList.add(s):!t&&e.classList.contains(s)&&e.classList.remove(s)};function updateSlidesClasses(){const e=this,{slides:t,params:s,slidesEl:i,activeIndex:r}=e,a=e.virtual&&s.virtual.enabled,n=e.grid&&s.grid&&s.grid.rows>1,l=e=>elementChildren(i,`.${s.slideClass}${e}, swiper-slide${e}`)[0];let o,d,c;if(a)if(s.loop){let t=r-e.virtual.slidesBefore;t<0&&(t=e.virtual.slides.length+t),t>=e.virtual.slides.length&&(t-=e.virtual.slides.length),o=l(`[data-swiper-slide-index="${t}"]`)}else o=l(`[data-swiper-slide-index="${r}"]`);else n?(o=t.find((e=>e.column===r)),c=t.find((e=>e.column===r+1)),d=t.find((e=>e.column===r-1))):o=t[r];o&&(n||(c=elementNextAll(o,`.${s.slideClass}, swiper-slide`)[0],s.loop&&!c&&(c=t[0]),d=elementPrevAll(o,`.${s.slideClass}, swiper-slide`)[0],s.loop&&0===!d&&(d=t[t.length-1]))),t.forEach((e=>{toggleSlideClasses(e,e===o,s.slideActiveClass),toggleSlideClasses(e,e===c,s.slideNextClass),toggleSlideClasses(e,e===d,s.slidePrevClass)})),e.emitSlidesClasses()}const processLazyPreloader=(e,t)=>{if(!e||e.destroyed||!e.params)return;const s=t.closest(e.isElement?"swiper-slide":`.${e.params.slideClass}`);if(s){let t=s.querySelector(`.${e.params.lazyPreloaderClass}`);!t&&e.isElement&&(s.shadowRoot?t=s.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`):requestAnimationFrame((()=>{s.shadowRoot&&(t=s.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`),t&&t.remove())}))),t&&t.remove()}},unlazy=(e,t)=>{if(!e.slides[t])return;const s=e.slides[t].querySelector('[loading="lazy"]');s&&s.removeAttribute("loading")},preload=e=>{if(!e||e.destroyed||!e.params)return;let t=e.params.lazyPreloadPrevNext;const s=e.slides.length;if(!s||!t||t<0)return;t=Math.min(t,s);const i="auto"===e.params.slidesPerView?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),r=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){const s=r,a=[s-t];return a.push(...Array.from({length:t}).map(((e,t)=>s+i+t))),void e.slides.forEach(((t,s)=>{a.includes(t.column)&&unlazy(e,s)}))}const a=r+i-1;if(e.params.rewind||e.params.loop)for(let i=r-t;i<=a+t;i+=1){const t=(i%s+s)%s;(t<r||t>a)&&unlazy(e,t)}else for(let i=Math.max(r-t,0);i<=Math.min(a+t,s-1);i+=1)i!==r&&(i>a||i<r)&&unlazy(e,i)};function getActiveIndexByTranslate(e){const{slidesGrid:t,params:s}=e,i=e.rtlTranslate?e.translate:-e.translate;let r;for(let e=0;e<t.length;e+=1)void 0!==t[e+1]?i>=t[e]&&i<t[e+1]-(t[e+1]-t[e])/2?r=e:i>=t[e]&&i<t[e+1]&&(r=e+1):i>=t[e]&&(r=e);return s.normalizeSlideIndex&&(r<0||void 0===r)&&(r=0),r}function updateActiveIndex(e){const t=this,s=t.rtlTranslate?t.translate:-t.translate,{snapGrid:i,params:r,activeIndex:a,realIndex:n,snapIndex:l}=t;let o,d=e;const c=e=>{let s=e-t.virtual.slidesBefore;return s<0&&(s=t.virtual.slides.length+s),s>=t.virtual.slides.length&&(s-=t.virtual.slides.length),s};if(void 0===d&&(d=getActiveIndexByTranslate(t)),i.indexOf(s)>=0)o=i.indexOf(s);else{const e=Math.min(r.slidesPerGroupSkip,d);o=e+Math.floor((d-e)/r.slidesPerGroup)}if(o>=i.length&&(o=i.length-1),d===a&&!t.params.loop)return void(o!==l&&(t.snapIndex=o,t.emit("snapIndexChange")));if(d===a&&t.params.loop&&t.virtual&&t.params.virtual.enabled)return void(t.realIndex=c(d));const p=t.grid&&r.grid&&r.grid.rows>1;let u;if(t.virtual&&r.virtual.enabled&&r.loop)u=c(d);else if(p){const e=t.slides.find((e=>e.column===d));let s=parseInt(e.getAttribute("data-swiper-slide-index"),10);Number.isNaN(s)&&(s=Math.max(t.slides.indexOf(e),0)),u=Math.floor(s/r.grid.rows)}else if(t.slides[d]){const e=t.slides[d].getAttribute("data-swiper-slide-index");u=e?parseInt(e,10):d}else u=d;Object.assign(t,{previousSnapIndex:l,snapIndex:o,previousRealIndex:n,realIndex:u,previousIndex:a,activeIndex:d}),t.initialized&&preload(t),t.emit("activeIndexChange"),t.emit("snapIndexChange"),(t.initialized||t.params.runCallbacksOnInit)&&(n!==u&&t.emit("realIndexChange"),t.emit("slideChange"))}function updateClickedSlide(e,t){const s=this,i=s.params;let r=e.closest(`.${i.slideClass}, swiper-slide`);!r&&s.isElement&&t&&t.length>1&&t.includes(e)&&[...t.slice(t.indexOf(e)+1,t.length)].forEach((e=>{!r&&e.matches&&e.matches(`.${i.slideClass}, swiper-slide`)&&(r=e)}));let a,n=!1;if(r)for(let e=0;e<s.slides.length;e+=1)if(s.slides[e]===r){n=!0,a=e;break}if(!r||!n)return s.clickedSlide=void 0,void(s.clickedIndex=void 0);s.clickedSlide=r,s.virtual&&s.params.virtual.enabled?s.clickedIndex=parseInt(r.getAttribute("data-swiper-slide-index"),10):s.clickedIndex=a,i.slideToClickedSlide&&void 0!==s.clickedIndex&&s.clickedIndex!==s.activeIndex&&s.slideToClickedSlide()}var update={updateSize:updateSize,updateSlides:updateSlides,updateAutoHeight:updateAutoHeight,updateSlidesOffset:updateSlidesOffset,updateSlidesProgress:updateSlidesProgress,updateProgress:updateProgress,updateSlidesClasses:updateSlidesClasses,updateActiveIndex:updateActiveIndex,updateClickedSlide:updateClickedSlide};function getSwiperTranslate(e){void 0===e&&(e=this.isHorizontal()?"x":"y");const{params:t,rtlTranslate:s,translate:i,wrapperEl:r}=this;if(t.virtualTranslate)return s?-i:i;if(t.cssMode)return i;let a=getTranslate(r,e);return a+=this.cssOverflowAdjustment(),s&&(a=-a),a||0}function setTranslate(e,t){const s=this,{rtlTranslate:i,params:r,wrapperEl:a,progress:n}=s;let l=0,o=0;let d;s.isHorizontal()?l=i?-e:e:o=e,r.roundLengths&&(l=Math.floor(l),o=Math.floor(o)),s.previousTranslate=s.translate,s.translate=s.isHorizontal()?l:o,r.cssMode?a[s.isHorizontal()?"scrollLeft":"scrollTop"]=s.isHorizontal()?-l:-o:r.virtualTranslate||(s.isHorizontal()?l-=s.cssOverflowAdjustment():o-=s.cssOverflowAdjustment(),a.style.transform=`translate3d(${l}px, ${o}px, 0px)`);const c=s.maxTranslate()-s.minTranslate();d=0===c?0:(e-s.minTranslate())/c,d!==n&&s.updateProgress(e),s.emit("setTranslate",s.translate,t)}function minTranslate(){return-this.snapGrid[0]}function maxTranslate(){return-this.snapGrid[this.snapGrid.length-1]}function translateTo(e,t,s,i,r){void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===s&&(s=!0),void 0===i&&(i=!0);const a=this,{params:n,wrapperEl:l}=a;if(a.animating&&n.preventInteractionOnTransition)return!1;const o=a.minTranslate(),d=a.maxTranslate();let c;if(c=i&&e>o?o:i&&e<d?d:e,a.updateProgress(c),n.cssMode){const e=a.isHorizontal();if(0===t)l[e?"scrollLeft":"scrollTop"]=-c;else{if(!a.support.smoothScroll)return animateCSSModeScroll({swiper:a,targetPosition:-c,side:e?"left":"top"}),!0;l.scrollTo({[e?"left":"top"]:-c,behavior:"smooth"})}return!0}return 0===t?(a.setTransition(0),a.setTranslate(c),s&&(a.emit("beforeTransitionStart",t,r),a.emit("transitionEnd"))):(a.setTransition(t),a.setTranslate(c),s&&(a.emit("beforeTransitionStart",t,r),a.emit("transitionStart")),a.animating||(a.animating=!0,a.onTranslateToWrapperTransitionEnd||(a.onTranslateToWrapperTransitionEnd=function(e){a&&!a.destroyed&&e.target===this&&(a.wrapperEl.removeEventListener("transitionend",a.onTranslateToWrapperTransitionEnd),a.onTranslateToWrapperTransitionEnd=null,delete a.onTranslateToWrapperTransitionEnd,a.animating=!1,s&&a.emit("transitionEnd"))}),a.wrapperEl.addEventListener("transitionend",a.onTranslateToWrapperTransitionEnd))),!0}var translate={getTranslate:getSwiperTranslate,setTranslate:setTranslate,minTranslate:minTranslate,maxTranslate:maxTranslate,translateTo:translateTo};function setTransition(e,t){const s=this;s.params.cssMode||(s.wrapperEl.style.transitionDuration=`${e}ms`,s.wrapperEl.style.transitionDelay=0===e?"0ms":""),s.emit("setTransition",e,t)}function transitionEmit(e){let{swiper:t,runCallbacks:s,direction:i,step:r}=e;const{activeIndex:a,previousIndex:n}=t;let l=i;l||(l=a>n?"next":a<n?"prev":"reset"),t.emit(`transition${r}`),s&&"reset"===l?t.emit(`slideResetTransition${r}`):s&&a!==n&&(t.emit(`slideChangeTransition${r}`),"next"===l?t.emit(`slideNextTransition${r}`):t.emit(`slidePrevTransition${r}`))}function transitionStart(e,t){void 0===e&&(e=!0);const s=this,{params:i}=s;i.cssMode||(i.autoHeight&&s.updateAutoHeight(),transitionEmit({swiper:s,runCallbacks:e,direction:t,step:"Start"}))}function transitionEnd(e,t){void 0===e&&(e=!0);const s=this,{params:i}=s;s.animating=!1,i.cssMode||(s.setTransition(0),transitionEmit({swiper:s,runCallbacks:e,direction:t,step:"End"}))}var transition={setTransition:setTransition,transitionStart:transitionStart,transitionEnd:transitionEnd};function slideTo(e,t,s,i,r){void 0===e&&(e=0),void 0===s&&(s=!0),"string"==typeof e&&(e=parseInt(e,10));const a=this;let n=e;n<0&&(n=0);const{params:l,snapGrid:o,slidesGrid:d,previousIndex:c,activeIndex:p,rtlTranslate:u,wrapperEl:h,enabled:m}=a;if(!m&&!i&&!r||a.destroyed||a.animating&&l.preventInteractionOnTransition)return!1;void 0===t&&(t=a.params.speed);const f=Math.min(a.params.slidesPerGroupSkip,n);let v=f+Math.floor((n-f)/a.params.slidesPerGroup);v>=o.length&&(v=o.length-1);const g=-o[v];if(l.normalizeSlideIndex)for(let e=0;e<d.length;e+=1){const t=-Math.floor(100*g),s=Math.floor(100*d[e]),i=Math.floor(100*d[e+1]);void 0!==d[e+1]?t>=s&&t<i-(i-s)/2?n=e:t>=s&&t<i&&(n=e+1):t>=s&&(n=e)}if(a.initialized&&n!==p){if(!a.allowSlideNext&&(u?g>a.translate&&g>a.minTranslate():g<a.translate&&g<a.minTranslate()))return!1;if(!a.allowSlidePrev&&g>a.translate&&g>a.maxTranslate()&&(p||0)!==n)return!1}let w;n!==(c||0)&&s&&a.emit("beforeSlideChangeStart"),a.updateProgress(g),w=n>p?"next":n<p?"prev":"reset";const S=a.virtual&&a.params.virtual.enabled;if(!(S&&r)&&(u&&-g===a.translate||!u&&g===a.translate))return a.updateActiveIndex(n),l.autoHeight&&a.updateAutoHeight(),a.updateSlidesClasses(),"slide"!==l.effect&&a.setTranslate(g),"reset"!==w&&(a.transitionStart(s,w),a.transitionEnd(s,w)),!1;if(l.cssMode){const e=a.isHorizontal(),s=u?g:-g;if(0===t)S&&(a.wrapperEl.style.scrollSnapType="none",a._immediateVirtual=!0),S&&!a._cssModeVirtualInitialSet&&a.params.initialSlide>0?(a._cssModeVirtualInitialSet=!0,requestAnimationFrame((()=>{h[e?"scrollLeft":"scrollTop"]=s}))):h[e?"scrollLeft":"scrollTop"]=s,S&&requestAnimationFrame((()=>{a.wrapperEl.style.scrollSnapType="",a._immediateVirtual=!1}));else{if(!a.support.smoothScroll)return animateCSSModeScroll({swiper:a,targetPosition:s,side:e?"left":"top"}),!0;h.scrollTo({[e?"left":"top"]:s,behavior:"smooth"})}return!0}const T=getBrowser().isSafari;return S&&!r&&T&&a.isElement&&a.virtual.update(!1,!1,n),a.setTransition(t),a.setTranslate(g),a.updateActiveIndex(n),a.updateSlidesClasses(),a.emit("beforeTransitionStart",t,i),a.transitionStart(s,w),0===t?a.transitionEnd(s,w):a.animating||(a.animating=!0,a.onSlideToWrapperTransitionEnd||(a.onSlideToWrapperTransitionEnd=function(e){a&&!a.destroyed&&e.target===this&&(a.wrapperEl.removeEventListener("transitionend",a.onSlideToWrapperTransitionEnd),a.onSlideToWrapperTransitionEnd=null,delete a.onSlideToWrapperTransitionEnd,a.transitionEnd(s,w))}),a.wrapperEl.addEventListener("transitionend",a.onSlideToWrapperTransitionEnd)),!0}function slideToLoop(e,t,s,i){if(void 0===e&&(e=0),void 0===s&&(s=!0),"string"==typeof e){e=parseInt(e,10)}const r=this;if(r.destroyed)return;void 0===t&&(t=r.params.speed);const a=r.grid&&r.params.grid&&r.params.grid.rows>1;let n=e;if(r.params.loop)if(r.virtual&&r.params.virtual.enabled)n+=r.virtual.slidesBefore;else{let e;if(a){const t=n*r.params.grid.rows;e=r.slides.find((e=>1*e.getAttribute("data-swiper-slide-index")===t)).column}else e=r.getSlideIndexByData(n);const t=a?Math.ceil(r.slides.length/r.params.grid.rows):r.slides.length,{centeredSlides:s}=r.params;let l=r.params.slidesPerView;"auto"===l?l=r.slidesPerViewDynamic():(l=Math.ceil(parseFloat(r.params.slidesPerView,10)),s&&l%2==0&&(l+=1));let o=t-e<l;if(s&&(o=o||e<Math.ceil(l/2)),i&&s&&"auto"!==r.params.slidesPerView&&!a&&(o=!1),o){const i=s?e<r.activeIndex?"prev":"next":e-r.activeIndex-1<r.params.slidesPerView?"next":"prev";r.loopFix({direction:i,slideTo:!0,activeSlideIndex:"next"===i?e+1:e-t+1,slideRealIndex:"next"===i?r.realIndex:void 0})}if(a){const e=n*r.params.grid.rows;n=r.slides.find((t=>1*t.getAttribute("data-swiper-slide-index")===e)).column}else n=r.getSlideIndexByData(n)}return requestAnimationFrame((()=>{r.slideTo(n,t,s,i)})),r}function slideNext(e,t,s){void 0===t&&(t=!0);const i=this,{enabled:r,params:a,animating:n}=i;if(!r||i.destroyed)return i;void 0===e&&(e=i.params.speed);let l=a.slidesPerGroup;"auto"===a.slidesPerView&&1===a.slidesPerGroup&&a.slidesPerGroupAuto&&(l=Math.max(i.slidesPerViewDynamic("current",!0),1));const o=i.activeIndex<a.slidesPerGroupSkip?1:l,d=i.virtual&&a.virtual.enabled;if(a.loop){if(n&&!d&&a.loopPreventsSliding)return!1;if(i.loopFix({direction:"next"}),i._clientLeft=i.wrapperEl.clientLeft,i.activeIndex===i.slides.length-1&&a.cssMode)return requestAnimationFrame((()=>{i.slideTo(i.activeIndex+o,e,t,s)})),!0}return a.rewind&&i.isEnd?i.slideTo(0,e,t,s):i.slideTo(i.activeIndex+o,e,t,s)}function slidePrev(e,t,s){void 0===t&&(t=!0);const i=this,{params:r,snapGrid:a,slidesGrid:n,rtlTranslate:l,enabled:o,animating:d}=i;if(!o||i.destroyed)return i;void 0===e&&(e=i.params.speed);const c=i.virtual&&r.virtual.enabled;if(r.loop){if(d&&!c&&r.loopPreventsSliding)return!1;i.loopFix({direction:"prev"}),i._clientLeft=i.wrapperEl.clientLeft}function p(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}const u=p(l?i.translate:-i.translate),h=a.map((e=>p(e))),m=r.freeMode&&r.freeMode.enabled;let f=a[h.indexOf(u)-1];if(void 0===f&&(r.cssMode||m)){let e;a.forEach(((t,s)=>{u>=t&&(e=s)})),void 0!==e&&(f=m?a[e]:a[e>0?e-1:e])}let v=0;if(void 0!==f&&(v=n.indexOf(f),v<0&&(v=i.activeIndex-1),"auto"===r.slidesPerView&&1===r.slidesPerGroup&&r.slidesPerGroupAuto&&(v=v-i.slidesPerViewDynamic("previous",!0)+1,v=Math.max(v,0))),r.rewind&&i.isBeginning){const r=i.params.virtual&&i.params.virtual.enabled&&i.virtual?i.virtual.slides.length-1:i.slides.length-1;return i.slideTo(r,e,t,s)}return r.loop&&0===i.activeIndex&&r.cssMode?(requestAnimationFrame((()=>{i.slideTo(v,e,t,s)})),!0):i.slideTo(v,e,t,s)}function slideReset(e,t,s){void 0===t&&(t=!0);const i=this;if(!i.destroyed)return void 0===e&&(e=i.params.speed),i.slideTo(i.activeIndex,e,t,s)}function slideToClosest(e,t,s,i){void 0===t&&(t=!0),void 0===i&&(i=.5);const r=this;if(r.destroyed)return;void 0===e&&(e=r.params.speed);let a=r.activeIndex;const n=Math.min(r.params.slidesPerGroupSkip,a),l=n+Math.floor((a-n)/r.params.slidesPerGroup),o=r.rtlTranslate?r.translate:-r.translate;if(o>=r.snapGrid[l]){const e=r.snapGrid[l];o-e>(r.snapGrid[l+1]-e)*i&&(a+=r.params.slidesPerGroup)}else{const e=r.snapGrid[l-1];o-e<=(r.snapGrid[l]-e)*i&&(a-=r.params.slidesPerGroup)}return a=Math.max(a,0),a=Math.min(a,r.slidesGrid.length-1),r.slideTo(a,e,t,s)}function slideToClickedSlide(){const e=this;if(e.destroyed)return;const{params:t,slidesEl:s}=e,i="auto"===t.slidesPerView?e.slidesPerViewDynamic():t.slidesPerView;let r,a=e.getSlideIndexWhenGrid(e.clickedIndex);const n=e.isElement?"swiper-slide":`.${t.slideClass}`,l=e.grid&&e.params.grid&&e.params.grid.rows>1;if(t.loop){if(e.animating)return;r=parseInt(e.clickedSlide.getAttribute("data-swiper-slide-index"),10),t.centeredSlides?e.slideToLoop(r):a>(l?(e.slides.length-i)/2-(e.params.grid.rows-1):e.slides.length-i)?(e.loopFix(),a=e.getSlideIndex(elementChildren(s,`${n}[data-swiper-slide-index="${r}"]`)[0]),nextTick((()=>{e.slideTo(a)}))):e.slideTo(a)}else e.slideTo(a)}var slide={slideTo:slideTo,slideToLoop:slideToLoop,slideNext:slideNext,slidePrev:slidePrev,slideReset:slideReset,slideToClosest:slideToClosest,slideToClickedSlide:slideToClickedSlide};function loopCreate(e,t){const s=this,{params:i,slidesEl:r}=s;if(!i.loop||s.virtual&&s.params.virtual.enabled)return;const a=()=>{elementChildren(r,`.${i.slideClass}, swiper-slide`).forEach(((e,t)=>{e.setAttribute("data-swiper-slide-index",t)}))},n=s.grid&&i.grid&&i.grid.rows>1;i.loopAddBlankSlides&&(i.slidesPerGroup>1||n)&&(()=>{const e=elementChildren(r,`.${i.slideBlankClass}`);e.forEach((e=>{e.remove()})),e.length>0&&(s.recalcSlides(),s.updateSlides())})();const l=i.slidesPerGroup*(n?i.grid.rows:1),o=s.slides.length%l!=0,d=n&&s.slides.length%i.grid.rows!=0,c=e=>{for(let t=0;t<e;t+=1){const e=s.isElement?createElement("swiper-slide",[i.slideBlankClass]):createElement("div",[i.slideClass,i.slideBlankClass]);s.slidesEl.append(e)}};if(o){if(i.loopAddBlankSlides){c(l-s.slides.length%l),s.recalcSlides(),s.updateSlides()}else showWarning("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");a()}else if(d){if(i.loopAddBlankSlides){c(i.grid.rows-s.slides.length%i.grid.rows),s.recalcSlides(),s.updateSlides()}else showWarning("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");a()}else a();s.loopFix({slideRealIndex:e,direction:i.centeredSlides?void 0:"next",initial:t})}function loopFix(e){let{slideRealIndex:t,slideTo:s=!0,direction:i,setTranslate:r,activeSlideIndex:a,initial:n,byController:l,byMousewheel:o}=void 0===e?{}:e;const d=this;if(!d.params.loop)return;d.emit("beforeLoopFix");const{slides:c,allowSlidePrev:p,allowSlideNext:u,slidesEl:h,params:m}=d,{centeredSlides:f,initialSlide:v}=m;if(d.allowSlidePrev=!0,d.allowSlideNext=!0,d.virtual&&m.virtual.enabled)return s&&(m.centeredSlides||0!==d.snapIndex?m.centeredSlides&&d.snapIndex<m.slidesPerView?d.slideTo(d.virtual.slides.length+d.snapIndex,0,!1,!0):d.snapIndex===d.snapGrid.length-1&&d.slideTo(d.virtual.slidesBefore,0,!1,!0):d.slideTo(d.virtual.slides.length,0,!1,!0)),d.allowSlidePrev=p,d.allowSlideNext=u,void d.emit("loopFix");let g=m.slidesPerView;"auto"===g?g=d.slidesPerViewDynamic():(g=Math.ceil(parseFloat(m.slidesPerView,10)),f&&g%2==0&&(g+=1));const w=m.slidesPerGroupAuto?g:m.slidesPerGroup;let S=f?Math.max(w,Math.ceil(g/2)):w;S%w!=0&&(S+=w-S%w),S+=m.loopAdditionalSlides,d.loopedSlides=S;const T=d.grid&&m.grid&&m.grid.rows>1;c.length<g+S||"cards"===d.params.effect&&c.length<g+2*S?showWarning("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):T&&"row"===m.grid.fill&&showWarning("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");const b=[],x=[],y=T?Math.ceil(c.length/m.grid.rows):c.length,E=n&&y-v<g&&!f;let C=E?v:d.activeIndex;void 0===a?a=d.getSlideIndex(c.find((e=>e.classList.contains(m.slideActiveClass)))):C=a;const M="next"===i||!i,P="prev"===i||!i;let k=0,I=0;const L=(T?c[a].column:a)+(f&&void 0===r?-g/2+.5:0);if(L<S){k=Math.max(S-L,w);for(let e=0;e<S-L;e+=1){const t=e-Math.floor(e/y)*y;if(T){const e=y-t-1;for(let t=c.length-1;t>=0;t-=1)c[t].column===e&&b.push(t)}else b.push(y-t-1)}}else if(L+g>y-S){I=Math.max(L-(y-2*S),w),E&&(I=Math.max(I,g-y+v+1));for(let e=0;e<I;e+=1){const t=e-Math.floor(e/y)*y;T?c.forEach(((e,s)=>{e.column===t&&x.push(s)})):x.push(t)}}if(d.__preventObserver__=!0,requestAnimationFrame((()=>{d.__preventObserver__=!1})),"cards"===d.params.effect&&c.length<g+2*S&&(x.includes(a)&&x.splice(x.indexOf(a),1),b.includes(a)&&b.splice(b.indexOf(a),1)),P&&b.forEach((e=>{c[e].swiperLoopMoveDOM=!0,h.prepend(c[e]),c[e].swiperLoopMoveDOM=!1})),M&&x.forEach((e=>{c[e].swiperLoopMoveDOM=!0,h.append(c[e]),c[e].swiperLoopMoveDOM=!1})),d.recalcSlides(),"auto"===m.slidesPerView?d.updateSlides():T&&(b.length>0&&P||x.length>0&&M)&&d.slides.forEach(((e,t)=>{d.grid.updateSlide(t,e,d.slides)})),m.watchSlidesProgress&&d.updateSlidesOffset(),s)if(b.length>0&&P){if(void 0===t){const e=d.slidesGrid[C],t=d.slidesGrid[C+k]-e;o?d.setTranslate(d.translate-t):(d.slideTo(C+Math.ceil(k),0,!1,!0),r&&(d.touchEventsData.startTranslate=d.touchEventsData.startTranslate-t,d.touchEventsData.currentTranslate=d.touchEventsData.currentTranslate-t))}else if(r){const e=T?b.length/m.grid.rows:b.length;d.slideTo(d.activeIndex+e,0,!1,!0),d.touchEventsData.currentTranslate=d.translate}}else if(x.length>0&&M)if(void 0===t){const e=d.slidesGrid[C],t=d.slidesGrid[C-I]-e;o?d.setTranslate(d.translate-t):(d.slideTo(C-I,0,!1,!0),r&&(d.touchEventsData.startTranslate=d.touchEventsData.startTranslate-t,d.touchEventsData.currentTranslate=d.touchEventsData.currentTranslate-t))}else{const e=T?x.length/m.grid.rows:x.length;d.slideTo(d.activeIndex-e,0,!1,!0)}if(d.allowSlidePrev=p,d.allowSlideNext=u,d.controller&&d.controller.control&&!l){const e={slideRealIndex:t,direction:i,setTranslate:r,activeSlideIndex:a,byController:!0};Array.isArray(d.controller.control)?d.controller.control.forEach((t=>{!t.destroyed&&t.params.loop&&t.loopFix({...e,slideTo:t.params.slidesPerView===m.slidesPerView&&s})})):d.controller.control instanceof d.constructor&&d.controller.control.params.loop&&d.controller.control.loopFix({...e,slideTo:d.controller.control.params.slidesPerView===m.slidesPerView&&s})}d.emit("loopFix")}function loopDestroy(){const e=this,{params:t,slidesEl:s}=e;if(!t.loop||!s||e.virtual&&e.params.virtual.enabled)return;e.recalcSlides();const i=[];e.slides.forEach((e=>{const t=void 0===e.swiperSlideIndex?1*e.getAttribute("data-swiper-slide-index"):e.swiperSlideIndex;i[t]=e})),e.slides.forEach((e=>{e.removeAttribute("data-swiper-slide-index")})),i.forEach((e=>{s.append(e)})),e.recalcSlides(),e.slideTo(e.realIndex,0)}var loop={loopCreate:loopCreate,loopFix:loopFix,loopDestroy:loopDestroy};function setGrabCursor(e){const t=this;if(!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;const s="container"===t.params.touchEventsTarget?t.el:t.wrapperEl;t.isElement&&(t.__preventObserver__=!0),s.style.cursor="move",s.style.cursor=e?"grabbing":"grab",t.isElement&&requestAnimationFrame((()=>{t.__preventObserver__=!1}))}function unsetGrabCursor(){const e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame((()=>{e.__preventObserver__=!1})))}var grabCursor={setGrabCursor:setGrabCursor,unsetGrabCursor:unsetGrabCursor};function closestElement(e,t){return void 0===t&&(t=this),function t(s){if(!s||s===getDocument()||s===getWindow())return null;s.assignedSlot&&(s=s.assignedSlot);const i=s.closest(e);return i||s.getRootNode?i||t(s.getRootNode().host):null}(t)}function preventEdgeSwipe(e,t,s){const i=getWindow(),{params:r}=e,a=r.edgeSwipeDetection,n=r.edgeSwipeThreshold;return!a||!(s<=n||s>=i.innerWidth-n)||"prevent"===a&&(t.preventDefault(),!0)}function onTouchStart(e){const t=this,s=getDocument();let i=e;i.originalEvent&&(i=i.originalEvent);const r=t.touchEventsData;if("pointerdown"===i.type){if(null!==r.pointerId&&r.pointerId!==i.pointerId)return;r.pointerId=i.pointerId}else"touchstart"===i.type&&1===i.targetTouches.length&&(r.touchId=i.targetTouches[0].identifier);if("touchstart"===i.type)return void preventEdgeSwipe(t,i,i.targetTouches[0].pageX);const{params:a,touches:n,enabled:l}=t;if(!l)return;if(!a.simulateTouch&&"mouse"===i.pointerType)return;if(t.animating&&a.preventInteractionOnTransition)return;!t.animating&&a.cssMode&&a.loop&&t.loopFix();let o=i.target;if("wrapper"===a.touchEventsTarget&&!elementIsChildOf(o,t.wrapperEl))return;if("which"in i&&3===i.which)return;if("button"in i&&i.button>0)return;if(r.isTouched&&r.isMoved)return;const d=!!a.noSwipingClass&&""!==a.noSwipingClass,c=i.composedPath?i.composedPath():i.path;d&&i.target&&i.target.shadowRoot&&c&&(o=c[0]);const p=a.noSwipingSelector?a.noSwipingSelector:`.${a.noSwipingClass}`,u=!(!i.target||!i.target.shadowRoot);if(a.noSwiping&&(u?closestElement(p,o):o.closest(p)))return void(t.allowClick=!0);if(a.swipeHandler&&!o.closest(a.swipeHandler))return;n.currentX=i.pageX,n.currentY=i.pageY;const h=n.currentX,m=n.currentY;if(!preventEdgeSwipe(t,i,h))return;Object.assign(r,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),n.startX=h,n.startY=m,r.touchStartTime=now(),t.allowClick=!0,t.updateSize(),t.swipeDirection=void 0,a.threshold>0&&(r.allowThresholdMove=!1);let f=!0;o.matches(r.focusableElements)&&(f=!1,"SELECT"===o.nodeName&&(r.isTouched=!1)),s.activeElement&&s.activeElement.matches(r.focusableElements)&&s.activeElement!==o&&("mouse"===i.pointerType||"mouse"!==i.pointerType&&!o.matches(r.focusableElements))&&s.activeElement.blur();const v=f&&t.allowTouchMove&&a.touchStartPreventDefault;!a.touchStartForcePreventDefault&&!v||o.isContentEditable||i.preventDefault(),a.freeMode&&a.freeMode.enabled&&t.freeMode&&t.animating&&!a.cssMode&&t.freeMode.onTouchStart(),t.emit("touchStart",i)}function onTouchMove(e){const t=getDocument(),s=this,i=s.touchEventsData,{params:r,touches:a,rtlTranslate:n,enabled:l}=s;if(!l)return;if(!r.simulateTouch&&"mouse"===e.pointerType)return;let o,d=e;if(d.originalEvent&&(d=d.originalEvent),"pointermove"===d.type){if(null!==i.touchId)return;if(d.pointerId!==i.pointerId)return}if("touchmove"===d.type){if(o=[...d.changedTouches].find((e=>e.identifier===i.touchId)),!o||o.identifier!==i.touchId)return}else o=d;if(!i.isTouched)return void(i.startMoving&&i.isScrolling&&s.emit("touchMoveOpposite",d));const c=o.pageX,p=o.pageY;if(d.preventedByNestedSwiper)return a.startX=c,void(a.startY=p);if(!s.allowTouchMove)return d.target.matches(i.focusableElements)||(s.allowClick=!1),void(i.isTouched&&(Object.assign(a,{startX:c,startY:p,currentX:c,currentY:p}),i.touchStartTime=now()));if(r.touchReleaseOnEdges&&!r.loop)if(s.isVertical()){if(p<a.startY&&s.translate<=s.maxTranslate()||p>a.startY&&s.translate>=s.minTranslate())return i.isTouched=!1,void(i.isMoved=!1)}else{if(n&&(c>a.startX&&-s.translate<=s.maxTranslate()||c<a.startX&&-s.translate>=s.minTranslate()))return;if(!n&&(c<a.startX&&s.translate<=s.maxTranslate()||c>a.startX&&s.translate>=s.minTranslate()))return}if(t.activeElement&&t.activeElement.matches(i.focusableElements)&&t.activeElement!==d.target&&"mouse"!==d.pointerType&&t.activeElement.blur(),t.activeElement&&d.target===t.activeElement&&d.target.matches(i.focusableElements))return i.isMoved=!0,void(s.allowClick=!1);i.allowTouchCallbacks&&s.emit("touchMove",d),a.previousX=a.currentX,a.previousY=a.currentY,a.currentX=c,a.currentY=p;const u=a.currentX-a.startX,h=a.currentY-a.startY;if(s.params.threshold&&Math.sqrt(u**2+h**2)<s.params.threshold)return;if(void 0===i.isScrolling){let e;s.isHorizontal()&&a.currentY===a.startY||s.isVertical()&&a.currentX===a.startX?i.isScrolling=!1:u*u+h*h>=25&&(e=180*Math.atan2(Math.abs(h),Math.abs(u))/Math.PI,i.isScrolling=s.isHorizontal()?e>r.touchAngle:90-e>r.touchAngle)}if(i.isScrolling&&s.emit("touchMoveOpposite",d),void 0===i.startMoving&&(a.currentX===a.startX&&a.currentY===a.startY||(i.startMoving=!0)),i.isScrolling||"touchmove"===d.type&&i.preventTouchMoveFromPointerMove)return void(i.isTouched=!1);if(!i.startMoving)return;s.allowClick=!1,!r.cssMode&&d.cancelable&&d.preventDefault(),r.touchMoveStopPropagation&&!r.nested&&d.stopPropagation();let m=s.isHorizontal()?u:h,f=s.isHorizontal()?a.currentX-a.previousX:a.currentY-a.previousY;r.oneWayMovement&&(m=Math.abs(m)*(n?1:-1),f=Math.abs(f)*(n?1:-1)),a.diff=m,m*=r.touchRatio,n&&(m=-m,f=-f);const v=s.touchesDirection;s.swipeDirection=m>0?"prev":"next",s.touchesDirection=f>0?"prev":"next";const g=s.params.loop&&!r.cssMode,w="next"===s.touchesDirection&&s.allowSlideNext||"prev"===s.touchesDirection&&s.allowSlidePrev;if(!i.isMoved){if(g&&w&&s.loopFix({direction:s.swipeDirection}),i.startTranslate=s.getTranslate(),s.setTransition(0),s.animating){const e=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});s.wrapperEl.dispatchEvent(e)}i.allowMomentumBounce=!1,!r.grabCursor||!0!==s.allowSlideNext&&!0!==s.allowSlidePrev||s.setGrabCursor(!0),s.emit("sliderFirstMove",d)}if((new Date).getTime(),!1!==r._loopSwapReset&&i.isMoved&&i.allowThresholdMove&&v!==s.touchesDirection&&g&&w&&Math.abs(m)>=1)return Object.assign(a,{startX:c,startY:p,currentX:c,currentY:p,startTranslate:i.currentTranslate}),i.loopSwapReset=!0,void(i.startTranslate=i.currentTranslate);s.emit("sliderMove",d),i.isMoved=!0,i.currentTranslate=m+i.startTranslate;let S=!0,T=r.resistanceRatio;if(r.touchReleaseOnEdges&&(T=0),m>0?(g&&w&&i.allowThresholdMove&&i.currentTranslate>(r.centeredSlides?s.minTranslate()-s.slidesSizesGrid[s.activeIndex+1]-("auto"!==r.slidesPerView&&s.slides.length-r.slidesPerView>=2?s.slidesSizesGrid[s.activeIndex+1]+s.params.spaceBetween:0)-s.params.spaceBetween:s.minTranslate())&&s.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),i.currentTranslate>s.minTranslate()&&(S=!1,r.resistance&&(i.currentTranslate=s.minTranslate()-1+(-s.minTranslate()+i.startTranslate+m)**T))):m<0&&(g&&w&&i.allowThresholdMove&&i.currentTranslate<(r.centeredSlides?s.maxTranslate()+s.slidesSizesGrid[s.slidesSizesGrid.length-1]+s.params.spaceBetween+("auto"!==r.slidesPerView&&s.slides.length-r.slidesPerView>=2?s.slidesSizesGrid[s.slidesSizesGrid.length-1]+s.params.spaceBetween:0):s.maxTranslate())&&s.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:s.slides.length-("auto"===r.slidesPerView?s.slidesPerViewDynamic():Math.ceil(parseFloat(r.slidesPerView,10)))}),i.currentTranslate<s.maxTranslate()&&(S=!1,r.resistance&&(i.currentTranslate=s.maxTranslate()+1-(s.maxTranslate()-i.startTranslate-m)**T))),S&&(d.preventedByNestedSwiper=!0),!s.allowSlideNext&&"next"===s.swipeDirection&&i.currentTranslate<i.startTranslate&&(i.currentTranslate=i.startTranslate),!s.allowSlidePrev&&"prev"===s.swipeDirection&&i.currentTranslate>i.startTranslate&&(i.currentTranslate=i.startTranslate),s.allowSlidePrev||s.allowSlideNext||(i.currentTranslate=i.startTranslate),r.threshold>0){if(!(Math.abs(m)>r.threshold||i.allowThresholdMove))return void(i.currentTranslate=i.startTranslate);if(!i.allowThresholdMove)return i.allowThresholdMove=!0,a.startX=a.currentX,a.startY=a.currentY,i.currentTranslate=i.startTranslate,void(a.diff=s.isHorizontal()?a.currentX-a.startX:a.currentY-a.startY)}r.followFinger&&!r.cssMode&&((r.freeMode&&r.freeMode.enabled&&s.freeMode||r.watchSlidesProgress)&&(s.updateActiveIndex(),s.updateSlidesClasses()),r.freeMode&&r.freeMode.enabled&&s.freeMode&&s.freeMode.onTouchMove(),s.updateProgress(i.currentTranslate),s.setTranslate(i.currentTranslate))}function onTouchEnd(e){const t=this,s=t.touchEventsData;let i,r=e;r.originalEvent&&(r=r.originalEvent);if("touchend"===r.type||"touchcancel"===r.type){if(i=[...r.changedTouches].find((e=>e.identifier===s.touchId)),!i||i.identifier!==s.touchId)return}else{if(null!==s.touchId)return;if(r.pointerId!==s.pointerId)return;i=r}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(r.type)){if(!(["pointercancel","contextmenu"].includes(r.type)&&(t.browser.isSafari||t.browser.isWebView)))return}s.pointerId=null,s.touchId=null;const{params:a,touches:n,rtlTranslate:l,slidesGrid:o,enabled:d}=t;if(!d)return;if(!a.simulateTouch&&"mouse"===r.pointerType)return;if(s.allowTouchCallbacks&&t.emit("touchEnd",r),s.allowTouchCallbacks=!1,!s.isTouched)return s.isMoved&&a.grabCursor&&t.setGrabCursor(!1),s.isMoved=!1,void(s.startMoving=!1);a.grabCursor&&s.isMoved&&s.isTouched&&(!0===t.allowSlideNext||!0===t.allowSlidePrev)&&t.setGrabCursor(!1);const c=now(),p=c-s.touchStartTime;if(t.allowClick){const e=r.path||r.composedPath&&r.composedPath();t.updateClickedSlide(e&&e[0]||r.target,e),t.emit("tap click",r),p<300&&c-s.lastClickTime<300&&t.emit("doubleTap doubleClick",r)}if(s.lastClickTime=now(),nextTick((()=>{t.destroyed||(t.allowClick=!0)})),!s.isTouched||!s.isMoved||!t.swipeDirection||0===n.diff&&!s.loopSwapReset||s.currentTranslate===s.startTranslate&&!s.loopSwapReset)return s.isTouched=!1,s.isMoved=!1,void(s.startMoving=!1);let u;if(s.isTouched=!1,s.isMoved=!1,s.startMoving=!1,u=a.followFinger?l?t.translate:-t.translate:-s.currentTranslate,a.cssMode)return;if(a.freeMode&&a.freeMode.enabled)return void t.freeMode.onTouchEnd({currentPos:u});const h=u>=-t.maxTranslate()&&!t.params.loop;let m=0,f=t.slidesSizesGrid[0];for(let e=0;e<o.length;e+=e<a.slidesPerGroupSkip?1:a.slidesPerGroup){const t=e<a.slidesPerGroupSkip-1?1:a.slidesPerGroup;void 0!==o[e+t]?(h||u>=o[e]&&u<o[e+t])&&(m=e,f=o[e+t]-o[e]):(h||u>=o[e])&&(m=e,f=o[o.length-1]-o[o.length-2])}let v=null,g=null;a.rewind&&(t.isBeginning?g=a.virtual&&a.virtual.enabled&&t.virtual?t.virtual.slides.length-1:t.slides.length-1:t.isEnd&&(v=0));const w=(u-o[m])/f,S=m<a.slidesPerGroupSkip-1?1:a.slidesPerGroup;if(p>a.longSwipesMs){if(!a.longSwipes)return void t.slideTo(t.activeIndex);"next"===t.swipeDirection&&(w>=a.longSwipesRatio?t.slideTo(a.rewind&&t.isEnd?v:m+S):t.slideTo(m)),"prev"===t.swipeDirection&&(w>1-a.longSwipesRatio?t.slideTo(m+S):null!==g&&w<0&&Math.abs(w)>a.longSwipesRatio?t.slideTo(g):t.slideTo(m))}else{if(!a.shortSwipes)return void t.slideTo(t.activeIndex);t.navigation&&(r.target===t.navigation.nextEl||r.target===t.navigation.prevEl)?r.target===t.navigation.nextEl?t.slideTo(m+S):t.slideTo(m):("next"===t.swipeDirection&&t.slideTo(null!==v?v:m+S),"prev"===t.swipeDirection&&t.slideTo(null!==g?g:m))}}function onResize(){const e=this,{params:t,el:s}=e;if(s&&0===s.offsetWidth)return;t.breakpoints&&e.setBreakpoint();const{allowSlideNext:i,allowSlidePrev:r,snapGrid:a}=e,n=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();const l=n&&t.loop;!("auto"===t.slidesPerView||t.slidesPerView>1)||!e.isEnd||e.isBeginning||e.params.centeredSlides||l?e.params.loop&&!n?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0):e.slideTo(e.slides.length-1,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout((()=>{e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()}),500)),e.allowSlidePrev=r,e.allowSlideNext=i,e.params.watchOverflow&&a!==e.snapGrid&&e.checkOverflow()}function onClick(e){const t=this;t.enabled&&(t.allowClick||(t.params.preventClicks&&e.preventDefault(),t.params.preventClicksPropagation&&t.animating&&(e.stopPropagation(),e.stopImmediatePropagation())))}function onScroll(){const e=this,{wrapperEl:t,rtlTranslate:s,enabled:i}=e;if(!i)return;let r;e.previousTranslate=e.translate,e.isHorizontal()?e.translate=-t.scrollLeft:e.translate=-t.scrollTop,0===e.translate&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses();const a=e.maxTranslate()-e.minTranslate();r=0===a?0:(e.translate-e.minTranslate())/a,r!==e.progress&&e.updateProgress(s?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1)}function onLoad(e){const t=this;processLazyPreloader(t,e.target),t.params.cssMode||"auto"!==t.params.slidesPerView&&!t.params.autoHeight||t.update()}function onDocumentTouchStart(){const e=this;e.documentTouchHandlerProceeded||(e.documentTouchHandlerProceeded=!0,e.params.touchReleaseOnEdges&&(e.el.style.touchAction="auto"))}const events=(e,t)=>{const s=getDocument(),{params:i,el:r,wrapperEl:a,device:n}=e,l=!!i.nested,o="on"===t?"addEventListener":"removeEventListener",d=t;r&&"string"!=typeof r&&(s[o]("touchstart",e.onDocumentTouchStart,{passive:!1,capture:l}),r[o]("touchstart",e.onTouchStart,{passive:!1}),r[o]("pointerdown",e.onTouchStart,{passive:!1}),s[o]("touchmove",e.onTouchMove,{passive:!1,capture:l}),s[o]("pointermove",e.onTouchMove,{passive:!1,capture:l}),s[o]("touchend",e.onTouchEnd,{passive:!0}),s[o]("pointerup",e.onTouchEnd,{passive:!0}),s[o]("pointercancel",e.onTouchEnd,{passive:!0}),s[o]("touchcancel",e.onTouchEnd,{passive:!0}),s[o]("pointerout",e.onTouchEnd,{passive:!0}),s[o]("pointerleave",e.onTouchEnd,{passive:!0}),s[o]("contextmenu",e.onTouchEnd,{passive:!0}),(i.preventClicks||i.preventClicksPropagation)&&r[o]("click",e.onClick,!0),i.cssMode&&a[o]("scroll",e.onScroll),i.updateOnWindowResize?e[d](n.ios||n.android?"resize orientationchange observerUpdate":"resize observerUpdate",onResize,!0):e[d]("observerUpdate",onResize,!0),r[o]("load",e.onLoad,{capture:!0}))};function attachEvents(){const e=this,{params:t}=e;e.onTouchStart=onTouchStart.bind(e),e.onTouchMove=onTouchMove.bind(e),e.onTouchEnd=onTouchEnd.bind(e),e.onDocumentTouchStart=onDocumentTouchStart.bind(e),t.cssMode&&(e.onScroll=onScroll.bind(e)),e.onClick=onClick.bind(e),e.onLoad=onLoad.bind(e),events(e,"on")}function detachEvents(){events(this,"off")}var events$1={attachEvents:attachEvents,detachEvents:detachEvents};const isGridEnabled=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;function setBreakpoint(){const e=this,{realIndex:t,initialized:s,params:i,el:r}=e,a=i.breakpoints;if(!a||a&&0===Object.keys(a).length)return;const n=getDocument(),l="window"!==i.breakpointsBase&&i.breakpointsBase?"container":i.breakpointsBase,o=["window","container"].includes(i.breakpointsBase)||!i.breakpointsBase?e.el:n.querySelector(i.breakpointsBase),d=e.getBreakpoint(a,l,o);if(!d||e.currentBreakpoint===d)return;const c=(d in a?a[d]:void 0)||e.originalParams,p=isGridEnabled(e,i),u=isGridEnabled(e,c),h=e.params.grabCursor,m=c.grabCursor,f=i.enabled;p&&!u?(r.classList.remove(`${i.containerModifierClass}grid`,`${i.containerModifierClass}grid-column`),e.emitContainerClasses()):!p&&u&&(r.classList.add(`${i.containerModifierClass}grid`),(c.grid.fill&&"column"===c.grid.fill||!c.grid.fill&&"column"===i.grid.fill)&&r.classList.add(`${i.containerModifierClass}grid-column`),e.emitContainerClasses()),h&&!m?e.unsetGrabCursor():!h&&m&&e.setGrabCursor(),["navigation","pagination","scrollbar"].forEach((t=>{if(void 0===c[t])return;const s=i[t]&&i[t].enabled,r=c[t]&&c[t].enabled;s&&!r&&e[t].disable(),!s&&r&&e[t].enable()}));const v=c.direction&&c.direction!==i.direction,g=i.loop&&(c.slidesPerView!==i.slidesPerView||v),w=i.loop;v&&s&&e.changeDirection(),extend(e.params,c);const S=e.params.enabled,T=e.params.loop;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),f&&!S?e.disable():!f&&S&&e.enable(),e.currentBreakpoint=d,e.emit("_beforeBreakpoint",c),s&&(g?(e.loopDestroy(),e.loopCreate(t),e.updateSlides()):!w&&T?(e.loopCreate(t),e.updateSlides()):w&&!T&&e.loopDestroy()),e.emit("breakpoint",c)}function getBreakpoint(e,t,s){if(void 0===t&&(t="window"),!e||"container"===t&&!s)return;let i=!1;const r=getWindow(),a="window"===t?r.innerHeight:s.clientHeight,n=Object.keys(e).map((e=>{if("string"==typeof e&&0===e.indexOf("@")){const t=parseFloat(e.substr(1));return{value:a*t,point:e}}return{value:e,point:e}}));n.sort(((e,t)=>parseInt(e.value,10)-parseInt(t.value,10)));for(let e=0;e<n.length;e+=1){const{point:a,value:l}=n[e];"window"===t?r.matchMedia(`(min-width: ${l}px)`).matches&&(i=a):l<=s.clientWidth&&(i=a)}return i||"max"}var breakpoints={setBreakpoint:setBreakpoint,getBreakpoint:getBreakpoint};function prepareClasses(e,t){const s=[];return e.forEach((e=>{"object"==typeof e?Object.keys(e).forEach((i=>{e[i]&&s.push(t+i)})):"string"==typeof e&&s.push(t+e)})),s}function addClasses(){const e=this,{classNames:t,params:s,rtl:i,el:r,device:a}=e,n=prepareClasses(["initialized",s.direction,{"free-mode":e.params.freeMode&&s.freeMode.enabled},{autoheight:s.autoHeight},{rtl:i},{grid:s.grid&&s.grid.rows>1},{"grid-column":s.grid&&s.grid.rows>1&&"column"===s.grid.fill},{android:a.android},{ios:a.ios},{"css-mode":s.cssMode},{centered:s.cssMode&&s.centeredSlides},{"watch-progress":s.watchSlidesProgress}],s.containerModifierClass);t.push(...n),r.classList.add(...t),e.emitContainerClasses()}function removeClasses(){const{el:e,classNames:t}=this;e&&"string"!=typeof e&&(e.classList.remove(...t),this.emitContainerClasses())}var classes={addClasses:addClasses,removeClasses:removeClasses};function checkOverflow(){const e=this,{isLocked:t,params:s}=e,{slidesOffsetBefore:i}=s;if(i){const t=e.slides.length-1,s=e.slidesGrid[t]+e.slidesSizesGrid[t]+2*i;e.isLocked=e.size>s}else e.isLocked=1===e.snapGrid.length;!0===s.allowSlideNext&&(e.allowSlideNext=!e.isLocked),!0===s.allowSlidePrev&&(e.allowSlidePrev=!e.isLocked),t&&t!==e.isLocked&&(e.isEnd=!1),t!==e.isLocked&&e.emit(e.isLocked?"lock":"unlock")}var checkOverflow$1={checkOverflow:checkOverflow},defaults={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function moduleExtendParams(e,t){return function(s){void 0===s&&(s={});const i=Object.keys(s)[0],r=s[i];"object"==typeof r&&null!==r?(!0===e[i]&&(e[i]={enabled:!0}),"navigation"===i&&e[i]&&e[i].enabled&&!e[i].prevEl&&!e[i].nextEl&&(e[i].auto=!0),["pagination","scrollbar"].indexOf(i)>=0&&e[i]&&e[i].enabled&&!e[i].el&&(e[i].auto=!0),i in e&&"enabled"in r?("object"!=typeof e[i]||"enabled"in e[i]||(e[i].enabled=!0),e[i]||(e[i]={enabled:!1}),extend(t,s)):extend(t,s)):extend(t,s)}}const prototypes={eventsEmitter:eventsEmitter,update:update,translate:translate,transition:transition,slide:slide,loop:loop,grabCursor:grabCursor,events:events$1,breakpoints:breakpoints,checkOverflow:checkOverflow$1,classes:classes},extendedDefaults={};class Swiper{constructor(){let e,t;for(var s=arguments.length,i=new Array(s),r=0;r<s;r++)i[r]=arguments[r];1===i.length&&i[0].constructor&&"Object"===Object.prototype.toString.call(i[0]).slice(8,-1)?t=i[0]:[e,t]=i,t||(t={}),t=extend({},t),e&&!t.el&&(t.el=e);const a=getDocument();if(t.el&&"string"==typeof t.el&&a.querySelectorAll(t.el).length>1){const e=[];return a.querySelectorAll(t.el).forEach((s=>{const i=extend({},t,{el:s});e.push(new Swiper(i))})),e}const n=this;n.__swiper__=!0,n.support=getSupport(),n.device=getDevice({userAgent:t.userAgent}),n.browser=getBrowser(),n.eventsListeners={},n.eventsAnyListeners=[],n.modules=[...n.__modules__],t.modules&&Array.isArray(t.modules)&&n.modules.push(...t.modules);const l={};n.modules.forEach((e=>{e({params:t,swiper:n,extendParams:moduleExtendParams(t,l),on:n.on.bind(n),once:n.once.bind(n),off:n.off.bind(n),emit:n.emit.bind(n)})}));const o=extend({},defaults,l);return n.params=extend({},o,extendedDefaults,t),n.originalParams=extend({},n.params),n.passedParams=extend({},t),n.params&&n.params.on&&Object.keys(n.params.on).forEach((e=>{n.on(e,n.params.on[e])})),n.params&&n.params.onAny&&n.onAny(n.params.onAny),Object.assign(n,{enabled:n.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=>"horizontal"===n.params.direction,isVertical:()=>"vertical"===n.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:n.params.allowSlideNext,allowSlidePrev:n.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:n.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:n.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),n.emit("_swiper"),n.params.init&&n.init(),n}getDirectionLabel(e){return this.isHorizontal()?e:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[e]}getSlideIndex(e){const{slidesEl:t,params:s}=this,i=elementChildren(t,`.${s.slideClass}, swiper-slide`),r=elementIndex(i[0]);return elementIndex(e)-r}getSlideIndexByData(e){return this.getSlideIndex(this.slides.find((t=>1*t.getAttribute("data-swiper-slide-index")===e)))}getSlideIndexWhenGrid(e){return this.grid&&this.params.grid&&this.params.grid.rows>1&&("column"===this.params.grid.fill?e=Math.floor(e/this.params.grid.rows):"row"===this.params.grid.fill&&(e%=Math.ceil(this.slides.length/this.params.grid.rows))),e}recalcSlides(){const{slidesEl:e,params:t}=this;this.slides=elementChildren(e,`.${t.slideClass}, swiper-slide`)}enable(){const e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){const e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,t){const s=this;e=Math.min(Math.max(e,0),1);const i=s.minTranslate(),r=(s.maxTranslate()-i)*e+i;s.translateTo(r,void 0===t?0:t),s.updateActiveIndex(),s.updateSlidesClasses()}emitContainerClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=e.el.className.split(" ").filter((t=>0===t.indexOf("swiper")||0===t.indexOf(e.params.containerModifierClass)));e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){const t=this;return t.destroyed?"":e.className.split(" ").filter((e=>0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass))).join(" ")}emitSlidesClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=[];e.slides.forEach((s=>{const i=e.getSlideClasses(s);t.push({slideEl:s,classNames:i}),e.emit("_slideClass",s,i)})),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){void 0===e&&(e="current"),void 0===t&&(t=!1);const{params:s,slides:i,slidesGrid:r,slidesSizesGrid:a,size:n,activeIndex:l}=this;let o=1;if("number"==typeof s.slidesPerView)return s.slidesPerView;if(s.centeredSlides){let e,t=i[l]?Math.ceil(i[l].swiperSlideSize):0;for(let s=l+1;s<i.length;s+=1)i[s]&&!e&&(t+=Math.ceil(i[s].swiperSlideSize),o+=1,t>n&&(e=!0));for(let s=l-1;s>=0;s-=1)i[s]&&!e&&(t+=i[s].swiperSlideSize,o+=1,t>n&&(e=!0))}else if("current"===e)for(let e=l+1;e<i.length;e+=1){(t?r[e]+a[e]-r[l]<n:r[e]-r[l]<n)&&(o+=1)}else for(let e=l-1;e>=0;e-=1){r[l]-r[e]<n&&(o+=1)}return o}update(){const e=this;if(!e||e.destroyed)return;const{snapGrid:t,params:s}=e;function i(){const t=e.rtlTranslate?-1*e.translate:e.translate,s=Math.min(Math.max(t,e.maxTranslate()),e.minTranslate());e.setTranslate(s),e.updateActiveIndex(),e.updateSlidesClasses()}let r;if(s.breakpoints&&e.setBreakpoint(),[...e.el.querySelectorAll('[loading="lazy"]')].forEach((t=>{t.complete&&processLazyPreloader(e,t)})),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),s.freeMode&&s.freeMode.enabled&&!s.cssMode)i(),s.autoHeight&&e.updateAutoHeight();else{if(("auto"===s.slidesPerView||s.slidesPerView>1)&&e.isEnd&&!s.centeredSlides){const t=e.virtual&&s.virtual.enabled?e.virtual.slides:e.slides;r=e.slideTo(t.length-1,0,!1,!0)}else r=e.slideTo(e.activeIndex,0,!1,!0);r||i()}s.watchOverflow&&t!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,t){void 0===t&&(t=!0);const s=this,i=s.params.direction;return e||(e="horizontal"===i?"vertical":"horizontal"),e===i||"horizontal"!==e&&"vertical"!==e||(s.el.classList.remove(`${s.params.containerModifierClass}${i}`),s.el.classList.add(`${s.params.containerModifierClass}${e}`),s.emitContainerClasses(),s.params.direction=e,s.slides.forEach((t=>{"vertical"===e?t.style.width="":t.style.height=""})),s.emit("changeDirection"),t&&s.update()),s}changeLanguageDirection(e){const t=this;t.rtl&&"rtl"===e||!t.rtl&&"ltr"===e||(t.rtl="rtl"===e,t.rtlTranslate="horizontal"===t.params.direction&&t.rtl,t.rtl?(t.el.classList.add(`${t.params.containerModifierClass}rtl`),t.el.dir="rtl"):(t.el.classList.remove(`${t.params.containerModifierClass}rtl`),t.el.dir="ltr"),t.update())}mount(e){const t=this;if(t.mounted)return!0;let s=e||t.params.el;if("string"==typeof s&&(s=document.querySelector(s)),!s)return!1;s.swiper=t,s.parentNode&&s.parentNode.host&&s.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);const i=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`;let r=(()=>{if(s&&s.shadowRoot&&s.shadowRoot.querySelector){return s.shadowRoot.querySelector(i())}return elementChildren(s,i())[0]})();return!r&&t.params.createElements&&(r=createElement("div",t.params.wrapperClass),s.append(r),elementChildren(s,`.${t.params.slideClass}`).forEach((e=>{r.append(e)}))),Object.assign(t,{el:s,wrapperEl:r,slidesEl:t.isElement&&!s.parentNode.host.slideSlots?s.parentNode.host:r,hostEl:t.isElement?s.parentNode.host:s,mounted:!0,rtl:"rtl"===s.dir.toLowerCase()||"rtl"===elementStyle(s,"direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===s.dir.toLowerCase()||"rtl"===elementStyle(s,"direction")),wrongRTL:"-webkit-box"===elementStyle(r,"display")}),!0}init(e){const t=this;if(t.initialized)return t;if(!1===t.mount(e))return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(void 0,!0),t.attachEvents();const s=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&s.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),s.forEach((e=>{e.complete?processLazyPreloader(t,e):e.addEventListener("load",(e=>{processLazyPreloader(t,e.target)}))})),preload(t),t.initialized=!0,preload(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);const s=this,{params:i,el:r,wrapperEl:a,slides:n}=s;return void 0===s.params||s.destroyed||(s.emit("beforeDestroy"),s.initialized=!1,s.detachEvents(),i.loop&&s.loopDestroy(),t&&(s.removeClasses(),r&&"string"!=typeof r&&r.removeAttribute("style"),a&&a.removeAttribute("style"),n&&n.length&&n.forEach((e=>{e.classList.remove(i.slideVisibleClass,i.slideFullyVisibleClass,i.slideActiveClass,i.slideNextClass,i.slidePrevClass),e.removeAttribute("style"),e.removeAttribute("data-swiper-slide-index")}))),s.emit("destroy"),Object.keys(s.eventsListeners).forEach((e=>{s.off(e)})),!1!==e&&(s.el&&"string"!=typeof s.el&&(s.el.swiper=null),deleteProps(s)),s.destroyed=!0),null}static extendDefaults(e){extend(extendedDefaults,e)}static get extendedDefaults(){return extendedDefaults}static get defaults(){return defaults}static installModule(e){Swiper.prototype.__modules__||(Swiper.prototype.__modules__=[]);const t=Swiper.prototype.__modules__;"function"==typeof e&&t.indexOf(e)<0&&t.push(e)}static use(e){return Array.isArray(e)?(e.forEach((e=>Swiper.installModule(e))),Swiper):(Swiper.installModule(e),Swiper)}}Object.keys(prototypes).forEach((e=>{Object.keys(prototypes[e]).forEach((t=>{Swiper.prototype[t]=prototypes[e][t]}))})),Swiper.use([Resize,Observer]);export{Swiper as S,defaults as d};
//# sourceMappingURL=swiper-core.min.mjs.map