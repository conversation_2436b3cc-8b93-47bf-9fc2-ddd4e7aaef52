import{g as getDocument,a as getWindow}from"../shared/ssr-window.esm.min.mjs";import{b as elementParents,d as elementOffset}from"../shared/utils.min.mjs";function Keyboard(e){let{swiper:t,extendParams:n,on:a,emit:r}=e;const l=getDocument(),i=getWindow();function o(e){if(!t.enabled)return;const{rtlTranslate:n}=t;let a=e;a.originalEvent&&(a=a.originalEvent);const o=a.keyCode||a.charCode,s=t.params.keyboard.pageUpDown,d=s&&33===o,m=s&&34===o,f=37===o,b=39===o,c=38===o,p=40===o;if(!t.allowSlideNext&&(t.isHorizontal()&&b||t.isVertical()&&p||m))return!1;if(!t.allowSlidePrev&&(t.isHorizontal()&&f||t.isVertical()&&c||d))return!1;if(!(a.shiftKey||a.altKey||a.ctrlKey||a.metaKey||l.activeElement&&(l.activeElement.isContentEditable||l.activeElement.nodeName&&("input"===l.activeElement.nodeName.toLowerCase()||"textarea"===l.activeElement.nodeName.toLowerCase())))){if(t.params.keyboard.onlyInViewport&&(d||m||f||b||c||p)){let e=!1;if(elementParents(t.el,`.${t.params.slideClass}, swiper-slide`).length>0&&0===elementParents(t.el,`.${t.params.slideActiveClass}`).length)return;const a=t.el,r=a.clientWidth,l=a.clientHeight,o=i.innerWidth,s=i.innerHeight,d=elementOffset(a);n&&(d.left-=a.scrollLeft);const m=[[d.left,d.top],[d.left+r,d.top],[d.left,d.top+l],[d.left+r,d.top+l]];for(let t=0;t<m.length;t+=1){const n=m[t];if(n[0]>=0&&n[0]<=o&&n[1]>=0&&n[1]<=s){if(0===n[0]&&0===n[1])continue;e=!0}}if(!e)return}t.isHorizontal()?((d||m||f||b)&&(a.preventDefault?a.preventDefault():a.returnValue=!1),((m||b)&&!n||(d||f)&&n)&&t.slideNext(),((d||f)&&!n||(m||b)&&n)&&t.slidePrev()):((d||m||c||p)&&(a.preventDefault?a.preventDefault():a.returnValue=!1),(m||p)&&t.slideNext(),(d||c)&&t.slidePrev()),r("keyPress",o)}}function s(){t.keyboard.enabled||(l.addEventListener("keydown",o),t.keyboard.enabled=!0)}function d(){t.keyboard.enabled&&(l.removeEventListener("keydown",o),t.keyboard.enabled=!1)}t.keyboard={enabled:!1},n({keyboard:{enabled:!1,onlyInViewport:!0,pageUpDown:!0}}),a("init",(()=>{t.params.keyboard.enabled&&s()})),a("destroy",(()=>{t.keyboard.enabled&&d()})),Object.assign(t.keyboard,{enable:s,disable:d})}export{Keyboard as default};
//# sourceMappingURL=keyboard.min.mjs.map