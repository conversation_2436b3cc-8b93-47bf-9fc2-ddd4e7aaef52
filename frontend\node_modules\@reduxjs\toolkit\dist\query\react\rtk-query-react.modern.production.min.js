var e=Object.defineProperty,t=Object.defineProperties,r=Object.getOwnPropertyDescriptors,n=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,o=(t,r,n)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[r]=n,u=(e,t)=>{for(var r in t||(t={}))i.call(t,r)&&o(e,r,t[r]);if(n)for(var r of n(t))s.call(t,r)&&o(e,r,t[r]);return e},c=(e,n)=>t(e,r(n));import{coreModule as a,buildCreateApi as d}from"@reduxjs/toolkit/query";import{createSelector as l}from"@reduxjs/toolkit";import{useCallback as p,useDebugValue as f,useEffect as y,useLayoutEffect as m,useMemo as b,useRef as h,useState as g}from"react";import{QueryStatus as v,skipToken as S}from"@reduxjs/toolkit/query";import{shallowEqual as O}from"react-redux";import{useEffect as q,useRef as x,useMemo as Q}from"react";function k(e,t,r,n){const i=Q((()=>({queryArgs:e,serialized:"object"==typeof e?t({queryArgs:e,endpointDefinition:r,endpointName:n}):e})),[e,t,r,n]),s=x(i);return q((()=>{s.current.serialized!==i.serialized&&(s.current=i)}),[i]),s.current.serialized===i.serialized?s.current.queryArgs:e}var z=Symbol();import{useEffect as j,useRef as L}from"react";import{shallowEqual as w}from"react-redux";function A(e){const t=L(e);return j((()=>{w(t.current,e)||(t.current=e)}),[e]),w(t.current,e)?t.current:e}import{isPlainObject as E}from"@reduxjs/toolkit";var R,I,D=WeakMap?new WeakMap:void 0,C=({endpointName:e,queryArgs:t})=>{let r="";const n=null==D?void 0:D.get(t);if("string"==typeof n)r=n;else{const e=JSON.stringify(t,((e,t)=>E(t)?Object.keys(t).sort().reduce(((e,r)=>(e[r]=t[r],e)),{}):t));E(t)&&(null==D||D.set(t,e)),r=e}return`${e}(${r})`},F="undefined"!=typeof window&&window.document&&window.document.createElement?m:y,M=e=>e,N=e=>e.isUninitialized?c(u({},e),{isUninitialized:!1,isFetching:!0,isLoading:void 0===e.data,status:v.pending}):e;function P(e){return e.replace(e[0],e[0].toUpperCase())}function K(e,...t){Object.assign(e,...t)}(I=R||(R={})).query="query",I.mutation="mutation";import{useDispatch as _,useSelector as $,useStore as H,batch as U}from"react-redux";var W=Symbol(),J=({batch:e=U,useDispatch:t=_,useSelector:r=$,useStore:n=H,unstable__sideEffectsInRender:i=!1}={})=>({name:W,init(s,{serializeQueryArgs:o},a){const d=s,{buildQueryHooks:m,buildMutationHook:v,usePrefetch:q}=function({api:e,moduleOptions:{batch:t,useDispatch:r,useSelector:n,useStore:i,unstable__sideEffectsInRender:s},serializeQueryArgs:o,context:a}){const d=s?e=>e():y;return{buildQueryHooks:function(s){const v=(t,{refetchOnReconnect:n,refetchOnFocus:i,refetchOnMountOrArgChange:o,skip:u=!1,pollingInterval:c=0}={})=>{const{initiate:l}=e.endpoints[s],p=r(),f=k(u?S:t,C,a.endpointDefinitions[s],s),m=A({refetchOnReconnect:n,refetchOnFocus:i,pollingInterval:c}),g=h(!1),v=h();let{queryCacheKey:O,requestId:q}=v.current||{},x=!1;if(O&&q){const t=p(e.internalActions.internal_probeSubscription({queryCacheKey:O,requestId:q}));x=!!t}const Q=!x&&g.current;return d((()=>{g.current=x})),d((()=>{Q&&(v.current=void 0)}),[Q]),d((()=>{var e;const t=v.current;if(f===S)return null==t||t.unsubscribe(),void(v.current=void 0);const r=null==(e=v.current)?void 0:e.subscriptionOptions;if(t&&t.arg===f)m!==r&&t.updateSubscriptionOptions(m);else{null==t||t.unsubscribe();const e=p(l(f,{subscriptionOptions:m,forceRefetch:o}));v.current=e}}),[p,l,o,f,m,Q]),y((()=>()=>{var e;null==(e=v.current)||e.unsubscribe(),v.current=void 0}),[]),b((()=>({refetch:()=>{var e;if(!v.current)throw new Error("Cannot refetch a query that has not been started yet.");return null==(e=v.current)?void 0:e.refetch()}})),[])},q=({refetchOnReconnect:n,refetchOnFocus:i,pollingInterval:o=0}={})=>{const{initiate:u}=e.endpoints[s],c=r(),[a,l]=g(z),f=h(),m=A({refetchOnReconnect:n,refetchOnFocus:i,pollingInterval:o});d((()=>{var e,t;const r=null==(e=f.current)?void 0:e.subscriptionOptions;m!==r&&(null==(t=f.current)||t.updateSubscriptionOptions(m))}),[m]);const v=h(m);d((()=>{v.current=m}),[m]);const S=p((function(e,r=!1){let n;return t((()=>{var t;null==(t=f.current)||t.unsubscribe(),f.current=n=c(u(e,{subscriptionOptions:v.current,forceRefetch:!r})),l(e)})),n}),[c,u]);return y((()=>()=>{var e;null==(e=null==f?void 0:f.current)||e.unsubscribe()}),[]),y((()=>{a===z||f.current||S(a,!0)}),[a,S]),b((()=>[S,a]),[S,a])},x=(t,{skip:r=!1,selectFromResult:u}={})=>{const{select:c}=e.endpoints[s],d=k(r?S:t,o,a.endpointDefinitions[s],s),p=h(),f=b((()=>l([c(d),(e,t)=>t,e=>d],m)),[c,d]),y=b((()=>u?l([f],u):f),[f,u]),g=n((e=>y(e,p.current)),O),v=i(),q=f(v.getState(),p.current);return F((()=>{p.current=q}),[q]),g};return{useQueryState:x,useQuerySubscription:v,useLazyQuerySubscription:q,useLazyQuery(e){const[t,r]=q(e),n=x(r,c(u({},e),{skip:r===z})),i=b((()=>({lastArg:r})),[r]);return b((()=>[t,n,i]),[t,n,i])},useQuery(e,t){const r=v(e,t),n=x(e,u({selectFromResult:e===S||(null==t?void 0:t.skip)?void 0:N},t)),{data:i,status:s,isLoading:o,isSuccess:c,isError:a,error:d}=n;return f({data:i,status:s,isLoading:o,isSuccess:c,isError:a,error:d}),b((()=>u(u({},n),r)),[n,r])}}},buildMutationHook:function(i){return({selectFromResult:s=M,fixedCacheKey:o}={})=>{const{select:a,initiate:d}=e.endpoints[i],m=r(),[h,v]=g();y((()=>()=>{(null==h?void 0:h.arg.fixedCacheKey)||null==h||h.reset()}),[h]);const S=p((function(e){const t=m(d(e,{fixedCacheKey:o}));return v(t),t}),[m,d,o]),{requestId:q}=h||{},x=b((()=>l([a({fixedCacheKey:o,requestId:null==h?void 0:h.requestId})],s)),[a,h,s,o]),Q=n(x,O),k=null==o?null==h?void 0:h.arg.originalArgs:void 0,z=p((()=>{t((()=>{h&&v(void 0),o&&m(e.internalActions.removeMutationResult({requestId:q,fixedCacheKey:o}))}))}),[m,o,h,q]),{endpointName:j,data:L,status:w,isLoading:A,isSuccess:E,isError:R,error:I}=Q;f({endpointName:j,data:L,status:w,isLoading:A,isSuccess:E,isError:R,error:I});const D=b((()=>c(u({},Q),{originalArgs:k,reset:z})),[Q,k,z]);return b((()=>[S,D]),[S,D])}},usePrefetch:function(t,n){const i=r(),s=A(n);return p(((r,n)=>i(e.util.prefetch(t,r,u(u({},s),n)))),[t,i,s])}};function m(e,t,r){if((null==t?void 0:t.endpointName)&&e.isUninitialized){const{endpointName:e}=t,n=a.endpointDefinitions[e];o({queryArgs:t.originalArgs,endpointDefinition:n,endpointName:e})===o({queryArgs:r,endpointDefinition:n,endpointName:e})&&(t=void 0)}let n=e.isSuccess?e.data:null==t?void 0:t.data;void 0===n&&(n=e.data);const i=void 0!==n,s=e.isLoading,d=!i&&s,l=e.isSuccess||s&&i;return c(u({},e),{data:n,currentData:e.data,isFetching:s,isLoading:d,isSuccess:l})}}({api:s,moduleOptions:{batch:e,useDispatch:t,useSelector:r,useStore:n,unstable__sideEffectsInRender:i},serializeQueryArgs:o,context:a});return K(d,{usePrefetch:q}),K(a,{batch:e}),{injectEndpoint(e,t){if(t.type===R.query){const{useQuery:t,useLazyQuery:r,useLazyQuerySubscription:n,useQueryState:i,useQuerySubscription:o}=m(e);K(d.endpoints[e],{useQuery:t,useLazyQuery:r,useLazyQuerySubscription:n,useQueryState:i,useQuerySubscription:o}),s[`use${P(e)}Query`]=t,s[`useLazy${P(e)}Query`]=r}else if(t.type===R.mutation){const t=v(e);K(d.endpoints[e],{useMutation:t}),s[`use${P(e)}Mutation`]=t}}}}});export*from"@reduxjs/toolkit/query";import{configureStore as B}from"@reduxjs/toolkit";import{useEffect as G}from"react";import T from"react";import{Provider as V}from"react-redux";import{setupListeners as X}from"@reduxjs/toolkit/query";function Y(e){const[t]=T.useState((()=>B({reducer:{[e.api.reducerPath]:e.api.reducer},middleware:t=>t().concat(e.api.middleware)})));return G((()=>!1===e.setupListeners?void 0:X(t.dispatch,e.setupListeners)),[e.setupListeners,t.dispatch]),T.createElement(V,{store:t,context:e.context},e.children)}var Z=d(a(),J());export{Y as ApiProvider,Z as createApi,J as reactHooksModule,W as reactHooksModuleName};
//# sourceMappingURL=rtk-query-react.modern.production.min.js.map