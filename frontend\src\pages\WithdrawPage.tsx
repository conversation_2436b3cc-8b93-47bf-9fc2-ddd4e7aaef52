import React, { useState } from 'react';
import { Card, Button, Input, Selector, Toast } from 'antd-mobile';
import { 
  SendOutline 
} from 'antd-mobile-icons';
import { formatNumber } from '../utils/formatters';
import './WithdrawPage.scss';

// 提现方式
const withdrawMethods = [
  {
    value: 'usdt',
    label: 'USDT (TRC20)',
    icon: <div>💰</div>,
    minAmount: 10,
    fee: 1,
  },
  {
    value: 'bank',
    label: 'Bank Transfer',
    icon: <div>🏦</div>,
    minAmount: 50,
    fee: 5,
  },
  {
    value: 'card',
    label: 'Credit Card',
    icon: <div>💳</div>,
    minAmount: 20,
    fee: 3,
  },
];

const WithdrawPage: React.FC = () => {
  const [selectedMethod, setSelectedMethod] = useState<string>('usdt');
  const [amount, setAmount] = useState<string>('');
  const [address, setAddress] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  // 模拟用户余额
  const userBalance = 0.000771;

  const selectedMethodData = withdrawMethods.find(m => m.value === selectedMethod);

  const handleWithdraw = async () => {
    if (!amount || !address) {
      Toast.show('请填写完整信息');
      return;
    }

    const numAmount = parseFloat(amount);
    if (numAmount < (selectedMethodData?.minAmount || 0)) {
      Toast.show(`最小提现金额为 ${selectedMethodData?.minAmount} USDT`);
      return;
    }

    if (numAmount > userBalance) {
      Toast.show('余额不足');
      return;
    }

    setIsLoading(true);
    try {
      // TODO: 调用提现API
      await new Promise(resolve => setTimeout(resolve, 2000)); // 模拟API调用
      Toast.show('提现申请已提交');
      setAmount('');
      setAddress('');
    } catch (error) {
      Toast.show('提现失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleMaxAmount = () => {
    setAmount(userBalance.toString());
  };

  return (
    <div className="withdraw-page">
      {/* 余额卡片 */}
      <Card className="balance-card">
        <div className="balance__header">
          <div className="balance-icon">💰</div>
          <h2>Available Balance</h2>
        </div>
        <div className="balance__amount">
          <span className="amount">{formatNumber(userBalance, 6)}</span>
          <span className="currency">USDT</span>
        </div>
      </Card>

      {/* 提现方式选择 */}
      <Card className="method-card">
        <div className="method__header">
          <h3>Withdrawal Method</h3>
        </div>
        <Selector
          options={withdrawMethods}
          value={[selectedMethod]}
          onChange={(arr) => setSelectedMethod(arr[0])}
          columns={1}
        />
      </Card>

      {/* 提现表单 */}
      <Card className="withdraw-form-card">
        <div className="form__header">
          <h3>Withdrawal Details</h3>
        </div>

        <div className="form__field">
          <label>Amount (USDT)</label>
          <div className="amount-input">
            <Input
              placeholder="Enter amount"
              value={amount}
              onChange={setAmount}
              type="number"
            />
            <Button size="small" onClick={handleMaxAmount}>
              MAX
            </Button>
          </div>
          {selectedMethodData && (
            <div className="amount-info">
              <span>Min: {selectedMethodData.minAmount} USDT</span>
              <span>Fee: {selectedMethodData.fee} USDT</span>
            </div>
          )}
        </div>

        <div className="form__field">
          <label>
            {selectedMethod === 'usdt' ? 'USDT Address (TRC20)' : 
             selectedMethod === 'bank' ? 'Bank Account Details' : 
             'Card Number'}
          </label>
          <Input
            placeholder={
              selectedMethod === 'usdt' ? 'Enter USDT address' :
              selectedMethod === 'bank' ? 'Enter bank details' :
              'Enter card number'
            }
            value={address}
            onChange={setAddress}
          />
        </div>

        {selectedMethodData && amount && (
          <div className="withdraw-summary">
            <div className="summary-item">
              <span>Amount:</span>
              <span>{formatNumber(parseFloat(amount), 6)} USDT</span>
            </div>
            <div className="summary-item">
              <span>Fee:</span>
              <span>{selectedMethodData.fee} USDT</span>
            </div>
            <div className="summary-item total">
              <span>You'll receive:</span>
              <span>{formatNumber(parseFloat(amount) - selectedMethodData.fee, 6)} USDT</span>
            </div>
          </div>
        )}

        <Button
          className="withdraw-button"
          color="primary"
          size="large"
          loading={isLoading}
          onClick={handleWithdraw}
          disabled={!amount || !address}
        >
          <SendOutline />
          Withdraw
        </Button>
      </Card>

      {/* 提现历史 */}
      <Card className="history-card">
        <div className="history__header">
          <h3>Withdrawal History</h3>
        </div>
        <div className="history__content">
          <p>No withdrawal history yet</p>
        </div>
      </Card>
    </div>
  );
};

export default WithdrawPage; 