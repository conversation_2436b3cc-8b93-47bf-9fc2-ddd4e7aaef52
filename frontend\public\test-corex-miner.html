<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
        href="https://fonts.googleapis.com/css2?family=Rubik:ital,wght@0,300..900;1,300..900&display=swap"
        rel="stylesheet"
    />
    <title>CoreX MINER - Test</title>
    <link
        rel="stylesheet"
        href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css"
    />
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: "Rubik", serif;
            line-height: 1.4;
            background-color: #000;
            color: #fff;
            min-height: 100vh;
        }

        .container {
            width: 100%;
            max-width: 1100px;
            margin: auto;
            padding: 0 15px;
            text-align: center;
        }

        .header {
            background: #202020;
            padding-top: 25px;
            padding-bottom: 11px;
        }

        .header__text {
            margin-bottom: 17px;
            text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
            font-size: 44px;
            font-weight: 500;
            line-height: 17.551px;
        }

        .header__link {
            text-decoration: none;
            color: rgba(114, 181, 230, 0.9);
            text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
            font-size: 21px;
            font-style: normal;
            font-weight: 400;
            line-height: 17.551px;
        }

        .hero__title {
            margin: auto;
            background-image: url('/images/block.png');
            background-repeat: no-repeat;
            background-position: 0 0;
            background-size: cover;
            width: 300px;
            padding: 15px 0 17px;
            margin-bottom: 30px;
            color: #fff;
            text-align: center;
            font-size: 24px;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }

        .hero__image {
            width: 100%;
            max-width: 400px;
            height: auto;
            border-radius: 10px;
            margin-bottom: 30px;
        }

        .swiper-container {
            position: relative;
            margin-bottom: 40px;
        }

        .swiper-custom {
            padding: 20px 0;
        }

        .custom-button-next,
        .custom-button-prev {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            z-index: 10;
            cursor: pointer;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .custom-button-next:hover,
        .custom-button-prev:hover {
            background: rgba(0, 0, 0, 0.8);
        }

        .hero__arrow-icon {
            width: 24px;
            height: 24px;
        }

        .custom-button-next {
            right: 20px;
        }

        .custom-button-prev {
            left: 20px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 15px 40px;
            font-size: 18px;
            font-weight: 600;
            border-radius: 25px;
            cursor: pointer;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.4);
        }

        .text {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            font-style: italic;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <p class="header__text">CoreX MINER</p>
            <a href="https://t.me/Core_xbot" class="header__link">Cloud Mining in Telegram</a>
        </div>
    </header>
    
    <main>
        <section>
            <div class="container">
                <div class="swiper-container">
                    <div class="swiper swiper-custom">
                        <div class="swiper-wrapper">
                            <div class="swiper-slide">
                                <h1 class="hero__title">CALCUMINER</h1>
                                <video autoplay muted loop playsinline class="hero__image">
                                    <source src="/videos/one.mp4" type="video/mp4" />
                                </video>
                            </div>
                            <div class="swiper-slide">
                                <h1 class="hero__title">BLOCK BREACKER</h1>
                                <video autoplay muted loop playsinline class="hero__image">
                                    <source src="/videos/two.MP4" type="video/mp4" />
                                </video>
                            </div>
                            <div class="swiper-slide">
                                <h1 class="hero__title">QUANTUM CORE</h1>
                                <video autoplay muted loop playsinline class="hero__image">
                                    <source src="/videos/three.MP4" type="video/mp4" />
                                </video>
                            </div>
                        </div>
                        <div class="custom-button-next">
                            <img class="hero__arrow-icon" src="/images/arrow-rigth.png" alt="Next" />
                        </div>
                        <div class="custom-button-prev">
                            <img class="hero__arrow-icon" src="/images/arrow-left.png" alt="Prev" />
                        </div>
                    </div>
                </div>
                
                <a href="https://t.me/core_xbot">
                    <button class="btn">START MINING</button>
                </a>
                <p class="text">*Choose your graphic card and start mining in 1 click</p>
            </div>
        </section>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    <script>
        const swiper = new Swiper(".swiper", {
            spaceBetween: 30,
            slidesPerView: 1,
            speed: 500,
            navigation: {
                nextEl: ".custom-button-next",
                prevEl: ".custom-button-prev",
            },
            on: {
                slideChange: updateButtonState,
            },
        });

        function updateButtonState() {
            const prevButton = document.querySelector(".custom-button-prev");
            const nextButton = document.querySelector(".custom-button-next");

            // Hide "Prev" button on first slide
            if (swiper.isBeginning) {
                prevButton.style.display = "none";
            } else {
                prevButton.style.display = "block";
            }

            // Hide "Next" button on last slide
            if (swiper.isEnd) {
                nextButton.style.display = "none";
            } else {
                nextButton.style.display = "block";
            }
        }

        // Initialize button state on load
        updateButtonState();
    </script>
</body>
</html>
