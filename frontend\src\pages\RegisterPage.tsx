import React, { useState } from 'react';
import { useN<PERSON>gate, Link } from 'react-router-dom';
import { Form, Input, Button, Toast } from 'antd-mobile';
import { 
  UserOutline, 
  LockOutline, 
  MailOutline,
  EyeOutline, 
  EyeInvisibleOutline,
  GiftOutline 
} from 'antd-mobile-icons';
import { useAuth } from '../hooks/useAuth';
import './RegisterPage.scss';

const RegisterPage: React.FC = () => {
  const navigate = useNavigate();
  const { register, isLoading } = useAuth();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const onFinish = async (values: { 
    username: string; 
    email: string; 
    password: string;
    confirmPassword: string;
    referralCode?: string;
  }) => {
    if (values.password !== values.confirmPassword) {
      Toast.show('密码确认不匹配');
      return;
    }

    try {
      const result = await register({
        username: values.username,
        email: values.email,
        password: values.password,
        referralCode: values.referralCode,
      });
      
      if (result.success) {
        Toast.show('注册成功');
        navigate('/');
      } else {
        Toast.show(result.error || '注册失败');
      }
    } catch (error) {
      Toast.show('注册失败，请重试');
    }
  };

  return (
    <div className="register-page">
      <div className="register-container">
        <div className="register-header">
          <h1>Join CoreX MINER</h1>
          <p>Start mining and earning USDT</p>
        </div>

        <Form
          className="register-form"
          onFinish={onFinish}
          layout="vertical"
          footer={
            <Button
              block
              type="submit"
              color="primary"
              size="large"
              loading={isLoading}
            >
              Create Account
            </Button>
          }
        >
          <Form.Item
            name="username"
            label="Username"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 3, message: '用户名至少3个字符' },
              { max: 20, message: '用户名最多20个字符' }
            ]}
          >
            <Input
              placeholder="Choose a username"
              prefix={<UserOutline />}
            />
          </Form.Item>

          <Form.Item
            name="email"
            label="Email"
            rules={[
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input
              placeholder="Enter your email"
              prefix={<MailOutline />}
              type="email"
            />
          </Form.Item>

          <Form.Item
            name="password"
            label="Password"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码至少6个字符' }
            ]}
          >
            <Input
              placeholder="Create a password"
              type={showPassword ? 'text' : 'password'}
              prefix={<LockOutline />}
              extra={
                <Button
                  fill="none"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeInvisibleOutline /> : <EyeOutline />}
                </Button>
              }
            />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            label="Confirm Password"
            rules={[
              { required: true, message: '请确认密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('密码确认不匹配'));
                },
              }),
            ]}
          >
            <Input
              placeholder="Confirm your password"
              type={showConfirmPassword ? 'text' : 'password'}
              prefix={<LockOutline />}
              extra={
                <Button
                  fill="none"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? <EyeInvisibleOutline /> : <EyeOutline />}
                </Button>
              }
            />
          </Form.Item>

          <Form.Item
            name="referralCode"
            label="Referral Code (Optional)"
          >
            <Input
              placeholder="Enter referral code"
              prefix={<GiftOutline />}
            />
          </Form.Item>
        </Form>

        <div className="register-footer">
          <p>
            Already have an account?{' '}
            <Link to="/login" className="login-link">
              Sign in
            </Link>
          </p>
        </div>

        <div className="register-benefits">
          <h3>Why Choose CoreX MINER?</h3>
          <ul>
            <li>✓ Instant mining start</li>
            <li>✓ Real USDT earnings</li>
            <li>✓ Easy withdrawals</li>
            <li>✓ 24/7 support</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default RegisterPage; 