.header {
  height: 48px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  max-width: 500px;
  width: 100%;
  margin: 0 auto;
  display: flex;
  align-items: center;
  background: #202020;
  z-index: 100;
  border-bottom: 1px solid #333;

  .header__nav {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 0;
    width: 100%;
  }

  .header__menu-button {
    width: 28px;
    height: 28px;
    background: transparent;
    border: none;
    color: #fff;
    cursor: pointer;
  }

  .header__menu-icon {
    width: 28px;
    height: 28px;
  }

  .language__box {
    position: relative;
  }

  .language__button {
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0;
  }

  .lang__img {
    width: 28px;
    height: 20px;
    border-radius: 4px;
  }

  .header__balance {
    position: relative;
    margin-left: auto;
    height: 26px;
    padding: 6px 13px 6px 30px;
    display: flex;
    align-items: center;
    gap: 4px;
    border-radius: 10px;
    border: 1px solid #353535;
    background: #131313;
    box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.25) inset;

    img {
      position: absolute;
      top: 57%;
      left: -7px;
      transform: translateY(-50%);
    }
  }

  .header__balance-amount {
    color: #fff;
    font-size: 21px;
    font-style: normal;
    font-weight: 400;
    line-height: 17.913px;
  }

  .header__user-plus {
    width: 32px;
    height: 26px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 5px;
    background: #2cadef;
    box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.15);
  }

  .header__deposit-button {
    background: transparent;
    border: none;
    color: #000;
    font-size: 14px;
    font-weight: 600;
  }
} 