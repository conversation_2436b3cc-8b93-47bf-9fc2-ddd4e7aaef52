{"mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;AAsBA,GAEA,SAAS,2BAAK,CAAC;IACb,IAAI,IAAI,IAAE,IACT,IAAI,EAAE,QAAQ;IACf,OAAO,KAAG,KAAM,CAAA,IAAE,EAAE,QAAQ,QAAQ,EAAE,QAAQ,gBAAc,CAAA;AAC9D;AACA,SAAS,0CAAY,EAAE,EAAC,CAAC;IACxB;;;;;;;CAOA,GACA,KAAM,CAAA,IAAE,CAAC,CAAA;IACT,IAAI,QAAQ,EAAE,SAAS,2BAAK,KAC3B,IAAI,EAAE,EACN,QAAQ,IAAI,OACZ,GACA,IACA,IAAI,SAAS,GAAG;QAAI,OAAO,KAAK,WAAW;YAC1C,IAAG,GAAG;gBACL,MAAM,OAAO;gBACb;YACD;YACA,sDAAsD;YACtD,EAAE,OAAQ,KAAI;QACd,kBAAkB;QAClB,GAAE,EAAE;IACL,GACA,IAAI,EAAE,SAAO,KAAK,EAAE,SAAO,WAAW,IAAI,GAC1C,KAAK,EAAE,SAAS,EAAE,SAAS,GAC3B,UAAU,EAAE,SACZ,OAAO,EAAE,YACT,GAAG,8BAA8B;IAClC,IAAG,GAAG,WAAS,KAAK,CAAC,EAAE,UAAU,CAAC,OAAO;QACxC,uDAAuD;QACvD,IAAK,SAAS,CAAC;YACb,IAAG,MAAM,IAAI,KAAK;YAClB,IAAI;YACJ,OAAO,MAAM,IAAI,MAAO,CAAA,AAAC,CAAC,KAAG,EAAE,IAAI,MAAM,IAAI,GAAE,IAAI,GAAG,KAAK,IAAI,EAAE,KAAI,CAAA;QACvE;QACA,IAAI;IACL,OAAQ,IAAG,IACX,mEAAmE;IACnE,yEAAyE;IACxE,IAAI;QACH,IAAI,IAAI,WAAS,UAAU,QAAQ,KAAK,EAAE,QAAO,IAAI;QACrD,MAAM,EAAE,IAAE,GACT,IAAI,CAAC,CAAC,EAAE,CAAC,WAAW,GAAG;YACtB,IAAI,IAAI;YACR,MAAQ,EAAE,IAAI,KAAK,GAAG,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,IAAM,mBAAmB;YACpE,IAAI,MAAM,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,mBAAmB;;QACjD;QAED,sHAAsH;QACtH,CAAC,CAAC,EAAE,GAAG;QACP,OAAO,AAAC,CAAC,KAAG,EAAE,IAAI,UAAU,MAAM,GAAG,MAAM,IAAI,EAAC,CAAC,CAAC,EAAE;IACrD;SAEA,IAAI;QACH,IAAI,IAAI,WAAS,UAAU,QAAQ,KAAK,EAAE,QAAQ,IAAI;QACtD,MAAM,EAAE,IAAE,GACT,IAAI,CAAC,CAAC,EAAE,CAAC,WAAW,GAAG;YACtB,IAAI,IAAI;YACR,MAAO,EAAE,IAAI,KAAK,SAAS,CAAC,EAAE,KAAG,CAAC,CAAC,EAAE,CAAC,EAAE,GAAK,mBAAmB;YAChE,IAAI,MAAM,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,mBAAmB;;QACjD;QAED,sHAAsH;QACtH,CAAC,CAAC,EAAE,GAAG;QACP,OAAO,AAAC,CAAC,KAAG,EAAE,IAAI,UAAU,MAAM,GAAG,MAAM,IAAI,EAAC,CAAC,CAAC,EAAE;IACrD;IAED,uBAAuB;IACvB,EAAE,QAAQ;QACT,IAAG,IAAI,aAAa;QACpB,MAAM;QACN,IAAI,EAAE;IACP;IACA,EAAE,OAAO;QAAa,OAAO,IAAI;eAAI,MAAM;SAAO,GAAG,EAAE;IAAS;IAChE,EAAE,SAAS;QAAa,OAAO,IAAI;eAAI,MAAM;SAAS,GAAG,EAAE,IAAI,CAAC,OAAS,KAAK;IAAM;IACpF,OAAO;AACR", "sources": ["src/index.js"], "sourcesContent": ["/*\r\nMIT License\r\n\r\nCopyright (c) 2018-2023 Simon <PERSON>\r\n\r\nPermission is hereby granted, free of charge, to any person obtaining a copy\r\nof this software and associated documentation files (the \"Software\"), to deal\r\nin the Software without restriction, including without limitation the rights\r\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\r\ncopies of the Software, and to permit persons to whom the Software is\r\nfurnished to do so, subject to the following conditions:\r\n\r\n\tThe above copyright notice and this permission notice shall be included in all\r\ncopies or substantial portions of the Software.\r\n\r\n\tTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\r\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\r\n\tFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\r\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\r\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\r\n\tOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\r\nSOFTWARE.\r\n*/\r\n\r\nfunction vrgs(f) {\r\n\t\tvar s = f+\"\",\r\n\t\t\ti = s.indexOf(\"...\");\r\n\t\treturn i>=0 && (i<s.indexOf(\")\") || s.indexOf(\"arguments\")>=0);\r\n}\r\nfunction nanomemoize(fn,o) {\r\n\t/*o = {\r\n\t\tserializer, // used to serialize arguments of single argument functions, multis are not serialized\r\n\t\tequals, // equality tester, will force use of slower multiarg approach even for single arg functions\r\n\t\tmaxAge, // max cache age is ms, set > 0 && < Infinity if you want automatic clearing\r\n\t\tmaxArgs, // max args to use for signature\r\n\t\tvargs = vrgs(fn) // set to true if function may have variable or beyond-signature arguments, default is best attempt at infering\r\n\t  } = {}\r\n\t*/\r\n\to || (o={});\r\n\tvar vargs = o.vargs || vrgs(fn),\r\n\t\tk = [], // multiple arg function arg key cache\r\n\t\tcache = new Map(), // single arg function key/value cache\r\n\t\tu, // flag indicating a unary arg function is in use for clear operation\r\n\t\tto, // timeout for clearing cache\r\n\t\td = function(key) { return to = setTimeout(function() {\r\n\t\t\tif(u) {\r\n\t\t\t\tcache.delete(key);\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t// dealing with multi-arg function, c and k are Arrays\r\n\t\t\tk.splice (key,1);\r\n\t\t\t//v.splice(key,1);\r\n\t\t\t},o.maxAge);\r\n\t\t},\r\n\t\tc = o.maxAge>0 && o.maxAge<Infinity ? d : 0, // cache change timeout,\r\n\t\teq = o.equals ? o.equals : 0,\r\n\t\tmaxargs = o.maxArgs,\r\n\t\tsrlz = o.serializer,\r\n\t\tf; // memoized function to return\r\n\tif(fn.length===1 && !o.equals && !vargs) {\r\n\t\t// for single argument functions, just use a Map lookup\r\n\t\tf =  function(a) {\r\n\t\t\t\tif(srlz) a = srlz(a);\r\n\t\t\t\tvar r;\r\n\t\t\t\treturn cache.get(a) || ((!c||c(a)),cache.set(a,r = fn.call(this, a)),r);\r\n\t\t};\r\n\t\tu = 1;\r\n\t}  else if(eq) {\r\n\t// for multiple arg functions, loop through a cache of all the args\r\n\t// looking at each arg separately so a test can abort as soon as possible\r\n\t\tf = function() {\r\n\t\t\tvar l = maxargs||arguments.length, kl = k.length,i = -1;\r\n\t\t\twhile(++i<kl) { // k is an array of arrays of args, each array represents a call signature\r\n\t\t\t\tif (k[i].length === l) { // maxargs!=null ||\r\n\t\t\t\t\tvar j = -1;\r\n\t\t\t\t\twhile ( ++j < l && eq(arguments[j], k[i][j])) {\t}// compare each arg\r\n\t\t\t\t\tif (j === l) return k[i].val //the args matched;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// set change timeout only when new value computed, hits will not push out the tte, but it is arguable they should not\r\n\t\t\tk[i] = arguments;\r\n\t\t\treturn (!c||c(i)),arguments.val = fn.apply(this,k[i]);\r\n\t\t}\r\n\t} else {\r\n\t\tf = function() {\r\n\t\t\tvar l = maxargs||arguments.length, kl = k.length, i = -1;\r\n\t\t\twhile(++i<kl) { // k is an array of arrays of args, each array represents a call signature\r\n\t\t\t\tif (k[i].length === l) { // maxargs!=null ||\r\n\t\t\t\t\tvar j = -1;\r\n\t\t\t\t\twhile (++j < l && arguments[j]===k[i][j]) {\t}// compare each arg\r\n\t\t\t\t\tif (j === l) return k[i].val //the args matched;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// set change timeout only when new value computed, hits will not push out the tte, but it is arguable they should not\r\n\t\t\tk[i] = arguments;\r\n\t\t\treturn (!c||c(i)),arguments.val = fn.apply(this,k[i]);\r\n\t\t}\r\n\t}\r\n\t// reset all the caches\r\n\tf.clear = function() {\r\n\t\tif(to) clearTimeout(to);\r\n\t\tcache.clear();\r\n\t\tk = [];\r\n\t};\r\n\tf.keys = function() { return u ? [...cache.keys()] : k.slice(); };\r\n\tf.values = function() { return u ? [...cache.values()] : k.map((args) => args.val); };\r\n\treturn f;\r\n}\r\nexport {nanomemoize,nanomemoize as default}\r\n\r\n"], "names": [], "version": 3, "file": "index.js.map"}