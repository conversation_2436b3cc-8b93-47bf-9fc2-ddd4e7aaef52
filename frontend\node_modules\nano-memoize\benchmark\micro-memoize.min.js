(function(global,factory){typeof exports==="object"&&typeof module!=="undefined"?module.exports=factory():typeof define==="function"&&define.amd?define(factory):(global=global||self,global["micro-memoize"]=factory())})(this,function(){"use strict";var DEFAULT_OPTIONS_KEYS={isEqual:true,isMatchingKey:true,isPromise:true,maxSize:true,onCacheAdd:true,onCacheChange:true,onCacheHit:true,transformKey:true};function createGetKeyIndex(_a){var isEqual=_a.isEqual,isMatchingKey=_a.isMatchingKey,maxSize=_a.maxSize;if(typeof isMatchingKey==="function"){return function getKeyIndex(allKeys,keyToMatch){if(isMatchingKey(allKeys[0],keyToMatch)){return 0}if(maxSize>1){var keysLength=allKeys.length;for(var index=1;index<keysLength;index++){if(isMatchingKey(allKeys[index],keyToMatch)){return index}}}return-1}}if(maxSize>1){return function getKeyIndex(allKeys,keyToMatch){var keysLength=allKeys.length;var keyLength=keyToMatch.length;var existingKey;for(var index=0;index<keysLength;index++){existingKey=allKeys[index];if(existingKey.length===keyLength){var argIndex=0;for(;argIndex<keyLength;argIndex++){if(!isEqual(existingKey[argIndex],keyToMatch[argIndex])){break}}if(argIndex===keyLength){return index}}}return-1}}return function getKeyIndex(allKeys,keyToMatch){var existingKey=allKeys[0];var keyLength=existingKey.length;if(keyToMatch.length!==keyLength){return-1}for(var index=0;index<keyLength;index++){if(!isEqual(existingKey[index],keyToMatch[index])){return-1}}return 0}}function getCustomOptions(options){var customOptions={};for(var key in options){if(!DEFAULT_OPTIONS_KEYS[key]){customOptions[key]=options[key]}}return customOptions}function isSameValueZero(object1,object2){return object1===object2||object1!==object1&&object2!==object2}function mergeOptions(extraOptions,providedOptions){var target={};for(var key in extraOptions){target[key]=extraOptions[key]}for(var key in providedOptions){target[key]=providedOptions[key]}return target}function orderByLru(cache,newKey,newValue,startingIndex,maxSize){var index=startingIndex;while(index--){cache.keys[index+1]=cache.keys[index];cache.values[index+1]=cache.values[index]}cache.keys[0]=newKey;cache.values[0]=newValue;if(startingIndex>=maxSize){cache.keys.length=maxSize;cache.values.length=maxSize}}function createUpdateAsyncCache(options){var getKeyIndex=createGetKeyIndex(options);var onCacheChange=options.onCacheChange,onCacheHit=options.onCacheHit;var shouldUpdateOnChange=typeof onCacheChange==="function";var shouldUpdateOnHit=typeof onCacheHit==="function";return function(cache,memoized){var key=cache.keys[0];cache.values[0]=cache.values[0].then(function(value){shouldUpdateOnHit&&onCacheHit(cache,options,memoized);shouldUpdateOnChange&&onCacheChange(cache,options,memoized);return value}).catch(function(error){var keyIndex=getKeyIndex(cache.keys,key);if(keyIndex!==-1){cache.keys.splice(keyIndex,1);cache.values.splice(keyIndex,1)}throw error})}}var slice=Array.prototype.slice;var defineProperties=Object.defineProperties;function createMemoizedFunction(fn,options){if(options===void 0){options={}}if(fn.isMemoized){return fn}var _a=options.isEqual,isEqual=_a===void 0?isSameValueZero:_a,isMatchingKey=options.isMatchingKey,_b=options.isPromise,isPromise=_b===void 0?false:_b,_c=options.maxSize,maxSize=_c===void 0?1:_c,onCacheAdd=options.onCacheAdd,onCacheChange=options.onCacheChange,onCacheHit=options.onCacheHit,transformKey=options.transformKey;var normalizedOptions=mergeOptions(getCustomOptions(options),{isEqual:isEqual,isMatchingKey:isMatchingKey,isPromise:isPromise,maxSize:maxSize,onCacheAdd:onCacheAdd,onCacheChange:onCacheChange,onCacheHit:onCacheHit,transformKey:transformKey});var getKeyIndex=createGetKeyIndex(normalizedOptions);var updateAsyncCache=createUpdateAsyncCache(normalizedOptions);var keys=[];var values=[];var cache={keys:keys,get size(){return cache.keys.length},values:values};var canTransformKey=typeof transformKey==="function";var shouldCloneArguments=!!(transformKey||isMatchingKey);var shouldUpdateOnAdd=typeof onCacheAdd==="function";var shouldUpdateOnChange=typeof onCacheChange==="function";var shouldUpdateOnHit=typeof onCacheHit==="function";function memoized(){var normalizedArgs=shouldCloneArguments?slice.call(arguments,0):arguments;var key=canTransformKey?transformKey(normalizedArgs):normalizedArgs;var keyIndex=keys.length?getKeyIndex(keys,key):-1;if(keyIndex!==-1){shouldUpdateOnHit&&onCacheHit(cache,normalizedOptions,memoized);if(keyIndex){orderByLru(cache,keys[keyIndex],values[keyIndex],keyIndex,maxSize);shouldUpdateOnChange&&onCacheChange(cache,normalizedOptions,memoized)}}else{var newValue=fn.apply(this,arguments);var newKey=shouldCloneArguments?key:slice.call(arguments,0);orderByLru(cache,newKey,newValue,keys.length,maxSize);isPromise&&updateAsyncCache(cache,memoized);shouldUpdateOnAdd&&onCacheAdd(cache,normalizedOptions,memoized);shouldUpdateOnChange&&onCacheChange(cache,normalizedOptions,memoized)}return values[0]}defineProperties(memoized,{cache:{configurable:true,value:cache},cacheSnapshot:{configurable:true,get:function(){return{keys:slice.call(cache.keys,0),size:cache.size,values:slice.call(cache.values,0)}}},isMemoized:{configurable:true,value:true},options:{configurable:true,value:normalizedOptions}});return memoized}return createMemoizedFunction});