(function (global, factory) {
	typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
	typeof define === 'function' && define.amd ? define(['exports'], factory) :
	(global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.RTK = {}));
})(this, (function (exports) { 'use strict';

	var t,n,e,r=undefined&&undefined.__extends||(t=function(n,e){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,n){t.__proto__=n;}||function(t,n){for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&(t[e]=n[e]);},t(n,e)},function(n,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=n;}t(n,e),n.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r);}),o=undefined&&undefined.__generator||function(t,n){var e,r,o,i,u={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){return function(i){if(e)throw new TypeError("Generator is already executing.");for(;u;)try{if(e=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return u.label++,{value:i[1],done:!1};case 5:u.label++,r=i[1],i=[0];continue;case 7:i=u.ops.pop(),u.trys.pop();continue;default:if(!((o=(o=u.trys).length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){u=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){u.label=i[1];break}if(6===i[0]&&u.label<o[1]){u.label=o[1],o=i;break}if(o&&u.label<o[2]){u.label=o[2],u.ops.push(i);break}o[2]&&u.ops.pop(),u.trys.pop();continue}i=n.call(t,u);}catch(t){i=[6,t],r=0;}finally{e=o=0;}if(5&i[0])throw i[1];return {value:i[0]?i[1]:void 0,done:!0}}([i,c])}}},i=undefined&&undefined.__spreadArray||function(t,n){for(var e=0,r=n.length,o=t.length;e<r;e++,o++)t[o]=n[e];return t},u=Object.defineProperty,c=Object.defineProperties,a=Object.getOwnPropertyDescriptors,f=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,d=function(t,n,e){return n in t?u(t,n,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[n]=e},p=function(t,n){for(var e in n||(n={}))l.call(n,e)&&d(t,e,n[e]);if(f)for(var r=0,o=f(n);r<o.length;r++)s.call(n,e=o[r])&&d(t,e,n[e]);return t},v=function(t,n){return c(t,a(n))},y=function(t,n,e){return new Promise((function(r,o){var i=function(t){try{c(e.next(t));}catch(t){o(t);}},u=function(t){try{c(e.throw(t));}catch(t){o(t);}},c=function(t){return t.done?r(t.value):Promise.resolve(t.value).then(i,u)};c((e=e.apply(t,n)).next());}))};function h(t){for(var n=arguments.length,e=Array(n>1?n-1:0),r=1;r<n;r++)e[r-1]=arguments[r];throw Error("[Immer] minified error nr: "+t+(e.length?" "+e.map((function(t){return "'"+t+"'"})).join(","):"")+". Find the full error at: https://bit.ly/3cXEKWf")}function b(t){return !!t&&!!t[et]}function g(t){var n;return !!t&&(function(t){if(!t||"object"!=typeof t)return !1;var n=Object.getPrototypeOf(t);if(null===n)return !0;var e=Object.hasOwnProperty.call(n,"constructor")&&n.constructor;return e===Object||"function"==typeof e&&Function.toString.call(e)===rt}(t)||Array.isArray(t)||!!t[nt]||!!(null===(n=t.constructor)||void 0===n?void 0:n[nt])||A(t)||S(t))}function m(t){return b(t)||h(23,t),t[et].t}function w(t,n,e){void 0===e&&(e=!1),0===O(t)?(e?Object.keys:ot)(t).forEach((function(r){e&&"symbol"==typeof r||n(r,t[r],t);})):t.forEach((function(e,r){return n(r,e,t)}));}function O(t){var n=t[et];return n?n.i>3?n.i-4:n.i:Array.isArray(t)?1:A(t)?2:S(t)?3:0}function j(t,n){return 2===O(t)?t.has(n):Object.prototype.hasOwnProperty.call(t,n)}function P(t,n,e){var r=O(t);2===r?t.set(n,e):3===r?t.add(e):t[n]=e;}function E(t,n){return t===n?0!==t||1/t==1/n:t!=t&&n!=n}function A(t){return Q&&t instanceof Map}function S(t){return Y&&t instanceof Set}function _(t){return t.o||t.t}function k(t){if(Array.isArray(t))return Array.prototype.slice.call(t);var n=it(t);delete n[et];for(var e=ot(n),r=0;r<e.length;r++){var o=e[r],i=n[o];!1===i.writable&&(i.writable=!0,i.configurable=!0),(i.get||i.set)&&(n[o]={configurable:!0,writable:!0,enumerable:i.enumerable,value:t[o]});}return Object.create(Object.getPrototypeOf(t),n)}function x(t,n){return void 0===n&&(n=!1),N(t)||b(t)||!g(t)||(O(t)>1&&(t.set=t.add=t.clear=t.delete=I),Object.freeze(t),n&&w(t,(function(t,n){return x(n,!0)}),!0)),t}function I(){h(2);}function N(t){return null==t||"object"!=typeof t||Object.isFrozen(t)}function R(t){var n=ut[t];return n||h(18,t),n}function T(){return e}function C(t,n){n&&(R("Patches"),t.u=[],t.s=[],t.v=n);}function D(t){M(t),t.p.forEach(F),t.p=null;}function M(t){t===e&&(e=t.l);}function q(t){return e={p:[],l:e,h:t,m:!0,_:0}}function F(t){var n=t[et];0===n.i||1===n.i?n.j():n.g=!0;}function z(t,n){n._=n.p.length;var e=n.p[0],r=void 0!==t&&t!==e;return n.h.O||R("ES5").S(n,t,r),r?(e[et].P&&(D(n),h(4)),g(t)&&(t=L(n,t),n.l||K(n,t)),n.u&&R("Patches").M(e[et].t,t,n.u,n.s)):t=L(n,e,[]),D(n),n.u&&n.v(n.u,n.s),t!==tt?t:void 0}function L(t,n,e){if(N(n))return n;var r=n[et];if(!r)return w(n,(function(o,i){return U(t,r,n,o,i,e)}),!0),n;if(r.A!==t)return n;if(!r.P)return K(t,r.t,!0),r.t;if(!r.I){r.I=!0,r.A._--;var o=4===r.i||5===r.i?r.o=k(r.k):r.o,i=o,u=!1;3===r.i&&(i=new Set(o),o.clear(),u=!0),w(i,(function(n,i){return U(t,r,o,n,i,e,u)})),K(t,o,!1),e&&t.u&&R("Patches").N(r,e,t.u,t.s);}return r.o}function U(t,n,e,r,o,i,u){if(b(o)){var c=L(t,o,i&&n&&3!==n.i&&!j(n.R,r)?i.concat(r):void 0);if(P(e,r,c),!b(c))return;t.m=!1;}else u&&e.add(o);if(g(o)&&!N(o)){if(!t.h.D&&t._<1)return;L(t,o),n&&n.A.l||K(t,o);}}function K(t,n,e){void 0===e&&(e=!1),!t.l&&t.h.D&&t.m&&x(n,e);}function W(t,n){var e=t[et];return (e?_(e):t)[n]}function B(t,n){if(n in t)for(var e=Object.getPrototypeOf(t);e;){var r=Object.getOwnPropertyDescriptor(e,n);if(r)return r;e=Object.getPrototypeOf(e);}}function V(t){t.P||(t.P=!0,t.l&&V(t.l));}function X(t){t.o||(t.o=k(t.t));}function G(t,n,e){var r=A(n)?R("MapSet").F(n,e):S(n)?R("MapSet").T(n,e):t.O?function(t,n){var e=Array.isArray(t),r={i:e?1:0,A:n?n.A:T(),P:!1,I:!1,R:{},l:n,t:t,k:null,o:null,j:null,C:!1},o=r,i=ct;e&&(o=[r],i=at);var u=Proxy.revocable(o,i),c=u.revoke,a=u.proxy;return r.k=a,r.j=c,a}(n,e):R("ES5").J(n,e);return (e?e.A:T()).p.push(r),r}function J(t){return b(t)||h(22,t),function t(n){if(!g(n))return n;var e,r=n[et],o=O(n);if(r){if(!r.P&&(r.i<4||!R("ES5").K(r)))return r.t;r.I=!0,e=$(n,o),r.I=!1;}else e=$(n,o);return w(e,(function(n,o){r&&function(t,n){return 2===O(t)?t.get(n):t[n]}(r.t,n)===o||P(e,n,t(o));})),3===o?new Set(e):e}(t)}function $(t,n){switch(n){case 2:return new Map(t);case 3:return Array.from(t)}return k(t)}var H="undefined"!=typeof Symbol&&"symbol"==typeof Symbol("x"),Q="undefined"!=typeof Map,Y="undefined"!=typeof Set,Z="undefined"!=typeof Proxy&&void 0!==Proxy.revocable&&"undefined"!=typeof Reflect,tt=H?Symbol.for("immer-nothing"):((n={})["immer-nothing"]=!0,n),nt=H?Symbol.for("immer-draftable"):"__$immer_draftable",et=H?Symbol.for("immer-state"):"__$immer_state",rt=(""+Object.prototype.constructor),ot="undefined"!=typeof Reflect&&Reflect.ownKeys?Reflect.ownKeys:void 0!==Object.getOwnPropertySymbols?function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:Object.getOwnPropertyNames,it=Object.getOwnPropertyDescriptors||function(t){var n={};return ot(t).forEach((function(e){n[e]=Object.getOwnPropertyDescriptor(t,e);})),n},ut={},ct={get:function(t,n){if(n===et)return t;var e,r,o,i=_(t);if(!j(i,n))return e=t,(o=B(i,n))?"value"in o?o.value:null===(r=o.get)||void 0===r?void 0:r.call(e.k):void 0;var u=i[n];return t.I||!g(u)?u:u===W(t.t,n)?(X(t),t.o[n]=G(t.A.h,u,t)):u},has:function(t,n){return n in _(t)},ownKeys:function(t){return Reflect.ownKeys(_(t))},set:function(t,n,e){var r=B(_(t),n);if(null==r?void 0:r.set)return r.set.call(t.k,e),!0;if(!t.P){var o=W(_(t),n),i=null==o?void 0:o[et];if(i&&i.t===e)return t.o[n]=e,t.R[n]=!1,!0;if(E(e,o)&&(void 0!==e||j(t.t,n)))return !0;X(t),V(t);}return t.o[n]===e&&(void 0!==e||n in t.o)||Number.isNaN(e)&&Number.isNaN(t.o[n])||(t.o[n]=e,t.R[n]=!0),!0},deleteProperty:function(t,n){return void 0!==W(t.t,n)||n in t.t?(t.R[n]=!1,X(t),V(t)):delete t.R[n],t.o&&delete t.o[n],!0},getOwnPropertyDescriptor:function(t,n){var e=_(t),r=Reflect.getOwnPropertyDescriptor(e,n);return r?{writable:!0,configurable:1!==t.i||"length"!==n,enumerable:r.enumerable,value:e[n]}:r},defineProperty:function(){h(11);},getPrototypeOf:function(t){return Object.getPrototypeOf(t.t)},setPrototypeOf:function(){h(12);}},at={};w(ct,(function(t,n){at[t]=function(){return arguments[0]=arguments[0][0],n.apply(this,arguments)};})),at.deleteProperty=function(t,n){return at.set.call(this,t,n,void 0)},at.set=function(t,n,e){return ct.set.call(this,t[0],n,e,t[0])};var ft=new(function(){function t(t){var n=this;this.O=Z,this.D=!0,this.produce=function(t,e,r){if("function"==typeof t&&"function"!=typeof e){var o=e;e=t;var i=n;return function(t){var n=this;void 0===t&&(t=o);for(var r=arguments.length,u=Array(r>1?r-1:0),c=1;c<r;c++)u[c-1]=arguments[c];return i.produce(t,(function(t){var r;return (r=e).call.apply(r,[n,t].concat(u))}))}}var u;if("function"!=typeof e&&h(6),void 0!==r&&"function"!=typeof r&&h(7),g(t)){var c=q(n),a=G(n,t,void 0),f=!0;try{u=e(a),f=!1;}finally{f?D(c):M(c);}return "undefined"!=typeof Promise&&u instanceof Promise?u.then((function(t){return C(c,r),z(t,c)}),(function(t){throw D(c),t})):(C(c,r),z(u,c))}if(!t||"object"!=typeof t){if(void 0===(u=e(t))&&(u=t),u===tt&&(u=void 0),n.D&&x(u,!0),r){var l=[],s=[];R("Patches").M(t,u,l,s),r(l,s);}return u}h(21,t);},this.produceWithPatches=function(t,e){if("function"==typeof t)return function(e){for(var r=arguments.length,o=Array(r>1?r-1:0),i=1;i<r;i++)o[i-1]=arguments[i];return n.produceWithPatches(e,(function(n){return t.apply(void 0,[n].concat(o))}))};var r,o,i=n.produce(t,e,(function(t,n){r=t,o=n;}));return "undefined"!=typeof Promise&&i instanceof Promise?i.then((function(t){return [t,r,o]})):[i,r,o]},"boolean"==typeof(null==t?void 0:t.useProxies)&&this.setUseProxies(t.useProxies),"boolean"==typeof(null==t?void 0:t.autoFreeze)&&this.setAutoFreeze(t.autoFreeze);}var n=t.prototype;return n.createDraft=function(t){g(t)||h(8),b(t)&&(t=J(t));var n=q(this),e=G(this,t,void 0);return e[et].C=!0,M(n),e},n.finishDraft=function(t,n){var e=(t&&t[et]).A;return C(e,n),z(void 0,e)},n.setAutoFreeze=function(t){this.D=t;},n.setUseProxies=function(t){t&&!Z&&h(20),this.O=t;},n.applyPatches=function(t,n){var e;for(e=n.length-1;e>=0;e--){var r=n[e];if(0===r.path.length&&"replace"===r.op){t=r.value;break}}e>-1&&(n=n.slice(e+1));var o=R("Patches").$;return b(t)?o(t,n):this.produce(t,(function(t){return o(t,n)}))},t}()),lt=ft.produce,st=(ft.produceWithPatches.bind(ft),ft.setAutoFreeze.bind(ft),ft.setUseProxies.bind(ft),ft.applyPatches.bind(ft),ft.createDraft.bind(ft),ft.finishDraft.bind(ft),lt);function dt(t,n,e){return n in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}function pt(t,n){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),e.push.apply(e,r);}return e}function vt(t){for(var n=1;n<arguments.length;n++){var e=null!=arguments[n]?arguments[n]:{};n%2?pt(Object(e),!0).forEach((function(n){dt(t,n,e[n]);})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):pt(Object(e)).forEach((function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n));}));}return t}function yt(t){return "Minified Redux error #"+t+"; visit https://redux.js.org/Errors?code="+t+" for the full message or use the non-minified dev environment for full errors. "}var ht="function"==typeof Symbol&&Symbol.observable||"@@observable",bt=function(){return Math.random().toString(36).substring(7).split("").join(".")},gt={INIT:"@@redux/INIT"+bt(),REPLACE:"@@redux/REPLACE"+bt(),PROBE_UNKNOWN_ACTION:function(){return "@@redux/PROBE_UNKNOWN_ACTION"+bt()}};function mt(t){if("object"!=typeof t||null===t)return !1;for(var n=t;null!==Object.getPrototypeOf(n);)n=Object.getPrototypeOf(n);return Object.getPrototypeOf(t)===n}function wt(t,n,e){var r;if("function"==typeof n&&"function"==typeof e||"function"==typeof e&&"function"==typeof arguments[3])throw new Error(yt(0));if("function"==typeof n&&void 0===e&&(e=n,n=void 0),void 0!==e){if("function"!=typeof e)throw new Error(yt(1));return e(wt)(t,n)}if("function"!=typeof t)throw new Error(yt(2));var o=t,i=n,u=[],c=u,a=!1;function f(){c===u&&(c=u.slice());}function l(){if(a)throw new Error(yt(3));return i}function s(t){if("function"!=typeof t)throw new Error(yt(4));if(a)throw new Error(yt(5));var n=!0;return f(),c.push(t),function(){if(n){if(a)throw new Error(yt(6));n=!1,f();var e=c.indexOf(t);c.splice(e,1),u=null;}}}function d(t){if(!mt(t))throw new Error(yt(7));if(void 0===t.type)throw new Error(yt(8));if(a)throw new Error(yt(9));try{a=!0,i=o(i,t);}finally{a=!1;}for(var n=u=c,e=0;e<n.length;e++)(0, n[e])();return t}function p(t){if("function"!=typeof t)throw new Error(yt(10));o=t,d({type:gt.REPLACE});}function v(){var t,n=s;return (t={subscribe:function(t){if("object"!=typeof t||null===t)throw new Error(yt(11));function e(){t.next&&t.next(l());}return e(),{unsubscribe:n(e)}}})[ht]=function(){return this},t}return d({type:gt.INIT}),(r={dispatch:d,subscribe:s,getState:l,replaceReducer:p})[ht]=v,r}var Ot=wt;function jt(t){for(var n=Object.keys(t),e={},r=0;r<n.length;r++){var o=n[r];"function"==typeof t[o]&&(e[o]=t[o]);}var i,u=Object.keys(e);try{!function(t){Object.keys(t).forEach((function(n){var e=t[n];if(void 0===e(void 0,{type:gt.INIT}))throw new Error(yt(12));if(void 0===e(void 0,{type:gt.PROBE_UNKNOWN_ACTION()}))throw new Error(yt(13))}));}(e);}catch(t){i=t;}return function(t,n){if(void 0===t&&(t={}),i)throw i;for(var r=!1,o={},c=0;c<u.length;c++){var a=u[c],f=t[a],l=(0, e[a])(f,n);if(void 0===l)throw new Error(yt(14));o[a]=l,r=r||l!==f;}return (r=r||u.length!==Object.keys(t).length)?o:t}}function Pt(t,n){return function(){return n(t.apply(this,arguments))}}function Et(t,n){if("function"==typeof t)return Pt(t,n);if("object"!=typeof t||null===t)throw new Error(yt(16));var e={};for(var r in t){var o=t[r];"function"==typeof o&&(e[r]=Pt(o,n));}return e}function At(){for(var t=arguments.length,n=new Array(t),e=0;e<t;e++)n[e]=arguments[e];return 0===n.length?function(t){return t}:1===n.length?n[0]:n.reduce((function(t,n){return function(){return t(n.apply(void 0,arguments))}}))}function St(){for(var t=arguments.length,n=new Array(t),e=0;e<t;e++)n[e]=arguments[e];return function(t){return function(){var e=t.apply(void 0,arguments),r=function(){throw new Error(yt(15))},o={getState:e.getState,dispatch:function(){return r.apply(void 0,arguments)}},i=n.map((function(t){return t(o)}));return r=At.apply(void 0,i)(e.dispatch),vt(vt({},e),{},{dispatch:r})}}}var _t=function(t,n){return t===n};function kt(t,n){var e,r,o,i="object"==typeof n?n:{equalityCheck:n},u=i.equalityCheck,c=i.maxSize,a=void 0===c?1:c,f=i.resultEqualityCheck,l=(o=void 0===u?_t:u,function(t,n){if(null===t||null===n||t.length!==n.length)return !1;for(var e=t.length,r=0;r<e;r++)if(!o(t[r],n[r]))return !1;return !0}),s=1===a?(e=l,{get:function(t){return r&&e(r.key,t)?r.value:"NOT_FOUND"},put:function(t,n){r={key:t,value:n};},getEntries:function(){return r?[r]:[]},clear:function(){r=void 0;}}):function(t,n){var e=[];function r(t){var r=e.findIndex((function(e){return n(t,e.key)}));if(r>-1){var o=e[r];return r>0&&(e.splice(r,1),e.unshift(o)),o.value}return "NOT_FOUND"}return {get:r,put:function(n,o){"NOT_FOUND"===r(n)&&(e.unshift({key:n,value:o}),e.length>t&&e.pop());},getEntries:function(){return e},clear:function(){e=[];}}}(a,l);function d(){var n=s.get(arguments);if("NOT_FOUND"===n){if(n=t.apply(null,arguments),f){var e=s.getEntries(),r=e.find((function(t){return f(t.value,n)}));r&&(n=r.value);}s.put(arguments,n);}return n}return d.clearCache=function(){return s.clear()},d}function xt(t){var n=Array.isArray(t[0])?t[0]:t;if(!n.every((function(t){return "function"==typeof t}))){var e=n.map((function(t){return "function"==typeof t?"function "+(t.name||"unnamed")+"()":typeof t})).join(", ");throw new Error("createSelector expects all input-selectors to be functions, but received the following types: ["+e+"]")}return n}function It(t){for(var n=arguments.length,e=new Array(n>1?n-1:0),r=1;r<n;r++)e[r-1]=arguments[r];var o=function(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];var i,u=0,c={memoizeOptions:void 0},a=r.pop();if("object"==typeof a&&(c=a,a=r.pop()),"function"!=typeof a)throw new Error("createSelector expects an output function after the inputs, but received: ["+typeof a+"]");var f=c,l=f.memoizeOptions,s=void 0===l?e:l,d=Array.isArray(s)?s:[s],p=xt(r),v=t.apply(void 0,[function(){return u++,a.apply(null,arguments)}].concat(d)),y=t((function(){for(var t=[],n=p.length,e=0;e<n;e++)t.push(p[e].apply(null,arguments));return i=v.apply(null,t)}));return Object.assign(y,{resultFunc:a,memoizedResultFunc:v,dependencies:p,lastResult:function(){return i},recomputations:function(){return u},resetRecomputations:function(){return u=0}}),y};return o}var Nt=It(kt),Rt=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var e=Nt.apply(void 0,t),r=function(t){for(var n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];return e.apply(void 0,i([b(t)?J(t):t],n))};return r},Tt="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!==arguments.length)return "object"==typeof arguments[0]?At:At.apply(null,arguments)};function Ct(t){if("object"!=typeof t||null===t)return !1;var n=Object.getPrototypeOf(t);if(null===n)return !0;for(var e=n;null!==Object.getPrototypeOf(e);)e=Object.getPrototypeOf(e);return n===e}function Dt(t){return function(n){var e=n.dispatch,r=n.getState;return function(n){return function(o){return "function"==typeof o?o(e,r,t):n(o)}}}}var Mt=Dt();Mt.withExtraArgument=Dt;var qt=Mt,Ft=function(t){return t&&"function"==typeof t.match};function zt(t,n){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];if(n){var o=n.apply(void 0,e);if(!o)throw new Error("prepareAction did not return an object");return p(p({type:t,payload:o.payload},"meta"in o&&{meta:o.meta}),"error"in o&&{error:o.error})}return {type:t,payload:e[0]}}return e.toString=function(){return ""+t},e.type=t,e.match=function(n){return n.type===t},e}function Lt(t){return Ct(t)&&"type"in t}function Ut(t){return "function"==typeof t&&"type"in t&&Ft(t)}function Kt(t){return Lt(t)&&"string"==typeof t.type&&Object.keys(t).every(Wt)}function Wt(t){return ["type","payload","error","meta"].indexOf(t)>-1}function Bt(t){return ""+t}function Vt(t){return function(){return function(t){return function(n){return t(n)}}}}var Xt=function(t){function n(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,e)||this;return Object.setPrototypeOf(o,n.prototype),o}return r(n,t),Object.defineProperty(n,Symbol.species,{get:function(){return n},enumerable:!1,configurable:!0}),n.prototype.concat=function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t.prototype.concat.apply(this,n)},n.prototype.prepend=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return 1===t.length&&Array.isArray(t[0])?new(n.bind.apply(n,i([void 0],t[0].concat(this)))):new(n.bind.apply(n,i([void 0],t.concat(this))))},n}(Array),Gt=function(t){function n(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,e)||this;return Object.setPrototypeOf(o,n.prototype),o}return r(n,t),Object.defineProperty(n,Symbol.species,{get:function(){return n},enumerable:!1,configurable:!0}),n.prototype.concat=function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t.prototype.concat.apply(this,n)},n.prototype.prepend=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return 1===t.length&&Array.isArray(t[0])?new(n.bind.apply(n,i([void 0],t[0].concat(this)))):new(n.bind.apply(n,i([void 0],t.concat(this))))},n}(Array);function Jt(t){return g(t)?st(t,(function(){})):t}function $t(t){var n=typeof t;return null==t||"string"===n||"boolean"===n||"number"===n||Array.isArray(t)||Ct(t)}function Ht(t,n,e,r,o,i){var u;if(void 0===n&&(n=""),void 0===e&&(e=$t),void 0===o&&(o=[]),!e(t))return {keyPath:n||"<root>",value:t};if("object"!=typeof t||null===t)return !1;if(null==i?void 0:i.has(t))return !1;for(var c=null!=r?r(t):Object.entries(t),a=o.length>0,f=function(t,c){var f=n?n+"."+t:t;return a&&o.some((function(t){return t instanceof RegExp?t.test(f):f===t}))?"continue":e(c)?"object"==typeof c&&(u=Ht(c,f,e,r,o,i))?{value:u}:void 0:{value:{keyPath:f,value:c}}},l=0,s=c;l<s.length;l++){var d=s[l],p=f(d[0],d[1]);if("object"==typeof p)return p.value}return i&&Qt(t)&&i.add(t),!1}function Qt(t){if(!Object.isFrozen(t))return !1;for(var n=0,e=Object.values(t);n<e.length;n++){var r=e[n];if("object"==typeof r&&null!==r&&!Qt(r))return !1}return !0}function Yt(t){return function(){return function(t){return function(n){return t(n)}}}}function Zt(t){void 0===t&&(t={});var n=t.thunk,e=void 0===n||n,r=new Xt;return e&&r.push("boolean"==typeof e?qt:qt.withExtraArgument(e.extraArgument)),r}function tn(t){var n,e=function(t){return Zt(t)},r=t||{},o=r.reducer,u=void 0===o?void 0:o,c=r.middleware,a=void 0===c?e():c,f=r.devTools,l=void 0===f||f,s=r.preloadedState,d=void 0===s?void 0:s,v=r.enhancers,y=void 0===v?void 0:v;if("function"==typeof u)n=u;else {if(!Ct(u))throw new Error('"reducer" is a required argument, and must be a function or an object of functions that can be passed to combineReducers');n=jt(u);}var h=a;"function"==typeof h&&(h=h(e));var b=St.apply(void 0,h),g=At;l&&(g=Tt(p({trace:!1},"object"==typeof l&&l)));var m=new Gt(b),w=m;return Array.isArray(y)?w=i([b],y):"function"==typeof y&&(w=y(m)),wt(n,d,g.apply(void 0,w))}function nn(t){var n,e={},r=[],o={addCase:function(t,n){var r="string"==typeof t?t:t.type;if(!r)throw new Error("`builder.addCase` cannot be called with an empty action type");if(r in e)throw new Error("`builder.addCase` cannot be called with two reducers for the same action type");return e[r]=n,o},addMatcher:function(t,n){return r.push({matcher:t,reducer:n}),o},addDefaultCase:function(t){return n=t,o}};return t(o),[e,r,n]}function en(t,n,e,r){void 0===e&&(e=[]);var o,u="function"==typeof n?nn(n):[n,e,r],c=u[0],a=u[1],f=u[2];if("function"==typeof t)o=function(){return Jt(t())};else {var l=Jt(t);o=function(){return l};}function s(t,n){void 0===t&&(t=o());var e=i([c[n.type]],a.filter((function(t){return (0, t.matcher)(n)})).map((function(t){return t.reducer})));return 0===e.filter((function(t){return !!t})).length&&(e=[f]),e.reduce((function(t,e){if(e){var r;if(b(t))return void 0===(r=e(t,n))?t:r;if(g(t))return st(t,(function(t){return e(t,n)}));if(void 0===(r=e(t,n))){if(null===t)return t;throw Error("A case reducer on a non-draftable value must not return undefined")}return r}return t}),t)}return s.getInitialState=o,s}function rn(t){var n=t.name;if(!n)throw new Error("`name` is a required option for createSlice");var e,r="function"==typeof t.initialState?t.initialState:Jt(t.initialState),o=t.reducers||{},i=Object.keys(o),u={},c={},a={};function f(){var n="function"==typeof t.extraReducers?nn(t.extraReducers):[t.extraReducers],e=n[0],o=n[1],i=void 0===o?[]:o,u=n[2],a=void 0===u?void 0:u,f=p(p({},void 0===e?{}:e),c);return en(r,(function(t){for(var n in f)t.addCase(n,f[n]);for(var e=0,r=i;e<r.length;e++){var o=r[e];t.addMatcher(o.matcher,o.reducer);}a&&t.addDefaultCase(a);}))}return i.forEach((function(t){var e,r,i=o[t],f=n+"/"+t;"reducer"in i?(e=i.reducer,r=i.prepare):e=i,u[t]=e,c[f]=e,a[t]=r?zt(f,r):zt(f);})),{name:n,reducer:function(t,n){return e||(e=f()),e(t,n)},actions:a,caseReducers:u,getInitialState:function(){return e||(e=f()),e.getInitialState()}}}function on(t){return "object"!=typeof t||null==t||Object.isFrozen(t)}function un(t){return function(){return function(t){return function(n){return t(n)}}}}function cn(t){return function(n,e){var r=function(n){Kt(e)?t(e.payload,n):t(e,n);};return b(n)?(r(n),n):st(n,r)}}function an(t,n){return n(t)}function fn(t){return Array.isArray(t)||(t=Object.values(t)),t}function ln(t,n,e){for(var r=[],o=[],i=0,u=t=fn(t);i<u.length;i++){var c=u[i],a=an(c,n);a in e.entities?o.push({id:a,changes:c}):r.push(c);}return [r,o]}function sn(t){function n(n,e){var r=an(n,t);r in e.entities||(e.ids.push(r),e.entities[r]=n);}function e(t,e){for(var r=0,o=t=fn(t);r<o.length;r++)n(o[r],e);}function r(n,e){var r=an(n,t);r in e.entities||e.ids.push(r),e.entities[r]=n;}function o(t,n){var e=!1;t.forEach((function(t){t in n.entities&&(delete n.entities[t],e=!0);})),e&&(n.ids=n.ids.filter((function(t){return t in n.entities})));}function i(n,e){var r={},o={};if(n.forEach((function(t){t.id in e.entities&&(o[t.id]={id:t.id,changes:p(p({},o[t.id]?o[t.id].changes:null),t.changes)});})),(n=Object.values(o)).length>0){var i=n.filter((function(n){return function(n,e,r){var o=Object.assign({},r.entities[e.id],e.changes),i=an(o,t),u=i!==e.id;return u&&(n[e.id]=i,delete r.entities[e.id]),r.entities[i]=o,u}(r,n,e)})).length>0;i&&(e.ids=Object.keys(e.entities));}}function u(n,r){var o=ln(n,t,r),u=o[0];i(o[1],r),e(u,r);}return {removeAll:(c=function(t){Object.assign(t,{ids:[],entities:{}});},a=cn((function(t,n){return c(n)})),function(t){return a(t,void 0)}),addOne:cn(n),addMany:cn(e),setOne:cn(r),setMany:cn((function(t,n){for(var e=0,o=t=fn(t);e<o.length;e++)r(o[e],n);})),setAll:cn((function(t,n){t=fn(t),n.ids=[],n.entities={},e(t,n);})),updateOne:cn((function(t,n){return i([t],n)})),updateMany:cn(i),upsertOne:cn((function(t,n){return u([t],n)})),upsertMany:cn(u),removeOne:cn((function(t,n){return o([t],n)})),removeMany:cn(o)};var c,a;}function dn(t){void 0===t&&(t={});var n=p({sortComparer:!1,selectId:function(t){return t.id}},t),e=n.selectId,r=n.sortComparer,o={getInitialState:function(t){return void 0===t&&(t={}),Object.assign({ids:[],entities:{}},t)}},i={getSelectors:function(t){var n=function(t){return t.ids},e=function(t){return t.entities},r=Rt(n,e,(function(t,n){return t.map((function(t){return n[t]}))})),o=function(t,n){return n},i=function(t,n){return t[n]},u=Rt(n,(function(t){return t.length}));if(!t)return {selectIds:n,selectEntities:e,selectAll:r,selectTotal:u,selectById:Rt(e,o,i)};var c=Rt(t,e);return {selectIds:Rt(t,n),selectEntities:c,selectAll:Rt(t,r),selectTotal:Rt(t,u),selectById:Rt(c,o,i)}}},u=r?function(t,n){var e=sn(t);function r(n,e){var r=(n=fn(n)).filter((function(n){return !(an(n,t)in e.entities)}));0!==r.length&&c(r,e);}function o(t,n){0!==(t=fn(t)).length&&c(t,n);}function i(n,e){for(var r=!1,o=0,i=n;o<i.length;o++){var u=i[o],c=e.entities[u.id];if(c){r=!0,Object.assign(c,u.changes);var f=t(c);u.id!==f&&(delete e.entities[u.id],e.entities[f]=c);}}r&&a(e);}function u(n,e){var o=ln(n,t,e),u=o[0];i(o[1],e),r(u,e);}function c(n,e){n.forEach((function(n){e.entities[t(n)]=n;})),a(e);}function a(e){var r=Object.values(e.entities);r.sort(n);var o=r.map(t);(function(t,n){if(t.length!==n.length)return !1;for(var e=0;e<t.length&&e<n.length;e++)if(t[e]!==n[e])return !1;return !0})(e.ids,o)||(e.ids=o);}return {removeOne:e.removeOne,removeMany:e.removeMany,removeAll:e.removeAll,addOne:cn((function(t,n){return r([t],n)})),updateOne:cn((function(t,n){return i([t],n)})),upsertOne:cn((function(t,n){return u([t],n)})),setOne:cn((function(t,n){return o([t],n)})),setMany:cn(o),setAll:cn((function(t,n){t=fn(t),n.entities={},n.ids=[],r(t,n);})),addMany:cn(r),updateMany:cn(i),upsertMany:cn(u)}}(e,r):sn(e);return p(p(p({selectId:e,sortComparer:r},o),i),u)}var pn=function(t){void 0===t&&(t=21);for(var n="",e=t;e--;)n+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return n},vn=["name","message","stack","code"],yn=function(t,n){this.payload=t,this.meta=n;},hn=function(t,n){this.payload=t,this.meta=n;},bn=function(t){if("object"==typeof t&&null!==t){for(var n={},e=0,r=vn;e<r.length;e++){var o=r[e];"string"==typeof t[o]&&(n[o]=t[o]);}return n}return {message:String(t)}},gn=function(){function t(t,n,e){var r=zt(t+"/fulfilled",(function(t,n,e,r){return {payload:t,meta:v(p({},r||{}),{arg:e,requestId:n,requestStatus:"fulfilled"})}})),i=zt(t+"/pending",(function(t,n,e){return {payload:void 0,meta:v(p({},e||{}),{arg:n,requestId:t,requestStatus:"pending"})}})),u=zt(t+"/rejected",(function(t,n,r,o,i){return {payload:o,error:(e&&e.serializeError||bn)(t||"Rejected"),meta:v(p({},i||{}),{arg:r,requestId:n,rejectedWithValue:!!o,requestStatus:"rejected",aborted:"AbortError"===(null==t?void 0:t.name),condition:"ConditionError"===(null==t?void 0:t.name)})}})),c="undefined"!=typeof AbortController?AbortController:function(){function t(){this.signal={aborted:!1,addEventListener:function(){},dispatchEvent:function(){return !1},onabort:function(){},removeEventListener:function(){},reason:void 0,throwIfAborted:function(){}};}return t.prototype.abort=function(){},t}();return Object.assign((function(t){return function(a,f,l){var s,d=(null==e?void 0:e.idGenerator)?e.idGenerator(t):pn(),p=new c;function v(t){s=t,p.abort();}var h=function(){return y(this,null,(function(){var c,y,h,b,g,m;return o(this,(function(o){switch(o.label){case 0:return o.trys.push([0,4,,5]),null===(w=b=null==(c=null==e?void 0:e.condition)?void 0:c.call(e,t,{getState:f,extra:l}))||"object"!=typeof w||"function"!=typeof w.then?[3,2]:[4,b];case 1:b=o.sent(),o.label=2;case 2:if(!1===b||p.signal.aborted)throw {name:"ConditionError",message:"Aborted due to condition callback returning false."};return g=new Promise((function(t,n){return p.signal.addEventListener("abort",(function(){return n({name:"AbortError",message:s||"Aborted"})}))})),a(i(d,t,null==(y=null==e?void 0:e.getPendingMeta)?void 0:y.call(e,{requestId:d,arg:t},{getState:f,extra:l}))),[4,Promise.race([g,Promise.resolve(n(t,{dispatch:a,getState:f,extra:l,requestId:d,signal:p.signal,abort:v,rejectWithValue:function(t,n){return new yn(t,n)},fulfillWithValue:function(t,n){return new hn(t,n)}})).then((function(n){if(n instanceof yn)throw n;return n instanceof hn?r(n.payload,d,t,n.meta):r(n,d,t)}))])];case 3:return h=o.sent(),[3,5];case 4:return m=o.sent(),h=m instanceof yn?u(null,d,t,m.payload,m.meta):u(m,d,t),[3,5];case 5:return e&&!e.dispatchConditionRejection&&u.match(h)&&h.meta.condition||a(h),[2,h]}var w;}))}))}();return Object.assign(h,{abort:v,requestId:d,arg:t,unwrap:function(){return h.then(mn)}})}}),{pending:i,rejected:u,fulfilled:r,typePrefix:t})}return t.withTypes=function(){return t},t}();function mn(t){if(t.meta&&t.meta.rejectedWithValue)throw t.payload;if(t.error)throw t.error;return t.payload}var wn=function(t,n){return Ft(t)?t.match(n):t(n)};function On(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return function(n){return t.some((function(t){return wn(t,n)}))}}function jn(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return function(n){return t.every((function(t){return wn(t,n)}))}}function Pn(t,n){if(!t||!t.meta)return !1;var e="string"==typeof t.meta.requestId,r=n.indexOf(t.meta.requestStatus)>-1;return e&&r}function En(t){return "function"==typeof t[0]&&"pending"in t[0]&&"fulfilled"in t[0]&&"rejected"in t[0]}function An(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return 0===t.length?function(t){return Pn(t,["pending"])}:En(t)?function(n){var e=t.map((function(t){return t.pending}));return On.apply(void 0,e)(n)}:An()(t[0])}function Sn(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return 0===t.length?function(t){return Pn(t,["rejected"])}:En(t)?function(n){var e=t.map((function(t){return t.rejected}));return On.apply(void 0,e)(n)}:Sn()(t[0])}function _n(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var e=function(t){return t&&t.meta&&t.meta.rejectedWithValue};return 0===t.length||En(t)?function(n){return jn(Sn.apply(void 0,t),e)(n)}:_n()(t[0])}function kn(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return 0===t.length?function(t){return Pn(t,["fulfilled"])}:En(t)?function(n){var e=t.map((function(t){return t.fulfilled}));return On.apply(void 0,e)(n)}:kn()(t[0])}function xn(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return 0===t.length?function(t){return Pn(t,["pending","fulfilled","rejected"])}:En(t)?function(n){for(var e=[],r=0,o=t;r<o.length;r++){var i=o[r];e.push(i.pending,i.rejected,i.fulfilled);}return On.apply(void 0,e)(n)}:xn()(t[0])}var In=function(t,n){if("function"!=typeof t)throw new TypeError(n+" is not a function")},Nn=function(){},Rn=function(t,n){return void 0===n&&(n=Nn),t.catch(n),t},Tn=function(t,n){return t.addEventListener("abort",n,{once:!0}),function(){return t.removeEventListener("abort",n)}},Cn=function(t,n){var e=t.signal;e.aborted||("reason"in e||Object.defineProperty(e,"reason",{enumerable:!0,value:n,configurable:!0,writable:!0}),t.abort(n));},Dn=function(t){this.code=t,this.name="TaskAbortError",this.message="task cancelled (reason: "+t+")";},Mn=function(t){if(t.aborted)throw new Dn(t.reason)};function qn(t,n){var e=Nn;return new Promise((function(r,o){var i=function(){return o(new Dn(t.reason))};t.aborted?i():(e=Tn(t,i),n.finally((function(){return e()})).then(r,o));})).finally((function(){e=Nn;}))}var Fn=function(t){return function(n){return Rn(qn(t,n).then((function(n){return Mn(t),n})))}},zn=function(t){var n=Fn(t);return function(t){return n(new Promise((function(n){return setTimeout(n,t)})))}},Ln=Object.assign,Un={},Kn="listenerMiddleware",Wn=function(t){var n=t.type,e=t.actionCreator,r=t.matcher,o=t.predicate,i=t.effect;if(n)o=zt(n).match;else if(e)n=e.type,o=e.match;else if(r)o=r;else if(!o)throw new Error("Creating or removing a listener requires one of the known fields for matching an action");return In(i,"options.listener"),{predicate:o,type:n,effect:i}},Bn=function(t){t.pending.forEach((function(t){Cn(t,"listener-cancelled");}));},Vn=function(t,n,e){try{t(n,e);}catch(t){setTimeout((function(){throw t}),0);}},Xn=zt(Kn+"/add"),Gn=zt(Kn+"/removeAll"),Jn=zt(Kn+"/remove"),$n=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];console.error.apply(console,i([Kn+"/error"],t));};function Hn(t){var n=this;void 0===t&&(t={});var e=new Map,r=t.extra,i=t.onError,u=void 0===i?$n:i;In(u,"onError");var c=function(t){for(var n=0,r=Array.from(e.values());n<r.length;n++){var o=r[n];if(t(o))return o}},a=function(t){var n=c((function(n){return n.effect===t.effect}));return n||(n=function(t){var n=Wn(t),e=n.type,r=n.predicate,o=n.effect;return {id:pn(),effect:o,type:e,predicate:r,pending:new Set,unsubscribe:function(){throw new Error("Unsubscribe not initialized")}}}(t)),function(t){return t.unsubscribe=function(){return e.delete(t.id)},e.set(t.id,t),function(n){t.unsubscribe(),(null==n?void 0:n.cancelActive)&&Bn(t);}}(n)},f=function(t){var n=Wn(t),e=n.type,r=n.effect,o=n.predicate,i=c((function(t){return ("string"==typeof e?t.type===e:t.predicate===o)&&t.effect===r}));return i&&(i.unsubscribe(),t.cancelActive&&Bn(i)),!!i},l=function(t,i,c,f){return y(n,null,(function(){var n,l,s,d;return o(this,(function(p){switch(p.label){case 0:n=new AbortController,l=function(t,n){return function(e,r){return Rn(function(e,r){return y(void 0,null,(function(){var i,u,c,a;return o(this,(function(o){switch(o.label){case 0:Mn(n),i=function(){},u=new Promise((function(n,r){var o=t({predicate:e,effect:function(t,e){e.unsubscribe(),n([t,e.getState(),e.getOriginalState()]);}});i=function(){o(),r();};})),c=[u],null!=r&&c.push(new Promise((function(t){return setTimeout(t,r,null)}))),o.label=1;case 1:return o.trys.push([1,,3,4]),[4,qn(n,Promise.race(c))];case 2:return a=o.sent(),Mn(n),[2,a];case 3:return i(),[7];case 4:return [2]}}))}))}(e,r))}}(a,n.signal),s=[],p.label=1;case 1:return p.trys.push([1,3,4,6]),t.pending.add(n),[4,Promise.resolve(t.effect(i,Ln({},c,{getOriginalState:f,condition:function(t,n){return l(t,n).then(Boolean)},take:l,delay:zn(n.signal),pause:Fn(n.signal),extra:r,signal:n.signal,fork:(v=n.signal,h=s,function(t,n){In(t,"taskExecutor");var e,r=new AbortController;e=r,Tn(v,(function(){return Cn(e,v.reason)}));var i,u,c=(i=function(){return y(void 0,null,(function(){var n;return o(this,(function(e){switch(e.label){case 0:return Mn(v),Mn(r.signal),[4,t({pause:Fn(r.signal),delay:zn(r.signal),signal:r.signal})];case 1:return n=e.sent(),Mn(r.signal),[2,n]}}))}))},u=function(){return Cn(r,"task-completed")},y(void 0,null,(function(){var t;return o(this,(function(n){switch(n.label){case 0:return n.trys.push([0,3,4,5]),[4,Promise.resolve()];case 1:return n.sent(),[4,i()];case 2:return [2,{status:"ok",value:n.sent()}];case 3:return [2,{status:(t=n.sent())instanceof Dn?"cancelled":"rejected",error:t}];case 4:return null==u||u(),[7];case 5:return [2]}}))})));return (null==n?void 0:n.autoJoin)&&h.push(c),{result:Fn(v)(c),cancel:function(){Cn(r,"task-cancelled");}}}),unsubscribe:t.unsubscribe,subscribe:function(){e.set(t.id,t);},cancelActiveListeners:function(){t.pending.forEach((function(t,e,r){t!==n&&(Cn(t,"listener-cancelled"),r.delete(t));}));}})))];case 2:return p.sent(),[3,6];case 3:return (d=p.sent())instanceof Dn||Vn(u,d,{raisedBy:"effect"}),[3,6];case 4:return [4,Promise.allSettled(s)];case 5:return p.sent(),Cn(n,"listener-completed"),t.pending.delete(n),[7];case 6:return [2]}var v,h;}))}))},s=function(t){return function(){t.forEach(Bn),t.clear();}}(e);return {middleware:function(t){return function(n){return function(r){if(!Lt(r))return n(r);if(Xn.match(r))return a(r.payload);if(!Gn.match(r)){if(Jn.match(r))return f(r.payload);var o,i=t.getState(),c=function(){if(i===Un)throw new Error(Kn+": getOriginalState can only be called synchronously");return i};try{if(o=n(r),e.size>0)for(var d=t.getState(),p=Array.from(e.values()),v=0,y=p;v<y.length;v++){var h=y[v],b=!1;try{b=h.predicate(r,d,i);}catch(t){b=!1,Vn(u,t,{raisedBy:"predicate"});}b&&l(h,r,t,c);}}finally{i=Un;}return o}s();}}},startListening:a,stopListening:f,clearListeners:s}}var Qn,Yn="RTK_autoBatch",Zn=function(){return function(t){var n;return {payload:t,meta:(n={},n.RTK_autoBatch=!0,n)}}},te="function"==typeof queueMicrotask?queueMicrotask.bind("undefined"!=typeof window?window:"undefined"!=typeof global?global:globalThis):function(t){return (Qn||(Qn=Promise.resolve())).then(t).catch((function(t){return setTimeout((function(){throw t}),0)}))},ne=function(t){return function(n){setTimeout(n,t);}},ee="undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:ne(10),re=function(t){return void 0===t&&(t={type:"raf"}),function(n){return function(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=n.apply(void 0,e),i=!0,u=!1,c=!1,a=new Set,f="tick"===t.type?te:"raf"===t.type?ee:"callback"===t.type?t.queueNotification:ne(t.timeout),l=function(){c=!1,u&&(u=!1,a.forEach((function(t){return t()})));};return Object.assign({},o,{subscribe:function(t){var n=o.subscribe((function(){return i&&t()}));return a.add(t),function(){n(),a.delete(t);}},dispatch:function(t){var n;try{return i=!(null==(n=null==t?void 0:t.meta)?void 0:n.RTK_autoBatch),(u=!i)&&(c||(c=!0,f(l))),o.dispatch(t)}finally{i=!0;}}})}}};!function(){function t(t,n){var e=o[t];return e?e.enumerable=n:o[t]=e={configurable:!0,enumerable:n,get:function(){return ct.get(this[et],t)},set:function(n){ct.set(this[et],t,n);}},e}function n(t){for(var n=t.length-1;n>=0;n--){var o=t[n][et];if(!o.P)switch(o.i){case 5:r(o)&&V(o);break;case 4:e(o)&&V(o);}}}function e(t){for(var n=t.t,e=t.k,r=ot(e),o=r.length-1;o>=0;o--){var i=r[o];if(i!==et){var u=n[i];if(void 0===u&&!j(n,i))return !0;var c=e[i],a=c&&c[et];if(a?a.t!==u:!E(c,u))return !0}}var f=!!n[et];return r.length!==ot(n).length+(f?0:1)}function r(t){var n=t.k;if(n.length!==t.t.length)return !0;var e=Object.getOwnPropertyDescriptor(n,n.length-1);if(e&&!e.get)return !0;for(var r=0;r<n.length;r++)if(!n.hasOwnProperty(r))return !0;return !1}var o={};ut.ES5||(ut.ES5={J:function(n,e){var r=Array.isArray(n),o=function(n,e){if(n){for(var r=Array(e.length),o=0;o<e.length;o++)Object.defineProperty(r,""+o,t(o,!0));return r}var i=it(e);delete i[et];for(var u=ot(i),c=0;c<u.length;c++){var a=u[c];i[a]=t(a,n||!!i[a].enumerable);}return Object.create(Object.getPrototypeOf(e),i)}(r,n),i={i:r?5:4,A:e?e.A:T(),P:!1,I:!1,R:{},l:e,t:n,k:o,o:null,g:!1,C:!1};return Object.defineProperty(o,et,{value:i,writable:!0}),o},S:function(t,e,o){o?b(e)&&e[et].A===t&&n(t.p):(t.u&&function t(n){if(n&&"object"==typeof n){var e=n[et];if(e){var o=e.t,i=e.k,u=e.R,c=e.i;if(4===c)w(i,(function(n){n!==et&&(void 0!==o[n]||j(o,n)?u[n]||t(i[n]):(u[n]=!0,V(e)));})),w(o,(function(t){void 0!==i[t]||j(i,t)||(u[t]=!1,V(e));}));else if(5===c){if(r(e)&&(V(e),u.length=!0),i.length<o.length)for(var a=i.length;a<o.length;a++)u[a]=!1;else for(var f=o.length;f<i.length;f++)u[f]=!0;for(var l=Math.min(i.length,o.length),s=0;s<l;s++)i.hasOwnProperty(s)||(u[s]=!0),void 0===u[s]&&t(i[s]);}}}}(t.p[0]),n(t.p));},K:function(t){return 4===t.i?e(t):r(t)}});}();

	exports.EnhancerArray = Gt;
	exports.MiddlewareArray = Xt;
	exports.SHOULD_AUTOBATCH = Yn;
	exports.TaskAbortError = Dn;
	exports.__DO_NOT_USE__ActionTypes = gt;
	exports.addListener = Xn;
	exports.applyMiddleware = St;
	exports.autoBatchEnhancer = re;
	exports.bindActionCreators = Et;
	exports.clearAllListeners = Gn;
	exports.combineReducers = jt;
	exports.compose = At;
	exports.configureStore = tn;
	exports.createAction = zt;
	exports.createActionCreatorInvariantMiddleware = Vt;
	exports.createAsyncThunk = gn;
	exports.createDraftSafeSelector = Rt;
	exports.createEntityAdapter = dn;
	exports.createImmutableStateInvariantMiddleware = un;
	exports.createListenerMiddleware = Hn;
	exports.createNextState = st;
	exports.createReducer = en;
	exports.createSelector = Nt;
	exports.createSerializableStateInvariantMiddleware = Yt;
	exports.createSlice = rn;
	exports.createStore = wt;
	exports.current = J;
	exports.findNonSerializableValue = Ht;
	exports.freeze = x;
	exports.getDefaultMiddleware = Zt;
	exports.getType = Bt;
	exports.isAction = Lt;
	exports.isActionCreator = Ut;
	exports.isAllOf = jn;
	exports.isAnyOf = On;
	exports.isAsyncThunkAction = xn;
	exports.isDraft = b;
	exports.isFluxStandardAction = Kt;
	exports.isFulfilled = kn;
	exports.isImmutableDefault = on;
	exports.isPending = An;
	exports.isPlain = $t;
	exports.isPlainObject = Ct;
	exports.isRejected = Sn;
	exports.isRejectedWithValue = _n;
	exports.legacy_createStore = Ot;
	exports.miniSerializeError = bn;
	exports.nanoid = pn;
	exports.original = m;
	exports.prepareAutoBatched = Zn;
	exports.removeListener = Jn;
	exports.unwrapResult = mn;

	Object.defineProperty(exports, '__esModule', { value: true });

}));
//# sourceMappingURL=redux-toolkit.umd.min.js.map
