/*! For license information please see ahooks.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("React")):"function"==typeof define&&define.amd?define(["React"],t):"object"==typeof exports?exports.ahooks=t(require("React")):e.ahooks=t(e.<PERSON>act)}(this,(e=>(()=>{var t={6:(e,t,n)=>{var r=n(714).Symbol;e.exports=r},12:(e,t,n)=>{var r=n(400),o=n(835),i=n(639),u=Math.max,c=Math.min;e.exports=function(e,t,n){var a,s,l,f,d,v,h=0,p=!1,m=!1,g=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function y(t){var n=a,r=s;return a=s=void 0,h=t,f=e.apply(r,n)}function b(e){var n=e-v;return void 0===v||n>=t||n<0||m&&e-h>=l}function w(){var e=o();if(b(e))return E(e);d=setTimeout(w,function(e){var n=t-(e-v);return m?c(n,l-(e-h)):n}(e))}function E(e){return d=void 0,g&&a?y(e):(a=s=void 0,f)}function S(){var e=o(),n=b(e);if(a=arguments,s=this,v=e,n){if(void 0===d)return function(e){return h=e,d=setTimeout(w,t),p?y(e):f}(v);if(m)return clearTimeout(d),d=setTimeout(w,t),y(v)}return void 0===d&&(d=setTimeout(w,t)),f}return t=i(t)||0,r(n)&&(p=!!n.leading,l=(m="maxWait"in n)?u(i(n.maxWait)||0,t):l,g="trailing"in n?!!n.trailing:g),S.cancel=function(){void 0!==d&&clearTimeout(d),h=0,a=v=s=d=void 0},S.flush=function(){return void 0===d?f:E(o())},S}},103:(e,t,n)=>{var r=n(997),o=/^\s+/;e.exports=function(e){return e?e.slice(0,r(e)+1).replace(o,""):e}},108:(e,t,n)=>{var r=n(271),o=n(556),i=n(583),u=Function.prototype,c=Object.prototype,a=u.toString,s=c.hasOwnProperty,l=a.call(Object);e.exports=function(e){if(!i(e)||"[object Object]"!=r(e))return!1;var t=o(e);if(null===t)return!0;var n=s.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&a.call(n)==l}},271:(e,t,n)=>{var r=n(6),o=n(650),i=n(881),u=r?r.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":u&&u in Object(e)?o(e):i(e)}},300:function(e){e.exports=function(){"use strict";var e=6e4,t=36e5,n="millisecond",r="second",o="minute",i="hour",u="day",c="week",a="month",s="quarter",l="year",f="date",d="Invalid Date",v=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,h=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,p={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],n=e%100;return"["+e+(t[(n-20)%10]||t[n]||t[0])+"]"}},m=function(e,t,n){var r=String(e);return!r||r.length>=t?e:""+Array(t+1-r.length).join(n)+e},g={s:m,z:function(e){var t=-e.utcOffset(),n=Math.abs(t),r=Math.floor(n/60),o=n%60;return(t<=0?"+":"-")+m(r,2,"0")+":"+m(o,2,"0")},m:function e(t,n){if(t.date()<n.date())return-e(n,t);var r=12*(n.year()-t.year())+(n.month()-t.month()),o=t.clone().add(r,a),i=n-o<0,u=t.clone().add(r+(i?-1:1),a);return+(-(r+(n-o)/(i?o-u:u-o))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(e){return{M:a,y:l,w:c,d:u,D:f,h:i,m:o,s:r,ms:n,Q:s}[e]||String(e||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}},y="en",b={};b[y]=p;var w="$isDayjsObject",E=function(e){return e instanceof T||!(!e||!e[w])},S=function e(t,n,r){var o;if(!t)return y;if("string"==typeof t){var i=t.toLowerCase();b[i]&&(o=i),n&&(b[i]=n,o=i);var u=t.split("-");if(!o&&u.length>1)return e(u[0])}else{var c=t.name;b[c]=t,o=c}return!r&&o&&(y=o),o||!r&&y},_=function(e,t){if(E(e))return e.clone();var n="object"==typeof t?t:{};return n.date=e,n.args=arguments,new T(n)},O=g;O.l=S,O.i=E,O.w=function(e,t){return _(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var T=function(){function p(e){this.$L=S(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[w]=!0}var m=p.prototype;return m.parse=function(e){this.$d=function(e){var t=e.date,n=e.utc;if(null===t)return new Date(NaN);if(O.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var r=t.match(v);if(r){var o=r[2]-1||0,i=(r[7]||"0").substring(0,3);return n?new Date(Date.UTC(r[1],o,r[3]||1,r[4]||0,r[5]||0,r[6]||0,i)):new Date(r[1],o,r[3]||1,r[4]||0,r[5]||0,r[6]||0,i)}}return new Date(t)}(e),this.init()},m.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},m.$utils=function(){return O},m.isValid=function(){return!(this.$d.toString()===d)},m.isSame=function(e,t){var n=_(e);return this.startOf(t)<=n&&n<=this.endOf(t)},m.isAfter=function(e,t){return _(e)<this.startOf(t)},m.isBefore=function(e,t){return this.endOf(t)<_(e)},m.$g=function(e,t,n){return O.u(e)?this[t]:this.set(n,e)},m.unix=function(){return Math.floor(this.valueOf()/1e3)},m.valueOf=function(){return this.$d.getTime()},m.startOf=function(e,t){var n=this,s=!!O.u(t)||t,d=O.p(e),v=function(e,t){var r=O.w(n.$u?Date.UTC(n.$y,t,e):new Date(n.$y,t,e),n);return s?r:r.endOf(u)},h=function(e,t){return O.w(n.toDate()[e].apply(n.toDate("s"),(s?[0,0,0,0]:[23,59,59,999]).slice(t)),n)},p=this.$W,m=this.$M,g=this.$D,y="set"+(this.$u?"UTC":"");switch(d){case l:return s?v(1,0):v(31,11);case a:return s?v(1,m):v(0,m+1);case c:var b=this.$locale().weekStart||0,w=(p<b?p+7:p)-b;return v(s?g-w:g+(6-w),m);case u:case f:return h(y+"Hours",0);case i:return h(y+"Minutes",1);case o:return h(y+"Seconds",2);case r:return h(y+"Milliseconds",3);default:return this.clone()}},m.endOf=function(e){return this.startOf(e,!1)},m.$set=function(e,t){var c,s=O.p(e),d="set"+(this.$u?"UTC":""),v=(c={},c[u]=d+"Date",c[f]=d+"Date",c[a]=d+"Month",c[l]=d+"FullYear",c[i]=d+"Hours",c[o]=d+"Minutes",c[r]=d+"Seconds",c[n]=d+"Milliseconds",c)[s],h=s===u?this.$D+(t-this.$W):t;if(s===a||s===l){var p=this.clone().set(f,1);p.$d[v](h),p.init(),this.$d=p.set(f,Math.min(this.$D,p.daysInMonth())).$d}else v&&this.$d[v](h);return this.init(),this},m.set=function(e,t){return this.clone().$set(e,t)},m.get=function(e){return this[O.p(e)]()},m.add=function(n,s){var f,d=this;n=Number(n);var v=O.p(s),h=function(e){var t=_(d);return O.w(t.date(t.date()+Math.round(e*n)),d)};if(v===a)return this.set(a,this.$M+n);if(v===l)return this.set(l,this.$y+n);if(v===u)return h(1);if(v===c)return h(7);var p=(f={},f[o]=e,f[i]=t,f[r]=1e3,f)[v]||1,m=this.$d.getTime()+n*p;return O.w(m,this)},m.subtract=function(e,t){return this.add(-1*e,t)},m.format=function(e){var t=this,n=this.$locale();if(!this.isValid())return n.invalidDate||d;var r=e||"YYYY-MM-DDTHH:mm:ssZ",o=O.z(this),i=this.$H,u=this.$m,c=this.$M,a=n.weekdays,s=n.months,l=n.meridiem,f=function(e,n,o,i){return e&&(e[n]||e(t,r))||o[n].slice(0,i)},v=function(e){return O.s(i%12||12,e,"0")},p=l||function(e,t,n){var r=e<12?"AM":"PM";return n?r.toLowerCase():r};return r.replace(h,(function(e,r){return r||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return O.s(t.$y,4,"0");case"M":return c+1;case"MM":return O.s(c+1,2,"0");case"MMM":return f(n.monthsShort,c,s,3);case"MMMM":return f(s,c);case"D":return t.$D;case"DD":return O.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return f(n.weekdaysMin,t.$W,a,2);case"ddd":return f(n.weekdaysShort,t.$W,a,3);case"dddd":return a[t.$W];case"H":return String(i);case"HH":return O.s(i,2,"0");case"h":return v(1);case"hh":return v(2);case"a":return p(i,u,!0);case"A":return p(i,u,!1);case"m":return String(u);case"mm":return O.s(u,2,"0");case"s":return String(t.$s);case"ss":return O.s(t.$s,2,"0");case"SSS":return O.s(t.$ms,3,"0");case"Z":return o}return null}(e)||o.replace(":","")}))},m.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},m.diff=function(n,f,d){var v,h=this,p=O.p(f),m=_(n),g=(m.utcOffset()-this.utcOffset())*e,y=this-m,b=function(){return O.m(h,m)};switch(p){case l:v=b()/12;break;case a:v=b();break;case s:v=b()/3;break;case c:v=(y-g)/6048e5;break;case u:v=(y-g)/864e5;break;case i:v=y/t;break;case o:v=y/e;break;case r:v=y/1e3;break;default:v=y}return d?v:O.a(v)},m.daysInMonth=function(){return this.endOf(a).$D},m.$locale=function(){return b[this.$L]},m.locale=function(e,t){if(!e)return this.$L;var n=this.clone(),r=S(e,t,!0);return r&&(n.$L=r),n},m.clone=function(){return O.w(this.$d,this)},m.toDate=function(){return new Date(this.valueOf())},m.toJSON=function(){return this.isValid()?this.toISOString():null},m.toISOString=function(){return this.$d.toISOString()},m.toString=function(){return this.$d.toUTCString()},p}(),x=T.prototype;return _.prototype=x,[["$ms",n],["$s",r],["$m",o],["$H",i],["$W",u],["$M",a],["$y",l],["$D",f]].forEach((function(e){x[e[1]]=function(t){return this.$g(t,e[0],e[1])}})),_.extend=function(e,t){return e.$i||(e(t,T,_),e.$i=!0),_},_.locale=S,_.isDayjs=E,_.unix=function(e){return _(1e3*e)},_.en=b[y],_.Ls=b,_.p={},_}()},400:e=>{e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},546:e=>{e.exports=function(e,t){return function(n){return e(t(n))}}},556:(e,t,n)=>{var r=n(546)(Object.getPrototypeOf,Object);e.exports=r},583:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},603:(e,t,n)=>{var r="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g;e.exports=r},639:(e,t,n)=>{var r=n(103),o=n(400),i=n(975),u=/^[-+]0x[0-9a-f]+$/i,c=/^0b[01]+$/i,a=/^0o[0-7]+$/i,s=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(i(e))return NaN;if(o(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=o(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=r(e);var n=c.test(e);return n||a.test(e)?s(e.slice(2),n?2:8):u.test(e)?NaN:+e}},650:(e,t,n)=>{var r=n(6),o=Object.prototype,i=o.hasOwnProperty,u=o.toString,c=r?r.toStringTag:void 0;e.exports=function(e){var t=i.call(e,c),n=e[c];try{e[c]=void 0;var r=!0}catch(e){}var o=u.call(e);return r&&(t?e[c]=n:delete e[c]),o}},714:(e,t,n)=>{var r=n(603),o="object"==typeof self&&self&&self.Object===Object&&self,i=r||o||Function("return this")();e.exports=i},727:e=>{var t="undefined"!=typeof Element,n="function"==typeof Map,r="function"==typeof Set,o="function"==typeof ArrayBuffer&&!!ArrayBuffer.isView;function i(e,u){if(e===u)return!0;if(e&&u&&"object"==typeof e&&"object"==typeof u){if(e.constructor!==u.constructor)return!1;var c,a,s,l;if(Array.isArray(e)){if((c=e.length)!=u.length)return!1;for(a=c;0!=a--;)if(!i(e[a],u[a]))return!1;return!0}if(n&&e instanceof Map&&u instanceof Map){if(e.size!==u.size)return!1;for(l=e.entries();!(a=l.next()).done;)if(!u.has(a.value[0]))return!1;for(l=e.entries();!(a=l.next()).done;)if(!i(a.value[1],u.get(a.value[0])))return!1;return!0}if(r&&e instanceof Set&&u instanceof Set){if(e.size!==u.size)return!1;for(l=e.entries();!(a=l.next()).done;)if(!u.has(a.value[0]))return!1;return!0}if(o&&ArrayBuffer.isView(e)&&ArrayBuffer.isView(u)){if((c=e.length)!=u.length)return!1;for(a=c;0!=a--;)if(e[a]!==u[a])return!1;return!0}if(e.constructor===RegExp)return e.source===u.source&&e.flags===u.flags;if(e.valueOf!==Object.prototype.valueOf&&"function"==typeof e.valueOf&&"function"==typeof u.valueOf)return e.valueOf()===u.valueOf();if(e.toString!==Object.prototype.toString&&"function"==typeof e.toString&&"function"==typeof u.toString)return e.toString()===u.toString();if((c=(s=Object.keys(e)).length)!==Object.keys(u).length)return!1;for(a=c;0!=a--;)if(!Object.prototype.hasOwnProperty.call(u,s[a]))return!1;if(t&&e instanceof Element)return!1;for(a=c;0!=a--;)if(("_owner"!==s[a]&&"__v"!==s[a]&&"__o"!==s[a]||!e.$$typeof)&&!i(e[s[a]],u[s[a]]))return!1;return!0}return e!=e&&u!=u}e.exports=function(e,t){try{return i(e,t)}catch(e){if((e.message||"").match(/stack|recursion/i))return console.warn("react-fast-compare cannot handle circular refs"),!1;throw e}}},735:()=>{!function(){"use strict";if("object"==typeof window)if("IntersectionObserver"in window&&"IntersectionObserverEntry"in window&&"intersectionRatio"in window.IntersectionObserverEntry.prototype)"isIntersecting"in window.IntersectionObserverEntry.prototype||Object.defineProperty(window.IntersectionObserverEntry.prototype,"isIntersecting",{get:function(){return this.intersectionRatio>0}});else{var e=function(){for(var e=window.document,t=o(e);t;)t=o(e=t.ownerDocument);return e}(),t=[],n=null,r=null;u.prototype.THROTTLE_TIMEOUT=100,u.prototype.POLL_INTERVAL=null,u.prototype.USE_MUTATION_OBSERVER=!0,u._setupCrossOriginUpdater=function(){return n||(n=function(e,n){r=e&&n?f(e,n):{top:0,bottom:0,left:0,right:0,width:0,height:0},t.forEach((function(e){e._checkForIntersections()}))}),n},u._resetCrossOriginUpdater=function(){n=null,r=null},u.prototype.observe=function(e){if(!this._observationTargets.some((function(t){return t.element==e}))){if(!e||1!=e.nodeType)throw new Error("target must be an Element");this._registerInstance(),this._observationTargets.push({element:e,entry:null}),this._monitorIntersections(e.ownerDocument),this._checkForIntersections()}},u.prototype.unobserve=function(e){this._observationTargets=this._observationTargets.filter((function(t){return t.element!=e})),this._unmonitorIntersections(e.ownerDocument),0==this._observationTargets.length&&this._unregisterInstance()},u.prototype.disconnect=function(){this._observationTargets=[],this._unmonitorAllIntersections(),this._unregisterInstance()},u.prototype.takeRecords=function(){var e=this._queuedEntries.slice();return this._queuedEntries=[],e},u.prototype._initThresholds=function(e){var t=e||[0];return Array.isArray(t)||(t=[t]),t.sort().filter((function(e,t,n){if("number"!=typeof e||isNaN(e)||e<0||e>1)throw new Error("threshold must be a number between 0 and 1 inclusively");return e!==n[t-1]}))},u.prototype._parseRootMargin=function(e){var t=(e||"0px").split(/\s+/).map((function(e){var t=/^(-?\d*\.?\d+)(px|%)$/.exec(e);if(!t)throw new Error("rootMargin must be specified in pixels or percent");return{value:parseFloat(t[1]),unit:t[2]}}));return t[1]=t[1]||t[0],t[2]=t[2]||t[0],t[3]=t[3]||t[1],t},u.prototype._monitorIntersections=function(t){var n=t.defaultView;if(n&&-1==this._monitoringDocuments.indexOf(t)){var r=this._checkForIntersections,i=null,u=null;this.POLL_INTERVAL?i=n.setInterval(r,this.POLL_INTERVAL):(c(n,"resize",r,!0),c(t,"scroll",r,!0),this.USE_MUTATION_OBSERVER&&"MutationObserver"in n&&(u=new n.MutationObserver(r)).observe(t,{attributes:!0,childList:!0,characterData:!0,subtree:!0})),this._monitoringDocuments.push(t),this._monitoringUnsubscribes.push((function(){var e=t.defaultView;e&&(i&&e.clearInterval(i),a(e,"resize",r,!0)),a(t,"scroll",r,!0),u&&u.disconnect()}));var s=this.root&&(this.root.ownerDocument||this.root)||e;if(t!=s){var l=o(t);l&&this._monitorIntersections(l.ownerDocument)}}},u.prototype._unmonitorIntersections=function(t){var n=this._monitoringDocuments.indexOf(t);if(-1!=n){var r=this.root&&(this.root.ownerDocument||this.root)||e,i=this._observationTargets.some((function(e){var n=e.element.ownerDocument;if(n==t)return!0;for(;n&&n!=r;){var i=o(n);if((n=i&&i.ownerDocument)==t)return!0}return!1}));if(!i){var u=this._monitoringUnsubscribes[n];if(this._monitoringDocuments.splice(n,1),this._monitoringUnsubscribes.splice(n,1),u(),t!=r){var c=o(t);c&&this._unmonitorIntersections(c.ownerDocument)}}}},u.prototype._unmonitorAllIntersections=function(){var e=this._monitoringUnsubscribes.slice(0);this._monitoringDocuments.length=0,this._monitoringUnsubscribes.length=0;for(var t=0;t<e.length;t++)e[t]()},u.prototype._checkForIntersections=function(){if(this.root||!n||r){var e=this._rootIsInDom(),t=e?this._getRootRect():{top:0,bottom:0,left:0,right:0,width:0,height:0};this._observationTargets.forEach((function(r){var o=r.element,u=s(o),c=this._rootContainsTarget(o),a=r.entry,l=e&&c&&this._computeTargetAndRootIntersection(o,u,t),f=null;this._rootContainsTarget(o)?n&&!this.root||(f=t):f={top:0,bottom:0,left:0,right:0,width:0,height:0};var d=r.entry=new i({time:window.performance&&performance.now&&performance.now(),target:o,boundingClientRect:u,rootBounds:f,intersectionRect:l});a?e&&c?this._hasCrossedThreshold(a,d)&&this._queuedEntries.push(d):a&&a.isIntersecting&&this._queuedEntries.push(d):this._queuedEntries.push(d)}),this),this._queuedEntries.length&&this._callback(this.takeRecords(),this)}},u.prototype._computeTargetAndRootIntersection=function(t,o,i){if("none"!=window.getComputedStyle(t).display){for(var u,c,a,l,d,h,p,m,g=o,y=v(t),b=!1;!b&&y;){var w=null,E=1==y.nodeType?window.getComputedStyle(y):{};if("none"==E.display)return null;if(y==this.root||9==y.nodeType)if(b=!0,y==this.root||y==e)n&&!this.root?!r||0==r.width&&0==r.height?(y=null,w=null,g=null):w=r:w=i;else{var S=v(y),_=S&&s(S),O=S&&this._computeTargetAndRootIntersection(S,_,i);_&&O?(y=S,w=f(_,O)):(y=null,g=null)}else{var T=y.ownerDocument;y!=T.body&&y!=T.documentElement&&"visible"!=E.overflow&&(w=s(y))}if(w&&(u=w,c=g,void 0,void 0,void 0,void 0,void 0,void 0,a=Math.max(u.top,c.top),l=Math.min(u.bottom,c.bottom),d=Math.max(u.left,c.left),m=l-a,g=(p=(h=Math.min(u.right,c.right))-d)>=0&&m>=0&&{top:a,bottom:l,left:d,right:h,width:p,height:m}||null),!g)break;y=y&&v(y)}return g}},u.prototype._getRootRect=function(){var t;if(this.root&&!h(this.root))t=s(this.root);else{var n=h(this.root)?this.root:e,r=n.documentElement,o=n.body;t={top:0,left:0,right:r.clientWidth||o.clientWidth,width:r.clientWidth||o.clientWidth,bottom:r.clientHeight||o.clientHeight,height:r.clientHeight||o.clientHeight}}return this._expandRectByRootMargin(t)},u.prototype._expandRectByRootMargin=function(e){var t=this._rootMarginValues.map((function(t,n){return"px"==t.unit?t.value:t.value*(n%2?e.width:e.height)/100})),n={top:e.top-t[0],right:e.right+t[1],bottom:e.bottom+t[2],left:e.left-t[3]};return n.width=n.right-n.left,n.height=n.bottom-n.top,n},u.prototype._hasCrossedThreshold=function(e,t){var n=e&&e.isIntersecting?e.intersectionRatio||0:-1,r=t.isIntersecting?t.intersectionRatio||0:-1;if(n!==r)for(var o=0;o<this.thresholds.length;o++){var i=this.thresholds[o];if(i==n||i==r||i<n!=i<r)return!0}},u.prototype._rootIsInDom=function(){return!this.root||d(e,this.root)},u.prototype._rootContainsTarget=function(t){var n=this.root&&(this.root.ownerDocument||this.root)||e;return d(n,t)&&(!this.root||n==t.ownerDocument)},u.prototype._registerInstance=function(){t.indexOf(this)<0&&t.push(this)},u.prototype._unregisterInstance=function(){var e=t.indexOf(this);-1!=e&&t.splice(e,1)},window.IntersectionObserver=u,window.IntersectionObserverEntry=i}function o(e){try{return e.defaultView&&e.defaultView.frameElement||null}catch(e){return null}}function i(e){this.time=e.time,this.target=e.target,this.rootBounds=l(e.rootBounds),this.boundingClientRect=l(e.boundingClientRect),this.intersectionRect=l(e.intersectionRect||{top:0,bottom:0,left:0,right:0,width:0,height:0}),this.isIntersecting=!!e.intersectionRect;var t=this.boundingClientRect,n=t.width*t.height,r=this.intersectionRect,o=r.width*r.height;this.intersectionRatio=n?Number((o/n).toFixed(4)):this.isIntersecting?1:0}function u(e,t){var n,r,o,i=t||{};if("function"!=typeof e)throw new Error("callback must be a function");if(i.root&&1!=i.root.nodeType&&9!=i.root.nodeType)throw new Error("root must be a Document or Element");this._checkForIntersections=(n=this._checkForIntersections.bind(this),r=this.THROTTLE_TIMEOUT,o=null,function(){o||(o=setTimeout((function(){n(),o=null}),r))}),this._callback=e,this._observationTargets=[],this._queuedEntries=[],this._rootMarginValues=this._parseRootMargin(i.rootMargin),this.thresholds=this._initThresholds(i.threshold),this.root=i.root||null,this.rootMargin=this._rootMarginValues.map((function(e){return e.value+e.unit})).join(" "),this._monitoringDocuments=[],this._monitoringUnsubscribes=[]}function c(e,t,n,r){"function"==typeof e.addEventListener?e.addEventListener(t,n,r||!1):"function"==typeof e.attachEvent&&e.attachEvent("on"+t,n)}function a(e,t,n,r){"function"==typeof e.removeEventListener?e.removeEventListener(t,n,r||!1):"function"==typeof e.detachEvent&&e.detachEvent("on"+t,n)}function s(e){var t;try{t=e.getBoundingClientRect()}catch(e){}return t?(t.width&&t.height||(t={top:t.top,right:t.right,bottom:t.bottom,left:t.left,width:t.right-t.left,height:t.bottom-t.top}),t):{top:0,bottom:0,left:0,right:0,width:0,height:0}}function l(e){return!e||"x"in e?e:{top:e.top,y:e.top,bottom:e.bottom,left:e.left,x:e.left,right:e.right,width:e.width,height:e.height}}function f(e,t){var n=t.top-e.top,r=t.left-e.left;return{top:n,left:r,height:t.height,width:t.width,bottom:n+t.height,right:r+t.width}}function d(e,t){for(var n=t;n;){if(n==e)return!0;n=v(n)}return!1}function v(t){var n=t.parentNode;return 9==t.nodeType&&t!=e?o(t):(n&&n.assignedSlot&&(n=n.assignedSlot.parentNode),n&&11==n.nodeType&&n.host?n.host:n)}function h(e){return e&&9===e.nodeType}}()},763:e=>{!function(){"use strict";var t="undefined"!=typeof window&&void 0!==window.document?window.document:{},n=e.exports,r=function(){for(var e,n=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],r=0,o=n.length,i={};r<o;r++)if((e=n[r])&&e[1]in t){for(r=0;r<e.length;r++)i[n[0][r]]=e[r];return i}return!1}(),o={change:r.fullscreenchange,error:r.fullscreenerror},i={request:function(e,n){return new Promise(function(o,i){var u=function(){this.off("change",u),o()}.bind(this);this.on("change",u);var c=(e=e||t.documentElement)[r.requestFullscreen](n);c instanceof Promise&&c.then(u).catch(i)}.bind(this))},exit:function(){return new Promise(function(e,n){if(this.isFullscreen){var o=function(){this.off("change",o),e()}.bind(this);this.on("change",o);var i=t[r.exitFullscreen]();i instanceof Promise&&i.then(o).catch(n)}else e()}.bind(this))},toggle:function(e,t){return this.isFullscreen?this.exit():this.request(e,t)},onchange:function(e){this.on("change",e)},onerror:function(e){this.on("error",e)},on:function(e,n){var r=o[e];r&&t.addEventListener(r,n,!1)},off:function(e,n){var r=o[e];r&&t.removeEventListener(r,n,!1)},raw:r};r?(Object.defineProperties(i,{isFullscreen:{get:function(){return Boolean(t[r.fullscreenElement])}},element:{enumerable:!0,get:function(){return t[r.fullscreenElement]}},isEnabled:{enumerable:!0,get:function(){return Boolean(t[r.fullscreenEnabled])}}}),n?e.exports=i:window.screenfull=i):n?e.exports={isEnabled:!1}:window.screenfull={isEnabled:!1}}()},823:(e,t,n)=>{var r=n(12),o=n(400);e.exports=function(e,t,n){var i=!0,u=!0;if("function"!=typeof e)throw new TypeError("Expected a function");return o(n)&&(i="leading"in n?!!n.leading:i,u="trailing"in n?!!n.trailing:u),r(e,t,{leading:i,maxWait:t,trailing:u})}},835:(e,t,n)=>{var r=n(714);e.exports=function(){return r.Date.now()}},881:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},883:t=>{"use strict";t.exports=e},975:(e,t,n)=>{var r=n(271),o=n(583);e.exports=function(e){return"symbol"==typeof e||o(e)&&"[object Symbol]"==r(e)}},997:e=>{var t=/\s/;e.exports=function(e){for(var n=e.length;n--&&t.test(e.charAt(n)););return n}}},n={};function r(e){var o=n[e];if(void 0!==o)return o.exports;var i=n[e]={exports:{}};return t[e].call(i.exports,i,i.exports,r),i.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};return(()=>{"use strict";r.r(o),r.d(o,{clearCache:()=>_,configResponsive:()=>Dt,createUpdateEffect:()=>t,useAntdTable:()=>G,useAsyncEffect:()=>K,useBoolean:()=>Z,useClickAway:()=>re,useControllableValue:()=>oe,useCookieState:()=>ce,useCountDown:()=>fe,useCounter:()=>ve,useCreation:()=>b,useDebounce:()=>pe,useDebounceEffect:()=>me,useDebounceFn:()=>he,useDeepCompareEffect:()=>Ee,useDeepCompareLayoutEffect:()=>Se,useDocumentVisibility:()=>Te,useDrag:()=>xe,useDrop:()=>Me,useDynamicList:()=>Re,useEventEmitter:()=>Ae,useEventListener:()=>_e,useEventTarget:()=>Le,useExternal:()=>De,useFavicon:()=>Fe,useFocusWithin:()=>Ne,useFullscreen:()=>$e,useFusionTable:()=>ze,useGetState:()=>He,useHistoryTravel:()=>Ye,useHover:()=>Be,useInViewport:()=>Xe,useInfiniteScroll:()=>qe,useInterval:()=>Ue,useIsomorphicLayoutEffect:()=>Ge,useKeyPress:()=>nt,useLatest:()=>w,useLocalStorageState:()=>it,useLockFn:()=>ut,useLongPress:()=>ct,useMap:()=>at,useMemoizedFn:()=>h,useMount:()=>Y,useMouse:()=>ft,useMutationObserver:()=>Rn,useNetwork:()=>pt,usePagination:()=>X,usePrevious:()=>gt,useRafInterval:()=>yt,useRafState:()=>st,useRafTimeout:()=>bt,useReactive:()=>Tt,useRequest:()=>U,useResetState:()=>xt,useResponsive:()=>It,useSafeState:()=>Nt,useScroll:()=>jt,useSelections:()=>Pt,useSessionStorageState:()=>$t,useSet:()=>zt,useSetState:()=>Ht,useSize:()=>dn,useTextSelection:()=>pn,useTheme:()=>Ln,useThrottle:()=>gn,useThrottleEffect:()=>yn,useThrottleFn:()=>mn,useTimeout:()=>bn,useTitle:()=>En,useToggle:()=>J,useTrackedEffect:()=>Sn,useUnmount:()=>E,useUnmountedRef:()=>Ft,useUpdate:()=>B,useUpdateEffect:()=>p,useUpdateLayoutEffect:()=>_n,useVirtualList:()=>On,useWebSocket:()=>xn,useWhyDidYouUpdate:()=>Mn});var e=r(883),t=function(t){return function(n,r){var o=(0,e.useRef)(!1);t((function(){return function(){o.current=!1}}),[]),t((function(){if(o.current)return n();o.current=!0}),r)}},n=function(){return n=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},n.apply(this,arguments)};function i(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}function u(e,t,n,r){return new(n||(n=Promise))((function(o,i){function u(e){try{a(r.next(e))}catch(e){i(e)}}function c(e){try{a(r.throw(e))}catch(e){i(e)}}function a(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(u,c)}a((r=r.apply(e,t||[])).next())}))}function c(e,t){var n,r,o,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},u=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return u.next=c(0),u.throw=c(1),u.return=c(2),"function"==typeof Symbol&&(u[Symbol.iterator]=function(){return this}),u;function c(c){return function(a){return function(c){if(n)throw new TypeError("Generator is already executing.");for(;u&&(u=0,c[0]&&(i=0)),i;)try{if(n=1,r&&(o=2&c[0]?r.return:c[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,c[1])).done)return o;switch(r=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return i.label++,{value:c[1],done:!1};case 5:i.label++,r=c[1],c=[0];continue;case 7:c=i.ops.pop(),i.trys.pop();continue;default:if(!((o=(o=i.trys).length>0&&o[o.length-1])||6!==c[0]&&2!==c[0])){i=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){i.label=c[1];break}if(6===c[0]&&i.label<o[1]){i.label=o[1],o=c;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(c);break}o[2]&&i.ops.pop(),i.trys.pop();continue}c=t.call(e,i)}catch(e){c=[6,e],r=0}finally{n=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,a])}}}function a(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function s(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),u=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)u.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return u}function l(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError;var f=function(e){return"function"==typeof e},d=function(e){return"string"==typeof e},v=function(e){return"number"==typeof e};const h=function(t){var n=(0,e.useRef)(t);n.current=(0,e.useMemo)((function(){return t}),[t]);var r=(0,e.useRef)(void 0);return r.current||(r.current=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return n.current.apply(this,e)}),r.current},p=t(e.useEffect);var m=function(t,n){var r=n.manual,o=n.ready,i=void 0===o||o,u=n.defaultParams,c=void 0===u?[]:u,a=n.refreshDeps,f=void 0===a?[]:a,d=n.refreshDepsAction,v=(0,e.useRef)(!1);return v.current=!1,p((function(){!r&&i&&(v.current=!0,t.run.apply(t,l([],s(c),!1)))}),[i]),p((function(){v.current||r||(v.current=!0,d?d():t.refresh())}),l([],s(f),!1)),{onBefore:function(){if(!i)return{stopNow:!0}}}};m.onInit=function(e){var t=e.ready,n=void 0===t||t;return{loading:!e.manual&&n}};const g=m,y=function(e,t){if(e===t)return!0;for(var n=0;n<e.length;n++)if(!Object.is(e[n],t[n]))return!1;return!0},b=function(t,n){var r=(0,e.useRef)({deps:n,obj:void 0,initialized:!1}).current;return!1!==r.initialized&&y(r.deps,n)||(r.deps=n,r.obj=t(),r.initialized=!0),r.obj},w=function(t){var n=(0,e.useRef)(t);return n.current=t,n},E=function(t){var n=w(t);(0,e.useEffect)((function(){return function(){n.current()}}),[])};var S=new Map,_=function(e){e?(Array.isArray(e)?e:[e]).forEach((function(e){return S.delete(e)})):S.clear()},O=new Map,T={},x=function(e,t){return T[e]||(T[e]=[]),T[e].push(t),function(){var n=T[e].indexOf(t);T[e].splice(n,1)}};const M=function(t,r){var o=r.cacheKey,i=r.cacheTime,u=void 0===i?3e5:i,c=r.staleTime,a=void 0===c?0:c,f=r.setCache,d=r.getCache,v=(0,e.useRef)(void 0),h=(0,e.useRef)(void 0),p=function(e,t){f?f(t):function(e,t,r){var o=S.get(e);(null==o?void 0:o.timer)&&clearTimeout(o.timer);var i=void 0;t>-1&&(i=setTimeout((function(){S.delete(e)}),t)),S.set(e,n(n({},r),{timer:i}))}(e,u,t),function(e,t){T[e]&&T[e].forEach((function(e){return e(t)}))}(e,t.data)},m=function(e,t){return void 0===t&&(t=[]),d?d(t):function(e){return S.get(e)}(e)};return b((function(){if(o){var e=m(o);e&&Object.hasOwnProperty.call(e,"data")&&(t.state.data=e.data,t.state.params=e.params,(-1===a||Date.now()-e.time<=a)&&(t.state.loading=!1)),v.current=x(o,(function(e){t.setState({data:e})}))}}),[]),E((function(){var e;null===(e=v.current)||void 0===e||e.call(v)})),o?{onBefore:function(e){var t=m(o,e);return t&&Object.hasOwnProperty.call(t,"data")?-1===a||Date.now()-t.time<=a?{loading:!1,data:null==t?void 0:t.data,error:void 0,returnNow:!0}:{data:null==t?void 0:t.data,error:void 0}:{}},onRequest:function(e,t){var n=function(e){return O.get(e)}(o);return n&&n!==h.current||(n=e.apply(void 0,l([],s(t),!1)),h.current=n,function(e,t){O.set(e,t),t.then((function(t){return O.delete(e),t})).catch((function(){O.delete(e)}))}(o,n)),{servicePromise:n}},onSuccess:function(e,n){var r;o&&(null===(r=v.current)||void 0===r||r.call(v),p(o,{data:e,params:n,time:Date.now()}),v.current=x(o,(function(e){t.setState({data:e})})))},onMutate:function(e){var n;o&&(null===(n=v.current)||void 0===n||n.call(v),p(o,{data:e,params:t.state.params,time:Date.now()}),v.current=x(o,(function(e){t.setState({data:e})})))}}:{}};var R=r(12),k=r.n(R);const A=function(t,n){var r=n.debounceWait,o=n.debounceLeading,i=n.debounceTrailing,u=n.debounceMaxWait,c=(0,e.useRef)(void 0),a=(0,e.useMemo)((function(){var e={};return void 0!==o&&(e.leading=o),void 0!==i&&(e.trailing=i),void 0!==u&&(e.maxWait=u),e}),[o,i,u]);return(0,e.useEffect)((function(){if(r){var e=t.runAsync.bind(t);return c.current=k()((function(e){e()}),r,a),t.runAsync=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new Promise((function(n,r){var o;null===(o=c.current)||void 0===o||o.call(c,(function(){e.apply(void 0,l([],s(t),!1)).then(n).catch(r)}))}))},function(){var n;null===(n=c.current)||void 0===n||n.cancel(),t.runAsync=e}}}),[r,a]),r?{onCancel:function(){var e;null===(e=c.current)||void 0===e||e.cancel()}}:{}},L=function(t,n){var r=n.loadingDelay,o=n.ready,i=(0,e.useRef)(void 0);if(!r)return{};var u=function(){i.current&&clearTimeout(i.current)};return{onBefore:function(){return u(),!1!==o&&(i.current=setTimeout((function(){t.setState({loading:!0})}),r)),{loading:!1}},onFinally:function(){u()},onCancel:function(){u()}}},C=!("undefined"==typeof window||!window.document||!window.document.createElement);function D(){return!C||"hidden"!==document.visibilityState}var I=[];C&&window.addEventListener("visibilitychange",(function(){if(D())for(var e=0;e<I.length;e++)(0,I[e])()}),!1);const F=function(t,n){var r=n.pollingInterval,o=n.pollingWhenHidden,i=void 0===o||o,u=n.pollingErrorRetryCount,c=void 0===u?-1:u,a=(0,e.useRef)(void 0),s=(0,e.useRef)(void 0),l=(0,e.useRef)(0),f=function(){var e;a.current&&clearTimeout(a.current),null===(e=s.current)||void 0===e||e.call(s)};return p((function(){r||f()}),[r]),r?{onBefore:function(){f()},onError:function(){l.current+=1},onSuccess:function(){l.current=0},onFinally:function(){-1===c||-1!==c&&l.current<=c?a.current=setTimeout((function(){var e;i||D()?t.refresh():s.current=(e=function(){t.refresh()},I.push(e),function(){var t=I.indexOf(e);I.splice(t,1)})}),r):l.current=0},onCancel:function(){f()}}:{}};var N=[];if(C){var j=function(){if(D()&&(!C||void 0===navigator.onLine||navigator.onLine))for(var e=0;e<N.length;e++)(0,N[e])()};window.addEventListener("visibilitychange",j,!1),window.addEventListener("focus",j,!1)}const P=function(t,n){var r=n.refreshOnWindowFocus,o=n.focusTimespan,i=void 0===o?5e3:o,u=(0,e.useRef)(void 0),c=function(){var e;null===(e=u.current)||void 0===e||e.call(u)};return(0,e.useEffect)((function(){if(r){var e=(o=t.refresh.bind(t),a=i,f=!1,function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];f||(f=!0,o.apply(void 0,l([],s(e),!1)),setTimeout((function(){f=!1}),a))});u.current=(n=function(){e()},N.push(n),function(){var e=N.indexOf(n);e>-1&&N.splice(e,1)})}var n,o,a,f;return function(){c()}}),[r,i]),E((function(){c()})),{}},$=function(t,n){var r=n.retryInterval,o=n.retryCount,i=(0,e.useRef)(void 0),u=(0,e.useRef)(0),c=(0,e.useRef)(!1);return o?{onBefore:function(){c.current||(u.current=0),c.current=!1,i.current&&clearTimeout(i.current)},onSuccess:function(){u.current=0},onError:function(){if(u.current+=1,-1===o||u.current<=o){var e=null!=r?r:Math.min(1e3*Math.pow(2,u.current),3e4);i.current=setTimeout((function(){c.current=!0,t.refresh()}),e)}else u.current=0},onCancel:function(){u.current=0,i.current&&clearTimeout(i.current)}}:{}};var z=r(823),H=r.n(z);const V=function(t,n){var r=n.throttleWait,o=n.throttleLeading,i=n.throttleTrailing,u=(0,e.useRef)(void 0),c={};return void 0!==o&&(c.leading=o),void 0!==i&&(c.trailing=i),(0,e.useEffect)((function(){if(r){var e=t.runAsync.bind(t);return u.current=H()((function(e){e()}),r,c),t.runAsync=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new Promise((function(n,r){var o;null===(o=u.current)||void 0===o||o.call(u,(function(){e.apply(void 0,l([],s(t),!1)).then(n).catch(r)}))}))},function(){var n;t.runAsync=e,null===(n=u.current)||void 0===n||n.cancel()}}}),[r,o,i]),r?{onCancel:function(){var e;null===(e=u.current)||void 0===e||e.cancel()}}:{}},Y=function(t){(0,e.useEffect)((function(){null==t||t()}),[])},B=function(){var t=s((0,e.useState)({}),2)[1];return(0,e.useCallback)((function(){return t({})}),[])};var W=function(){function e(e,t,r,o){void 0===o&&(o={}),this.serviceRef=e,this.options=t,this.subscribe=r,this.initState=o,this.count=0,this.state={loading:!1,params:void 0,data:void 0,error:void 0},this.state=n(n(n({},this.state),{loading:!t.manual}),o)}return e.prototype.setState=function(e){void 0===e&&(e={}),this.state=n(n({},this.state),e),this.subscribe()},e.prototype.runPluginHandler=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r=this.pluginImpls.map((function(n){var r;return null===(r=n[e])||void 0===r?void 0:r.call.apply(r,l([n],s(t),!1))})).filter(Boolean);return Object.assign.apply(Object,l([{}],s(r),!1))},e.prototype.runAsync=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return u(this,void 0,void 0,(function(){var t,r,o,u,a,f,d,v,h,p,m,g,y,b,w,E,S,_,O,T,x;return c(this,(function(c){switch(c.label){case 0:if(this.count+=1,t=this.count,r=this.runPluginHandler("onBefore",e),o=r.stopNow,u=void 0!==o&&o,a=r.returnNow,f=void 0!==a&&a,d=i(r,["stopNow","returnNow"]),u)return[2,new Promise((function(){}))];if(this.setState(n({loading:!0,params:e},d)),f)return[2,Promise.resolve(d.data)];null===(y=(g=this.options).onBefore)||void 0===y||y.call(g,e),c.label=1;case 1:return c.trys.push([1,3,,4]),(v=this.runPluginHandler("onRequest",this.serviceRef.current,e).servicePromise)||(v=(m=this.serviceRef).current.apply(m,l([],s(e),!1))),[4,v];case 2:return h=c.sent(),t!==this.count?[2,new Promise((function(){}))]:(this.setState({data:h,error:void 0,loading:!1}),null===(w=(b=this.options).onSuccess)||void 0===w||w.call(b,h,e),this.runPluginHandler("onSuccess",h,e),null===(S=(E=this.options).onFinally)||void 0===S||S.call(E,e,h,void 0),t===this.count&&this.runPluginHandler("onFinally",e,h,void 0),[2,h]);case 3:if(p=c.sent(),t!==this.count)return[2,new Promise((function(){}))];throw this.setState({error:p,loading:!1}),null===(O=(_=this.options).onError)||void 0===O||O.call(_,p,e),this.runPluginHandler("onError",p,e),null===(x=(T=this.options).onFinally)||void 0===x||x.call(T,e,void 0,p),t===this.count&&this.runPluginHandler("onFinally",e,void 0,p),p;case 4:return[2]}}))}))},e.prototype.run=function(){for(var e=this,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];this.runAsync.apply(this,l([],s(t),!1)).catch((function(t){e.options.onError||console.error(t)}))},e.prototype.cancel=function(){this.count+=1,this.setState({loading:!1}),this.runPluginHandler("onCancel")},e.prototype.refresh=function(){this.run.apply(this,l([],s(this.state.params||[]),!1))},e.prototype.refreshAsync=function(){return this.runAsync.apply(this,l([],s(this.state.params||[]),!1))},e.prototype.mutate=function(e){var t=f(e)?e(this.state.data):e;this.runPluginHandler("onMutate",t),this.setState({data:t})},e}();const q=W,U=function(e,t,r){return function(e,t,r){void 0===t&&(t={}),void 0===r&&(r=[]);var o=t.manual,u=void 0!==o&&o,c=t.ready,a=void 0===c||c,f=i(t,["manual","ready"]),d=n({manual:u,ready:a},f),v=w(e),p=B(),m=b((function(){var e=r.map((function(e){var t;return null===(t=null==e?void 0:e.onInit)||void 0===t?void 0:t.call(e,d)})).filter(Boolean);return new q(v,d,p,Object.assign.apply(Object,l([{}],s(e),!1)))}),[]);return m.options=d,m.pluginImpls=r.map((function(e){return e(m,d)})),Y((function(){if(!u&&a){var e=m.state.params||t.defaultParams||[];m.run.apply(m,l([],s(e),!1))}})),E((function(){m.cancel()})),{loading:m.state.loading,data:m.state.data,error:m.state.error,params:m.state.params||[],cancel:h(m.cancel.bind(m)),refresh:h(m.refresh.bind(m)),refreshAsync:h(m.refreshAsync.bind(m)),run:h(m.run.bind(m)),runAsync:h(m.runAsync.bind(m)),mutate:h(m.mutate.bind(m))}}(e,t,l(l([],s(r||[]),!1),[A,L,F,P,V,g,M,$],!1))},X=function(t,r){var o;void 0===r&&(r={});var u=r.defaultPageSize,c=void 0===u?10:u,a=r.defaultCurrent,f=void 0===a?1:a,d=i(r,["defaultPageSize","defaultCurrent"]),v=U(t,n({defaultParams:[{current:f,pageSize:c}],refreshDepsAction:function(){_(1)}},d)),p=v.params[0]||{},m=p.current,g=void 0===m?1:m,y=p.pageSize,b=void 0===y?c:y,w=(null===(o=v.data)||void 0===o?void 0:o.total)||0,E=(0,e.useMemo)((function(){return Math.ceil(w/b)}),[b,w]),S=function(e,t){var r=e<=0?1:e,o=t<=0?1:t,i=Math.ceil(w/o);r>i&&(r=Math.max(1,i));var u=s(v.params||[]),c=u[0],a=void 0===c?{}:c,f=u.slice(1);v.run.apply(v,l([n(n({},a),{current:r,pageSize:o})],s(f),!1))},_=function(e){S(e,b)};return n(n({},v),{pagination:{current:g,pageSize:b,total:w,totalPage:E,onChange:h(S),changeCurrent:h(_),changePageSize:h((function(e){S(g,e)}))}})},G=function(t,r){var o;void 0===r&&(r={});var u=r.form,c=r.defaultType,a=void 0===c?"simple":c,f=r.defaultParams,d=r.manual,v=void 0!==d&&d,m=r.refreshDeps,g=void 0===m?[]:m,y=r.ready,b=void 0===y||y,w=i(r,["form","defaultType","defaultParams","manual","refreshDeps","ready"]),E=X(t,n(n({ready:b,manual:!0},w),{onSuccess:function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];L.current=!0,null===(e=w.onSuccess)||void 0===e||e.call.apply(e,l([w],s(t),!1))}})),S=E.params,_=void 0===S?[]:S,O=E.run,T=_[2]||{},x=s((0,e.useState)((null==T?void 0:T.type)||a),2),M=x[0],R=x[1],k=(0,e.useRef)({}),A=(0,e.useRef)([]),L=(0,e.useRef)(!1),C=!!(null==u?void 0:u.getInternalHooks),D=function(){if(!u)return{};if(C)return u.getFieldsValue(null,(function(){return!0}));var e=u.getFieldsValue(),t={};return Object.keys(e).forEach((function(n){u.getFieldInstance&&!u.getFieldInstance(n)||(t[n]=e[n])})),t},I=function(){if(u){if(C)return u.setFieldsValue(k.current);var e={};Object.keys(k.current).forEach((function(t){u.getFieldInstance&&!u.getFieldInstance(t)||(e[t]=k.current[t])})),u.setFieldsValue(e)}},F=function(e){b&&setTimeout((function(){(function(){if(!u)return Promise.resolve({});var e=D(),t=Object.keys(e);return C?u.validateFields(t):new Promise((function(e,n){u.validateFields(t,(function(t,r){t?n(t):e(r)}))}))})().then((function(t){void 0===t&&(t={});var o=e||n(n({pageSize:r.defaultPageSize||10},(null==_?void 0:_[0])||{}),{current:1});u?(k.current=n(n({},k.current),t),O(o,t,{allFormData:k.current,type:M})):O(o)})).catch((function(e){return e}))}))};(0,e.useEffect)((function(){if(_.length>0)return k.current=(null==T?void 0:T.allFormData)||{},I(),void O.apply(void 0,l([],s(_),!1));b&&(k.current=(null==f?void 0:f[1])||{},I(),v||F(null==f?void 0:f[0]))}),[]),p((function(){b&&I()}),[M]);var N=(0,e.useRef)(!1);return N.current=!1,p((function(){!v&&b&&(N.current=!0,u&&u.resetFields(),k.current=(null==f?void 0:f[1])||{},I(),F(null==f?void 0:f[0]))}),[b]),p((function(){N.current||b&&(v||(N.current=!0,E.pagination.changeCurrent(1)))}),l([],s(g),!1)),n(n({},E),{tableProps:{dataSource:(null===(o=E.data)||void 0===o?void 0:o.list)||A.current,loading:E.loading,onChange:h((function(e,t,r,o){var i=s(_||[]),u=i[0],c=i.slice(1);O.apply(void 0,l([n(n({},u),{current:e.current,pageSize:e.pageSize,filters:t,sorter:r,extra:o})],s(c),!1))})),pagination:{current:E.pagination.current,pageSize:E.pagination.pageSize,total:E.pagination.total}},search:{submit:h((function(e){var t,o,i;null===(t=null==e?void 0:e.preventDefault)||void 0===t||t.call(e),F(L.current?void 0:n({pageSize:r.defaultPageSize||(null===(i=null===(o=r.defaultParams)||void 0===o?void 0:o[0])||void 0===i?void 0:i.pageSize)||10,current:1},(null==f?void 0:f[0])||{}))})),type:M,changeType:h((function(){var e=D();k.current=n(n({},k.current),e),R((function(e){return"simple"===e?"advance":"simple"}))})),reset:h((function(){var e,t;u&&u.resetFields(),F(n(n({},(null==f?void 0:f[0])||{}),{pageSize:r.defaultPageSize||(null===(t=null===(e=r.defaultParams)||void 0===e?void 0:e[0])||void 0===t?void 0:t.pageSize)||10,current:1}))}))}})},K=function(t,n){(0,e.useEffect)((function(){var e=t(),n=!1;return function(){u(this,void 0,void 0,(function(){return c(this,(function(t){switch(t.label){case 0:if(!f(e[Symbol.asyncIterator]))return[3,4];t.label=1;case 1:return[4,e.next()];case 2:return t.sent().done||n?[3,3]:[3,1];case 3:return[3,6];case 4:return[4,e];case 5:t.sent(),t.label=6;case 6:return[2]}}))}))}(),function(){n=!0}}),n)},J=function(t,n){void 0===t&&(t=!1);var r=s((0,e.useState)(t),2),o=r[0],i=r[1];return[o,(0,e.useMemo)((function(){var e=void 0===n?!t:n;return{toggle:function(){return i((function(n){return n===t?e:t}))},set:function(e){return i(e)},setLeft:function(){return i(t)},setRight:function(){return i(e)}}}),[])]};function Z(t){void 0===t&&(t=!1);var n=s(J(!!t),2),r=n[0],o=n[1],i=o.toggle,u=o.set;return[r,(0,e.useMemo)((function(){return{toggle:i,set:function(e){return u(!!e)},setTrue:function(){return u(!0)},setFalse:function(){return u(!1)}}}),[])]}function Q(e,t){if(C)return e?f(e)?e():"current"in e?e.current:e:t}const ee=function(e){if(!e||!document.getRootNode)return document;var t,n=Array.isArray(e)?e:[e];return function(e){return e.every((function(e){var t=Q(e);return!!t&&t.getRootNode()instanceof ShadowRoot}))}(n)&&(t=Q(n[0]))?t.getRootNode():document},te=function(t){return function(n,r,o){var i=(0,e.useRef)(!1),u=(0,e.useRef)([]),c=(0,e.useRef)([]),a=(0,e.useRef)(void 0);t((function(){var e,t=(Array.isArray(o)?o:[o]).map((function(e){return Q(e)}));if(!i.current)return i.current=!0,u.current=t,c.current=r,void(a.current=n());t.length===u.current.length&&y(u.current,t)&&y(c.current,r)||(null===(e=a.current)||void 0===e||e.call(a),u.current=t,c.current=r,a.current=n())})),E((function(){var e;null===(e=a.current)||void 0===e||e.call(a),i.current=!1}))}},ne=te(e.useEffect);function re(e,t,n){void 0===n&&(n="click");var r=w(e);ne((function(){var e=function(e){(Array.isArray(t)?t:[t]).some((function(t){var n=Q(t);return!n||n.contains(e.target)}))||r.current(e)},o=ee(t),i=Array.isArray(n)?n:[n];return i.forEach((function(t){return o.addEventListener(t,e)})),function(){i.forEach((function(t){return o.removeEventListener(t,e)}))}}),Array.isArray(n)?n:[n],t)}const oe=function(t,n){void 0===n&&(n={});var r=null!=t?t:{},o=n.defaultValue,i=n.defaultValuePropName,u=void 0===i?"defaultValue":i,c=n.valuePropName,a=void 0===c?"value":c,d=n.trigger,v=void 0===d?"onChange":d,p=r[a],m=Object.prototype.hasOwnProperty.call(r,a),g=(0,e.useMemo)((function(){return m?p:Object.prototype.hasOwnProperty.call(r,u)?r[u]:o}),[]),y=(0,e.useRef)(g);m&&(y.current=p);var b=B();return[y.current,h((function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var o=f(e)?e(y.current):e;m||(y.current=o,b()),r[v]&&r[v].apply(r,l([o],s(t),!1))}))]};function ie(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)e[r]=n[r]}return e}var ue=function e(t,n){function r(e,r,o){if("undefined"!=typeof document){"number"==typeof(o=ie({},n,o)).expires&&(o.expires=new Date(Date.now()+864e5*o.expires)),o.expires&&(o.expires=o.expires.toUTCString()),e=encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var i="";for(var u in o)o[u]&&(i+="; "+u,!0!==o[u]&&(i+="="+o[u].split(";")[0]));return document.cookie=e+"="+t.write(r,e)+i}}return Object.create({set:r,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var n=document.cookie?document.cookie.split("; "):[],r={},o=0;o<n.length;o++){var i=n[o].split("="),u=i.slice(1).join("=");try{var c=decodeURIComponent(i[0]);if(r[c]=t.read(u,c),e===c)break}catch(e){}}return e?r[e]:r}},remove:function(e,t){r(e,"",ie({},t,{expires:-1}))},withAttributes:function(t){return e(this.converter,ie({},this.attributes,t))},withConverter:function(t){return e(ie({},this.converter,t),this.attributes)}},{attributes:{value:Object.freeze(n)},converter:{value:Object.freeze(t)}})}({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"});const ce=function(t,r){void 0===r&&(r={});var o=s((0,e.useState)((function(){var e=ue.get(t);return d(e)?e:f(r.defaultValue)?r.defaultValue():r.defaultValue})),2),u=o[0],c=o[1],a=h((function(e,o){void 0===o&&(o={});var a=n(n({},r),o),s=(a.defaultValue,i(a,["defaultValue"])),l=f(e)?e(u):e;c(l),void 0===l?ue.remove(t):ue.set(t,l,s)}));return[u,a]};var ae=r(300),se=r.n(ae),le=function(e){if(!e)return 0;var t=se()(e).valueOf()-Date.now();return t<0?0:t};const fe=function(t){void 0===t&&(t={});var n=t||{},r=n.leftTime,o=n.targetDate,i=n.interval,u=void 0===i?1e3:i,c=n.onEnd,a=(0,e.useMemo)((function(){return v(r)&&r>0?Date.now()+r:void 0}),[r]),l="leftTime"in t?a:o,f=s((0,e.useState)((function(){return le(l)})),2),d=f[0],h=f[1],p=w(c);(0,e.useEffect)((function(){if(l){h(le(l));var e=setInterval((function(){var t,n=le(l);h(n),0===n&&(clearInterval(e),null===(t=p.current)||void 0===t||t.call(p))}),u);return function(){return clearInterval(e)}}h(0)}),[l,u]);var m=(0,e.useMemo)((function(){return e=d,{days:Math.floor(e/864e5),hours:Math.floor(e/36e5)%24,minutes:Math.floor(e/6e4)%60,seconds:Math.floor(e/1e3)%60,milliseconds:Math.floor(e)%1e3};var e}),[d]);return[d,m]};function de(e,t){void 0===t&&(t={});var n=t.min,r=t.max,o=e;return v(r)&&(o=Math.min(r,o)),v(n)&&(o=Math.max(n,o)),o}const ve=function(t,n){void 0===t&&(t=0),void 0===n&&(n={});var r=n.min,o=n.max,i=s((0,e.useState)((function(){return de(t,{min:r,max:o})})),2),u=i[0],c=i[1],a=function(e){c((function(t){return de(v(e)?e:e(t),{max:o,min:r})}))};return[u,{inc:h((function(e){void 0===e&&(e=1),a((function(t){return t+e}))})),dec:h((function(e){void 0===e&&(e=1),a((function(t){return t-e}))})),set:h((function(e){a(e)})),reset:h((function(){a(t)}))}]},he=function(t,n){var r,o=w(t),i=null!==(r=null==n?void 0:n.wait)&&void 0!==r?r:1e3,u=(0,e.useMemo)((function(){return k()((function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return o.current.apply(o,l([],s(e),!1))}),i,n)}),[]);return E((function(){u.cancel()})),{run:u,cancel:u.cancel,flush:u.flush}},pe=function(t,n){var r=s((0,e.useState)(t),2),o=r[0],i=r[1],u=he((function(){i(t)}),n).run;return(0,e.useEffect)((function(){u()}),[t]),o},me=function(t,n,r){var o=s((0,e.useState)({}),2),i=o[0],u=o[1],c=he((function(){u({})}),r).run;(0,e.useEffect)((function(){return c()}),n),p(t,[i])};var ge=r(727),ye=r.n(ge),be=function(e,t){return void 0===e&&(e=[]),void 0===t&&(t=[]),ye()(e,t)},we=function(t){return function(n,r){var o=(0,e.useRef)(void 0),i=(0,e.useRef)(0);void 0!==r&&be(r,o.current)||(i.current+=1),o.current=r,t(n,[i.current])}};const Ee=we(e.useEffect),Se=we(e.useLayoutEffect),_e=function(e,t,n){void 0===n&&(n={});var r=n.enable,o=void 0===r||r,i=w(t);ne((function(){if(o){var t=Q(n.target,window);if(null==t?void 0:t.addEventListener){var r=function(e){return i.current(e)},u=Array.isArray(e)?e:[e];return u.forEach((function(e){t.addEventListener(e,r,{capture:n.capture,once:n.once,passive:n.passive})})),function(){u.forEach((function(e){t.removeEventListener(e,r,{capture:n.capture})}))}}}}),[e,n.capture,n.once,n.passive,o],n.target)};var Oe=function(){return C?document.visibilityState:"visible"};const Te=function(){var t=s((0,e.useState)(Oe),2),n=t[0],r=t[1];return _e("visibilitychange",(function(){r(Oe())}),{target:function(){return document}}),n},xe=function(t,n,r){void 0===r&&(r={});var o=w(r),i=w(t),u=(0,e.useRef)(void 0),c=o.current.dragImage;Y((function(){if(null==c?void 0:c.image){var e=c.image;if(d(e)){var t=new Image;t.src=e,u.current=t}else u.current=e}})),ne((function(){var e=Q(n);if(null==e?void 0:e.addEventListener){var t=function(e){var t,n;if(null===(n=(t=o.current).onDragStart)||void 0===n||n.call(t,e),e.dataTransfer.setData("custom",JSON.stringify(i.current)),(null==c?void 0:c.image)&&u.current){var r=c.offsetX,a=void 0===r?0:r,s=c.offsetY,l=void 0===s?0:s;e.dataTransfer.setDragImage(u.current,a,l)}},r=function(e){var t,n;null===(n=(t=o.current).onDragEnd)||void 0===n||n.call(t,e)};return e.setAttribute("draggable","true"),e.addEventListener("dragstart",t),e.addEventListener("dragend",r),function(){e.removeEventListener("dragstart",t),e.removeEventListener("dragend",r)}}}),[],n)},Me=function(t,n){void 0===n&&(n={});var r=w(n),o=(0,e.useRef)(void 0);ne((function(){var e=Q(t);if(null==e?void 0:e.addEventListener){var n=function(e,t){var n=e.getData("text/uri-list"),o=e.getData("custom");if(o&&r.current.onDom){var i=o;try{i=JSON.parse(o)}catch(e){i=o}r.current.onDom(i,t)}else n&&r.current.onUri?r.current.onUri(n,t):e.files&&e.files.length&&r.current.onFiles?r.current.onFiles(Array.from(e.files),t):e.items&&e.items.length&&r.current.onText&&e.items[0].getAsString((function(e){r.current.onText(e,t)}))},i=function(e){var t,n;e.preventDefault(),e.stopPropagation(),o.current=e.target,null===(n=(t=r.current).onDragEnter)||void 0===n||n.call(t,e)},u=function(e){var t,n;e.preventDefault(),null===(n=(t=r.current).onDragOver)||void 0===n||n.call(t,e)},c=function(e){var t,n;e.target===o.current&&(null===(n=(t=r.current).onDragLeave)||void 0===n||n.call(t,e))},a=function(e){var t,o;e.preventDefault(),n(e.dataTransfer,e),null===(o=(t=r.current).onDrop)||void 0===o||o.call(t,e)},s=function(e){var t,o;n(e.clipboardData,e),null===(o=(t=r.current).onPaste)||void 0===o||o.call(t,e)};return e.addEventListener("dragenter",i),e.addEventListener("dragover",u),e.addEventListener("dragleave",c),e.addEventListener("drop",a),e.addEventListener("paste",s),function(){e.removeEventListener("dragenter",i),e.removeEventListener("dragover",u),e.removeEventListener("dragleave",c),e.removeEventListener("drop",a),e.removeEventListener("paste",s)}}}),[],t)},Re=function(t){void 0===t&&(t=[]);var n=(0,e.useRef)(-1),r=(0,e.useRef)([]),o=(0,e.useCallback)((function(e){n.current+=1,r.current.splice(e,0,n.current)}),[]),i=s((0,e.useState)((function(){return t.forEach((function(e,t){o(t)})),t})),2),u=i[0],c=i[1],a=(0,e.useCallback)((function(e){r.current=[],c((function(){return e.forEach((function(e,t){o(t)})),e}))}),[]),f=(0,e.useCallback)((function(e,t){c((function(n){var r=l([],s(n),!1);return r.splice(e,0,t),o(e),r}))}),[]),d=(0,e.useCallback)((function(e){return r.current[e]}),[]),v=(0,e.useCallback)((function(e){return r.current.findIndex((function(t){return t===e}))}),[]),h=(0,e.useCallback)((function(e,t){c((function(n){var r=l([],s(n),!1);return t.forEach((function(t,n){o(e+n)})),r.splice.apply(r,l([e,0],s(t),!1)),r}))}),[]),p=(0,e.useCallback)((function(e,t){c((function(n){var r=l([],s(n),!1);return r[e]=t,r}))}),[]),m=(0,e.useCallback)((function(e){c((function(t){var n=l([],s(t),!1);n.splice(e,1);try{r.current.splice(e,1)}catch(e){console.error(e)}return n}))}),[]),g=(0,e.useCallback)((function(e){Array.isArray(e)&&e.length&&c((function(t){var n=[],o=t.filter((function(t,r){var o=!e.includes(r);return o&&n.push(d(r)),o}));return r.current=n,o}))}),[]),y=(0,e.useCallback)((function(e,t){e!==t&&c((function(n){var o=l([],s(n),!1),i=o.filter((function(t,n){return n!==e}));i.splice(t,0,o[e]);try{var u=r.current.filter((function(t,n){return n!==e}));u.splice(t,0,r.current[e]),r.current=u}catch(e){console.error(e)}return i}))}),[]),b=(0,e.useCallback)((function(e){c((function(t){return o(t.length),t.concat([e])}))}),[]),w=(0,e.useCallback)((function(){try{r.current=r.current.slice(0,r.current.length-1)}catch(e){console.error(e)}c((function(e){return e.slice(0,e.length-1)}))}),[]),E=(0,e.useCallback)((function(e){c((function(t){return o(0),[e].concat(t)}))}),[]),S=(0,e.useCallback)((function(){try{r.current=r.current.slice(1,r.current.length)}catch(e){console.error(e)}c((function(e){return e.slice(1,e.length)}))}),[]),_=(0,e.useCallback)((function(e){return e.map((function(e,t){return{key:t,item:e}})).sort((function(e,t){return v(e.key)-v(t.key)})).filter((function(e){return!!e.item})).map((function(e){return e.item}))}),[]);return{list:u,insert:f,merge:h,replace:p,remove:m,batchRemove:g,getKey:d,getIndex:v,move:y,push:b,pop:w,unshift:E,shift:S,sortList:_,resetList:a}};var ke=function(){var t=this;this.subscriptions=new Set,this.emit=function(e){var n,r;try{for(var o=a(t.subscriptions),i=o.next();!i.done;i=o.next())(0,i.value)(e)}catch(e){n={error:e}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}},this.useSubscription=function(n){var r=(0,e.useRef)(void 0);r.current=n,(0,e.useEffect)((function(){function e(e){r.current&&r.current(e)}return t.subscriptions.add(e),function(){t.subscriptions.delete(e)}}),[])}};const Ae=function(){var t=(0,e.useRef)(void 0);return t.current||(t.current=new ke),t.current},Le=function(t){var n=t||{},r=n.initialValue,o=n.transformer,i=s((0,e.useState)(r),2),u=i[0],c=i[1],a=w(o),l=(0,e.useCallback)((function(){return c(r)}),[]);return[u,{onChange:(0,e.useCallback)((function(e){var t=e.target.value;return f(a.current)?c(a.current(t)):c(t)}),[]),reset:l}]};var Ce={};const De=function(t,n){var r=s((0,e.useState)(t?"loading":"unset"),2),o=r[0],i=r[1],u=(0,e.useRef)(void 0);return(0,e.useEffect)((function(){if(t){var e=t.replace(/[|#].*$/,"");if("css"===(null==n?void 0:n.type)||!(null==n?void 0:n.type)&&/(^css!|\.css$)/.test(e)){var r=function(e,t){void 0===t&&(t={});var n=document.querySelector('link[href="'.concat(e,'"]'));if(!n){var r=document.createElement("link");return r.rel="stylesheet",r.href=e,Object.keys(t).forEach((function(e){r[e]=t[e]})),"hideFocus"in r&&r.relList&&(r.rel="preload",r.as="style"),r.setAttribute("data-status","loading"),document.head.appendChild(r),{ref:r,status:"loading"}}return{ref:n,status:n.getAttribute("data-status")||"ready"}}(t,null==n?void 0:n.css);u.current=r.ref,i(r.status)}else"js"===(null==n?void 0:n.type)||!(null==n?void 0:n.type)&&/(^js!|\.js$)/.test(e)?(r=function(e,t){void 0===t&&(t={});var n=document.querySelector('script[src="'.concat(e,'"]'));if(!n){var r=document.createElement("script");return r.src=e,Object.keys(t).forEach((function(e){r[e]=t[e]})),r.setAttribute("data-status","loading"),document.body.appendChild(r),{ref:r,status:"loading"}}return{ref:n,status:n.getAttribute("data-status")||"ready"}}(t,null==n?void 0:n.js),u.current=r.ref,i(r.status)):console.error("Cannot infer the type of external resource, and please provide a type ('js' | 'css'). Refer to the https://ahooks.js.org/hooks/dom/use-external/#options");if(u.current){void 0===Ce[t]?Ce[t]=1:Ce[t]+=1;var o=function(e){var t,n="load"===e.type?"ready":"error";null===(t=u.current)||void 0===t||t.setAttribute("data-status",n),i(n)};return u.current.addEventListener("load",o),u.current.addEventListener("error",o),function(){var e,r,i;null===(e=u.current)||void 0===e||e.removeEventListener("load",o),null===(r=u.current)||void 0===r||r.removeEventListener("error",o),Ce[t]-=1,0!==Ce[t]||(null==n?void 0:n.keepWhenUnused)||null===(i=u.current)||void 0===i||i.remove(),u.current=void 0}}}else i("unset")}),[t]),o};var Ie={SVG:"image/svg+xml",ICO:"image/x-icon",GIF:"image/gif",PNG:"image/png"};const Fe=function(t){(0,e.useEffect)((function(){if(t){var e=t.split("."),n=e[e.length-1].toLocaleUpperCase(),r=document.querySelector("link[rel*='icon']")||document.createElement("link");r.type=Ie[n],r.href=t,r.rel="shortcut icon",document.getElementsByTagName("head")[0].appendChild(r)}}),[t])};function Ne(t,n){var r=s((0,e.useState)(!1),2),o=r[0],i=r[1],u=n||{},c=u.onFocus,a=u.onBlur,l=u.onChange;return _e("focusin",(function(e){o||(null==c||c(e),null==l||l(!0),i(!0))}),{target:t}),_e("focusout",(function(e){var t,n;o&&!(null===(n=null===(t=e.currentTarget)||void 0===t?void 0:t.contains)||void 0===n?void 0:n.call(t,e.relatedTarget))&&(null==a||a(e),null==l||l(!1),i(!1))}),{target:t}),o}var je=r(763),Pe=r.n(je);const $e=function(t,n){var r=n||{},o=r.onExit,i=r.onEnter,u=r.pageFullscreen,c=void 0!==u&&u,a="boolean"!=typeof c&&c?c:{},l=a.className,f=void 0===l?"ahooks-page-fullscreen":l,d=a.zIndex,v=void 0===d?999999:d,p=w(o),m=w(i),g=s((0,e.useState)(S),2),y=g[0],b=g[1],E=(0,e.useRef)(S());function S(){return Pe().isEnabled&&!!Pe().element&&Pe().element===Q(t)}var _=function(e){E.current!==e&&(function(e){var t,n;e?null===(t=m.current)||void 0===t||t.call(m):null===(n=p.current)||void 0===n||n.call(p)}(e),b(e),E.current=e)},O=function(){var e=S();_(e)},T=function(e){var n=Q(t);if(n){var r=document.getElementById(f);e?(n.classList.add(f),r||((r=document.createElement("style")).setAttribute("id",f),r.textContent="\n          .".concat(f," {\n            position: fixed; left: 0; top: 0; right: 0; bottom: 0;\n            width: 100% !important; height: 100% !important;\n            z-index: ").concat(v,";\n          }"),n.appendChild(r))):(n.classList.remove(f),r&&r.remove()),_(e)}},x=function(){var e=Q(t);if(e)if(c)T(!0);else if(Pe().isEnabled)try{Pe().request(e)}catch(e){console.error(e)}},M=function(){var e=Q(t);e&&(c?T(!1):Pe().isEnabled&&Pe().element===e&&Pe().exit())};return(0,e.useEffect)((function(){if(Pe().isEnabled&&!c)return Pe().on("change",O),function(){Pe().off("change",O)}}),[]),[y,{enterFullscreen:h(x),exitFullscreen:h(M),toggleFullscreen:h((function(){y?M():x()})),isEnabled:Pe().isEnabled}]},ze=function(e,t){void 0===t&&(t={});var r,o,i,u,c=G(e,n(n({},t),{form:t.field?(r=t.field,{getFieldInstance:function(e){return r.getNames().includes(e)},setFieldsValue:r.setValues,getFieldsValue:r.getValues,resetFields:r.resetToDefault,validateFields:function(e,t){r.validate(e,t)}}):void 0}));return i={dataSource:(o=c).tableProps.dataSource,loading:o.tableProps.loading,onSort:function(e,t){var n;o.tableProps.onChange({current:o.pagination.current,pageSize:o.pagination.pageSize},null===(n=o.params[0])||void 0===n?void 0:n.filters,{field:e,order:t})},onFilter:function(e){var t;o.tableProps.onChange({current:o.pagination.current,pageSize:o.pagination.pageSize},e,null===(t=o.params[0])||void 0===t?void 0:t.sorter)}},u={onChange:o.pagination.changeCurrent,onPageSizeChange:o.pagination.changePageSize,current:o.pagination.current,pageSize:o.pagination.pageSize,total:o.pagination.total},n(n({},o),{tableProps:i,paginationProps:u})},He=function(t){var n=s((0,e.useState)(t),2),r=n[0],o=n[1],i=w(r);return[r,o,(0,e.useCallback)((function(){return i.current}),[])]};var Ve=function(e,t){var n=function(e,t){var n=e>0?e-1:t.length+e;return n>=t.length-1&&(n=t.length-1),n<0&&(n=0),n}(e,t);return{_current:t[n],_before:t.slice(0,n),_after:t.slice(n+1)}};function Ye(t,n){void 0===n&&(n=0);var r=s((0,e.useState)({present:t,past:[],future:[]}),2),o=r[0],i=r[1],u=o.present,c=o.past,a=o.future,f=(0,e.useRef)(t),d=function(e){var t=v(e)?e:Number(e);if(0!==t)return t>0?function(e){if(void 0===e&&(e=1),0!==a.length){var t=Ve(e,a),n=t._before,r=t._current,o=t._after;i({past:l(l(l([],s(c),!1),[u],!1),s(n),!1),present:r,future:o})}}(t):void function(e){if(void 0===e&&(e=-1),0!==c.length){var t=Ve(e,c),n=t._before,r=t._current,o=t._after;i({past:n,present:r,future:l(l(l([],s(o),!1),[u],!1),s(a),!1)})}}(t)};return{value:u,backLength:c.length,forwardLength:a.length,setValue:h((function(e){var t=l(l([],s(c),!1),[u],!1),r=v(n)?n:Number(n);r>0&&t.length>r&&t.splice(0,1),i({present:e,future:[],past:t})})),go:h(d),back:h((function(){d(-1)})),forward:h((function(){d(1)})),reset:h((function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=e.length>0?e[0]:f.current;f.current=n,i({present:n,future:[],past:[]})}))}}const Be=function(e,t){var n=t||{},r=n.onEnter,o=n.onLeave,i=n.onChange,u=s(Z(!1),2),c=u[0],a=u[1],l=a.setTrue,f=a.setFalse;return _e("mouseenter",(function(){null==r||r(),l(),null==i||i(!0)}),{target:e}),_e("mouseleave",(function(){null==o||o(),f(),null==i||i(!1)}),{target:e}),c};var We=function(e){return e.scrollHeight||Math.max(document.documentElement.scrollHeight,document.body.scrollHeight)};const qe=function(t,r){void 0===r&&(r={});var o=r.target,i=r.isNoMore,a=r.threshold,f=void 0===a?100:a,d=r.direction,v=void 0===d?"bottom":d,m=r.reloadDeps,g=void 0===m?[]:m,y=r.manual,b=r.onBefore,w=r.onSuccess,E=r.onError,S=r.onFinally,_=s((0,e.useState)(),2),O=_[0],T=_[1],x=s((0,e.useState)(!1),2),M=x[0],R=x[1],k="top"===v,A=(0,e.useRef)(void 0),L=(0,e.useRef)(0),C=(0,e.useMemo)((function(){return!!i&&i(O)}),[O]),D=U((function(e){return u(void 0,void 0,void 0,(function(){var r,o,i,u;return c(this,(function(c){switch(c.label){case 0:return[4,t(e)];case 1:return r=c.sent(),T(n(n({},r),e?{list:k?l(l([],s(r.list),!1),s(null!==(i=e.list)&&void 0!==i?i:[]),!1):l(l([],s(null!==(u=e.list)&&void 0!==u?u:[]),!1),s(r.list),!1)}:{list:l([],s(null!==(o=r.list)&&void 0!==o?o:[]),!1)})),[2,r]}}))}))}),{manual:y,onFinally:function(e,t,n){R(!1),null==S||S(t,n)},onBefore:function(){return null==b?void 0:b()},onSuccess:function(e){setTimeout((function(){if(k){var e=Q(o);if(e=e===document?document.documentElement:e){var t=We(e);e.scrollTo(0,t-L.current)}}else H()})),null==w||w(e)},onError:function(e){return null==E?void 0:E(e)}}),I=D.loading,F=D.error,N=D.run,j=D.runAsync,P=D.cancel,$=h((function(){C||(R(!0),N(O))})),z=h((function(){return C?Promise.reject():(R(!0),j(O))})),H=function(){var e=Q(o);if(e){var t=e===document?document.documentElement:e,n=function(e){return e===document||e===document.documentElement||e===document.body?Math.max(window.pageYOffset,document.documentElement.scrollTop,document.body.scrollTop):e.scrollTop}(t),r=We(t),i=function(e){return e.clientHeight||Math.max(document.documentElement.clientHeight,document.body.clientHeight)}(t);k?(void 0!==A.current&&A.current>n&&n<=f&&$(),A.current=n,L.current=r-n):r-n<=i+f&&$()}};return _e("scroll",(function(){I||M||H()}),{target:o}),p((function(){N()}),l([],s(g),!1)),{data:O,loading:!M&&I,error:F,loadingMore:M,noMore:C,loadMore:$,loadMoreAsync:z,reload:h((function(){return R(!1),N()})),reloadAsync:h((function(){return R(!1),j()})),mutate:T,cancel:P}},Ue=function(t,n,r){void 0===r&&(r={});var o=h(t),i=(0,e.useRef)(null),u=(0,e.useCallback)((function(){i.current&&clearInterval(i.current)}),[]);return(0,e.useEffect)((function(){if(v(n)&&!(n<0))return r.immediate&&o(),i.current=setInterval(o,n),u}),[n,r.immediate]),u};r(735);const Xe=function(t,r){var o=r||{},u=o.callback,c=i(o,["callback"]),l=s((0,e.useState)(),2),f=l[0],d=l[1],v=s((0,e.useState)(),2),h=v[0],p=v[1];return ne((function(){var e=(Array.isArray(t)?t:[t]).map((function(e){return Q(e)})).filter(Boolean);if(e.length){var o=new IntersectionObserver((function(e){var t,n;try{for(var r=a(e),o=r.next();!o.done;o=r.next()){var i=o.value;p(i.intersectionRatio),d(i.isIntersecting),null==u||u(i)}}catch(e){t={error:e}}finally{try{o&&!o.done&&(n=r.return)&&n.call(r)}finally{if(t)throw t.error}}}),n(n({},c),{root:Q(null==r?void 0:r.root)}));return e.forEach((function(e){return o.observe(e)})),function(){o.disconnect()}}}),[null==r?void 0:r.rootMargin,null==r?void 0:r.threshold,u],t),[f,h]},Ge=C?e.useLayoutEffect:e.useEffect,Ke=function(t,n,r){var o=(0,e.useRef)(void 0),i=(0,e.useRef)(0);be(n,o.current)||(i.current+=1),o.current=n,ne(t,[i.current],r)};var Je={0:48,1:49,2:50,3:51,4:52,5:53,6:54,7:55,8:56,9:57,backspace:8,tab:9,enter:13,shift:16,ctrl:17,alt:18,pausebreak:19,capslock:20,esc:27,space:32,pageup:33,pagedown:34,end:35,home:36,leftarrow:37,uparrow:38,rightarrow:39,downarrow:40,insert:45,delete:46,a:65,b:66,c:67,d:68,e:69,f:70,g:71,h:72,i:73,j:74,k:75,l:76,m:77,n:78,o:79,p:80,q:81,r:82,s:83,t:84,u:85,v:86,w:87,x:88,y:89,z:90,leftwindowkey:91,rightwindowkey:92,meta:/(mac|iphone|ipod|ipad)/i.test("undefined"!=typeof navigator?null===navigator||void 0===navigator?void 0:navigator.platform:"")?[91,93]:[91,92],selectkey:93,numpad0:96,numpad1:97,numpad2:98,numpad3:99,numpad4:100,numpad5:101,numpad6:102,numpad7:103,numpad8:104,numpad9:105,multiply:106,add:107,subtract:109,decimalpoint:110,divide:111,f1:112,f2:113,f3:114,f4:115,f5:116,f6:117,f7:118,f8:119,f9:120,f10:121,f11:122,f12:123,numlock:144,scrolllock:145,semicolon:186,equalsign:187,comma:188,dash:189,period:190,forwardslash:191,graveaccent:192,openbracket:219,backslash:220,closebracket:221,singlequote:222},Ze={ctrl:function(e){return e.ctrlKey},shift:function(e){return e.shiftKey},alt:function(e){return e.altKey},meta:function(e){return"keyup"===e.type?Je.meta.includes(e.keyCode):e.metaKey}};function Qe(e){return d(e)||v(e)}function et(e,t,n){var r,o;if(!e.key)return!1;if(v(t))return e.keyCode===t&&t;var i=t.split("."),u=0;try{for(var c=a(i),s=c.next();!s.done;s=c.next()){var l=s.value,f=Ze[l],d=Je[l.toLowerCase()];(f&&f(e)||d&&d===e.keyCode)&&u++}}catch(e){r={error:e}}finally{try{s&&!s.done&&(o=c.return)&&o.call(c)}finally{if(r)throw r.error}}return n?u===i.length&&function(e){var t=Object.keys(Ze).reduce((function(t,n){return Ze[n](e)?t+1:t}),0);return[16,17,18,91,92].includes(e.keyCode)?t:t+1}(e)===i.length&&t:u===i.length&&t}var tt=["keydown"];const nt=function(e,t,n){var r=n||{},o=r.events,i=void 0===o?tt:o,u=r.target,c=r.exactMatch,s=void 0!==c&&c,l=r.useCapture,d=void 0!==l&&l,v=w(t),h=w(e);Ke((function(){var e,t,n,r=Q(u,window);if(r){var o=function(e){var t,n=function(e,t){return f(e)?e:Qe(e)?function(n){return et(n,e,t)}:Array.isArray(e)?function(n){return e.find((function(e){return et(n,e,t)}))}:function(){return Boolean(e)}}(h.current,s),r=n(e),o=Qe(r)?r:e.key;if(r)return null===(t=v.current)||void 0===t?void 0:t.call(v,e,o)};try{for(var c=a(i),l=c.next();!l.done;l=c.next()){var p=l.value;null===(n=null==r?void 0:r.addEventListener)||void 0===n||n.call(r,p,o,d)}}catch(t){e={error:t}}finally{try{l&&!l.done&&(t=c.return)&&t.call(c)}finally{if(e)throw e.error}}return function(){var e,t,n;try{for(var u=a(i),c=u.next();!c.done;c=u.next()){var s=c.value;null===(n=null==r?void 0:r.removeEventListener)||void 0===n||n.call(r,s,o,d)}}catch(t){e={error:t}}finally{try{c&&!c.done&&(t=u.return)&&t.call(u)}finally{if(e)throw e.error}}}}}),[i],u)};var rt="AHOOKS_SYNC_STORAGE_EVENT_NAME";function ot(t){return function(n,r){var o;void 0===r&&(r={});var i=r.listenStorageChange,u=void 0!==i&&i,c=r.onError,a=void 0===c?function(e){console.error(e)}:c;try{o=t()}catch(e){a(e)}function l(){try{var e=null==o?void 0:o.getItem(n);if(e)return t=e,r.deserializer?r.deserializer(t):JSON.parse(t)}catch(e){a(e)}var t;return f(r.defaultValue)?r.defaultValue():r.defaultValue}var d=s((0,e.useState)(l),2),v=d[0],m=d[1];p((function(){m(l())}),[n]);var g=function(e){e.key===n&&e.storageArea===o&&m(l())};return _e("storage",g,{enable:u}),_e(rt,(function(e){g(e.detail)}),{enable:u}),[v,h((function(e){var t=f(e)?e(v):e;u||m(t);try{var i=void 0,c=null==o?void 0:o.getItem(n);!function(e){return void 0===e}(t)?(i=function(e){return r.serializer?r.serializer(e):JSON.stringify(e)}(t),null==o||o.setItem(n,i)):(i=null,null==o||o.removeItem(n)),dispatchEvent(new CustomEvent(rt,{detail:{key:n,newValue:i,oldValue:c,storageArea:o}}))}catch(e){a(e)}}))]}}const it=ot((function(){return C?localStorage:void 0})),ut=function(t){var n=this,r=(0,e.useRef)(!1);return(0,e.useCallback)((function(){for(var e=[],o=0;o<arguments.length;o++)e[o]=arguments[o];return u(n,void 0,void 0,(function(){return c(this,(function(n){switch(n.label){case 0:if(r.current)return[2];r.current=!0,n.label=1;case 1:return n.trys.push([1,3,4,5]),[4,t.apply(void 0,l([],s(e),!1))];case 2:return[2,n.sent()];case 3:throw n.sent();case 4:return r.current=!1,[7];case 5:return[2]}}))}))}),[t])},ct=function(t,n,r){var o=void 0===r?{}:r,i=o.delay,u=void 0===i?300:i,c=o.moveThreshold,a=o.onClick,s=o.onLongPressEnd,l=w(t),f=w(a),d=w(s),v=(0,e.useRef)(void 0),h=(0,e.useRef)(!1),p=(0,e.useRef)({x:0,y:0}),m=(0,e.useRef)(!1),g=(0,e.useRef)(!1),y=!!((null==c?void 0:c.x)&&c.x>0||(null==c?void 0:c.y)&&c.y>0);ne((function(){var e=Q(n);if(null==e?void 0:e.addEventListener){var t=function(e){v.current=setTimeout((function(){l.current(e),h.current=!0}),u)},r=function(e){if(!g.current){if(g.current=!0,y){var n=w(e),r=n.clientX,o=n.clientY;p.current.x=r,p.current.y=o}t(e)}},o=function(e){var n;(null===(n=null==e?void 0:e.sourceCapabilities)||void 0===n?void 0:n.firesTouchEvents)||(m.current=!0,y&&(p.current.x=e.clientX,p.current.y=e.clientY),t(e))},i=function(e){v.current&&function(e){var t=w(e),n=t.clientX,r=t.clientY,o=Math.abs(n-p.current.x),i=Math.abs(r-p.current.y);return!!((null==c?void 0:c.x)&&o>c.x||(null==c?void 0:c.y)&&i>c.y)}(e)&&(clearTimeout(v.current),v.current=void 0)},a=function(e){var t;g.current&&(g.current=!1,v.current&&(clearTimeout(v.current),v.current=void 0),h.current?null===(t=d.current)||void 0===t||t.call(d,e):f.current&&f.current(e),h.current=!1)},s=function(e){var t,n;(null===(t=null==e?void 0:e.sourceCapabilities)||void 0===t?void 0:t.firesTouchEvents)||m.current&&(m.current=!1,v.current&&(clearTimeout(v.current),v.current=void 0),h.current?null===(n=d.current)||void 0===n||n.call(d,e):f.current&&f.current(e),h.current=!1)},b=function(e){var t;m.current&&(m.current=!1,v.current&&(clearTimeout(v.current),v.current=void 0),h.current&&(null===(t=d.current)||void 0===t||t.call(d,e),h.current=!1))};return e.addEventListener("mousedown",o),e.addEventListener("mouseup",s),e.addEventListener("mouseleave",b),e.addEventListener("touchstart",r),e.addEventListener("touchend",a),y&&(e.addEventListener("mousemove",i),e.addEventListener("touchmove",i)),function(){v.current&&(clearTimeout(v.current),h.current=!1),e.removeEventListener("mousedown",o),e.removeEventListener("mouseup",s),e.removeEventListener("mouseleave",b),e.removeEventListener("touchstart",r),e.removeEventListener("touchend",a),y&&(e.removeEventListener("mousemove",i),e.removeEventListener("touchmove",i))}}function w(e){return"TouchEvent"in window&&e instanceof TouchEvent?{clientX:e.touches[0].clientX,clientY:e.touches[0].clientY}:e instanceof MouseEvent?{clientX:e.clientX,clientY:e.clientY}:{clientX:0,clientY:0}}}),[],n)},at=function(t){var n=function(){return new Map(t)},r=s((0,e.useState)(n),2),o=r[0],i=r[1];return[o,{set:h((function(e,t){i((function(n){var r=new Map(n);return r.set(e,t),r}))})),setAll:h((function(e){i(new Map(e))})),remove:h((function(e){i((function(t){var n=new Map(t);return n.delete(e),n}))})),reset:h((function(){return i(n())})),get:h((function(e){return o.get(e)}))}]},st=function(t){var n=(0,e.useRef)(0),r=s((0,e.useState)(t),2),o=r[0],i=r[1],u=(0,e.useCallback)((function(e){cancelAnimationFrame(n.current),n.current=requestAnimationFrame((function(){i(e)}))}),[]);return E((function(){cancelAnimationFrame(n.current)})),[o,u]};var lt={screenX:NaN,screenY:NaN,clientX:NaN,clientY:NaN,pageX:NaN,pageY:NaN,elementX:NaN,elementY:NaN,elementH:NaN,elementW:NaN,elementPosX:NaN,elementPosY:NaN};const ft=function(e){var t=s(st(lt),2),n=t[0],r=t[1];return _e("mousemove",(function(t){var n=t.screenX,o=t.screenY,i=t.clientX,u=t.clientY,c=t.pageX,a=t.pageY,s={screenX:n,screenY:o,clientX:i,clientY:u,pageX:c,pageY:a,elementX:NaN,elementY:NaN,elementH:NaN,elementW:NaN,elementPosX:NaN,elementPosY:NaN},l=Q(e);if(l){var f=l.getBoundingClientRect(),d=f.left,v=f.top,h=f.width,p=f.height;s.elementPosX=d+window.pageXOffset,s.elementPosY=v+window.pageYOffset,s.elementX=c-s.elementPosX,s.elementY=a-s.elementPosY,s.elementW=h,s.elementH=p}r(s)}),{target:function(){return document}}),n};var dt;function vt(){var e,t=navigator;return null===(e=t)||"object"!=typeof e?null:t.connection||t.mozConnection||t.webkitConnection}function ht(){var e=vt();return e?{rtt:e.rtt,type:e.type,saveData:e.saveData,downlink:e.downlink,downlinkMax:e.downlinkMax,effectiveType:e.effectiveType}:{}}!function(e){e.ONLINE="online",e.OFFLINE="offline",e.CHANGE="change"}(dt||(dt={}));const pt=function(){var t=s((0,e.useState)((function(){return n({since:void 0,online:null===navigator||void 0===navigator?void 0:navigator.onLine},ht())})),2),r=t[0],o=t[1];return(0,e.useEffect)((function(){var e=function(){o((function(e){return n(n({},e),{online:!0,since:new Date})}))},t=function(){o((function(e){return n(n({},e),{online:!1,since:new Date})}))},r=function(){o((function(e){return n(n({},e),ht())}))};window.addEventListener(dt.ONLINE,e),window.addEventListener(dt.OFFLINE,t);var i=vt();return null==i||i.addEventListener(dt.CHANGE,r),function(){window.removeEventListener(dt.ONLINE,e),window.removeEventListener(dt.OFFLINE,t),null==i||i.removeEventListener(dt.CHANGE,r)}}),[]),r};var mt=function(e,t){return!Object.is(e,t)};const gt=function(t,n){void 0===n&&(n=mt);var r=(0,e.useRef)(void 0),o=(0,e.useRef)(void 0);return n(o.current,t)&&(r.current=o.current,o.current=t),r.current};const yt=function(t,n,r){var o=null==r?void 0:r.immediate,i=w(t),u=(0,e.useRef)(void 0),c=(0,e.useCallback)((function(){u.current&&function(e){if(e.id,"undefined"==typeof cancelAnimationFrame)return clearInterval(e.id);cancelAnimationFrame(e.id)}(u.current)}),[]);return(0,e.useEffect)((function(){if(v(n)&&!(n<0))return o&&i.current(),u.current=function(e,t){if(void 0===t&&(t=0),"undefined"==typeof requestAnimationFrame)return{id:setInterval(e,t)};var n=Date.now(),r={id:0},o=function(){Date.now()-n>=t&&(e(),n=Date.now()),r.id=requestAnimationFrame(o)};return r.id=requestAnimationFrame(o),r}((function(){i.current()}),n),c}),[n]),c};const bt=function(t,n){var r=w(t),o=(0,e.useRef)(void 0),i=(0,e.useCallback)((function(){o.current&&function(e){if(e.id,"undefined"==typeof cancelAnimationFrame)return clearTimeout(e.id);cancelAnimationFrame(e.id)}(o.current)}),[]);return(0,e.useEffect)((function(){if(v(n)&&!(n<0))return o.current=function(e,t){if(void 0===t&&(t=0),"undefined"==typeof requestAnimationFrame)return{id:setTimeout(e,t)};var n={id:0},r=Date.now(),o=function(){Date.now()-r>=t?e():n.id=requestAnimationFrame(o)};return n.id=requestAnimationFrame(o),n}((function(){r.current()}),n),i}),[n]),i};var wt=r(108),Et=r.n(wt),St=new WeakMap,_t=new WeakMap;function Ot(e,t){var n=St.get(e);if(n)return n;if(_t.has(e))return e;var r=new Proxy(e,{get:function(e,n,r){var o=Reflect.get(e,n,r),i=Reflect.getOwnPropertyDescriptor(e,n);return((null==i?void 0:i.configurable)||(null==i?void 0:i.writable))&&(Et()(o)||Array.isArray(o))?Ot(o,t):o},set:function(e,n,r){var o=Reflect.set(e,n,r);return t(),o},deleteProperty:function(e,n){var r=Reflect.deleteProperty(e,n);return t(),r}});return St.set(e,r),_t.set(r,e),r}const Tt=function(t){var n=B(),r=(0,e.useRef)(t);return b((function(){return Ot(r.current,(function(){n()}))}),[])},xt=function(t){var n=(0,e.useRef)(t),r=b((function(){return f(n.current)?n.current():n.current}),[]),o=s((0,e.useState)(r),2),i=o[0],u=o[1],c=h((function(){u(r)}));return[i,u,c]};var Mt,Rt=new Set,kt={xs:0,sm:576,md:768,lg:992,xl:1200};function At(){var e,t,n=Mt;if(Ct(),n!==Mt)try{for(var r=a(Rt),o=r.next();!o.done;o=r.next())(0,o.value)()}catch(t){e={error:t}}finally{try{o&&!o.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}}var Lt=!1;function Ct(){var e,t,n=window.innerWidth,r={},o=!1;try{for(var i=a(Object.keys(kt)),u=i.next();!u.done;u=i.next()){var c=u.value;r[c]=n>=kt[c],r[c]!==Mt[c]&&(o=!0)}}catch(t){e={error:t}}finally{try{u&&!u.done&&(t=i.return)&&t.call(i)}finally{if(e)throw e.error}}o&&(Mt=r)}function Dt(e){kt=e,Mt&&Ct()}const It=function(){C&&!Lt&&(Mt={},Ct(),window.addEventListener("resize",At),Lt=!0);var t=s((0,e.useState)(Mt),2),n=t[0],r=t[1];return(0,e.useEffect)((function(){if(C){Lt||window.addEventListener("resize",At);var e=function(){r(Mt)};return Rt.add(e),function(){Rt.delete(e),0===Rt.size&&(window.removeEventListener("resize",At),Lt=!1)}}}),[]),n},Ft=function(){var t=(0,e.useRef)(!1);return(0,e.useEffect)((function(){return t.current=!1,function(){t.current=!0}}),[]),t},Nt=function(t){var n=Ft(),r=s((0,e.useState)(t),2),o=r[0],i=r[1];return[o,(0,e.useCallback)((function(e){n.current||i(e)}),[])]},jt=function(e,t){void 0===t&&(t=function(){return!0});var n=s(st(),2),r=n[0],o=n[1],i=w(t);return ne((function(){var t=Q(e,document);if(t){var n=function(){var e;e=t===document?document.scrollingElement?{left:document.scrollingElement.scrollLeft,top:document.scrollingElement.scrollTop}:{left:Math.max(window.pageXOffset,document.documentElement.scrollLeft,document.body.scrollLeft),top:Math.max(window.pageYOffset,document.documentElement.scrollTop,document.body.scrollTop)}:{left:t.scrollLeft,top:t.scrollTop},i.current(e)&&o(e)};return n(),t.addEventListener("scroll",n),function(){t.removeEventListener("scroll",n)}}}),[],e),r},Pt=function(t,n){var r,o,i,u=[];Array.isArray(n)?u=n:Et()(n)&&(u=null!==(r=null==n?void 0:n.defaultSelected)&&void 0!==r?r:u,i=null!==(o=null==n?void 0:n.itemKey)&&void 0!==o?o:i);var c=function(e){return f(i)?i(e):d(i)&&Et()(e)?e[i]:e},a=s((0,e.useState)(u),2),l=a[0],v=a[1],p=(0,e.useMemo)((function(){var e=new Map;return Array.isArray(l)?(l.forEach((function(t){e.set(c(t),t)})),e):e}),[l]),m=function(e){return p.has(c(e))},g=function(e){p.set(c(e),e),v(Array.from(p.values()))},y=function(e){p.delete(c(e)),v(Array.from(p.values()))},b=function(){t.forEach((function(e){p.set(c(e),e)})),v(Array.from(p.values()))},w=function(){t.forEach((function(e){p.delete(c(e))})),v(Array.from(p.values()))},E=(0,e.useMemo)((function(){return t.every((function(e){return!p.has(c(e))}))}),[t,p]),S=(0,e.useMemo)((function(){return t.every((function(e){return p.has(c(e))}))&&!E}),[t,p,E]),_=(0,e.useMemo)((function(){return!E&&!S}),[E,S]);return{selected:l,noneSelected:E,allSelected:S,partiallySelected:_,setSelected:v,isSelected:m,select:h(g),unSelect:h(y),toggle:h((function(e){m(e)?y(e):g(e)})),selectAll:h(b),unSelectAll:h(w),clearAll:h((function(){p.clear(),v([])})),toggleAll:h((function(){return S?w():b()}))}},$t=ot((function(){return C?sessionStorage:void 0})),zt=function(t){var n=function(){return new Set(t)},r=s((0,e.useState)(n),2),o=r[0],i=r[1];return[o,{add:h((function(e){o.has(e)||i((function(t){var n=new Set(t);return n.add(e),n}))})),remove:h((function(e){o.has(e)&&i((function(t){var n=new Set(t);return n.delete(e),n}))})),reset:h((function(){return i(n())}))}]},Ht=function(t){var r=s((0,e.useState)(t),2),o=r[0],i=r[1];return[o,h((function(e){i((function(t){var r=f(e)?e(t):e;return r?n(n({},t),r):t}))}))]};var Vt=function(){if("undefined"!=typeof Map)return Map;function e(e,t){var n=-1;return e.some((function(e,r){return e[0]===t&&(n=r,!0)})),n}return function(){function t(){this.__entries__=[]}return Object.defineProperty(t.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),t.prototype.get=function(t){var n=e(this.__entries__,t),r=this.__entries__[n];return r&&r[1]},t.prototype.set=function(t,n){var r=e(this.__entries__,t);~r?this.__entries__[r][1]=n:this.__entries__.push([t,n])},t.prototype.delete=function(t){var n=this.__entries__,r=e(n,t);~r&&n.splice(r,1)},t.prototype.has=function(t){return!!~e(this.__entries__,t)},t.prototype.clear=function(){this.__entries__.splice(0)},t.prototype.forEach=function(e,t){void 0===t&&(t=null);for(var n=0,r=this.__entries__;n<r.length;n++){var o=r[n];e.call(t,o[1],o[0])}},t}()}(),Yt="undefined"!=typeof window&&"undefined"!=typeof document&&window.document===document,Bt=void 0!==r.g&&r.g.Math===Math?r.g:"undefined"!=typeof self&&self.Math===Math?self:"undefined"!=typeof window&&window.Math===Math?window:Function("return this")(),Wt="function"==typeof requestAnimationFrame?requestAnimationFrame.bind(Bt):function(e){return setTimeout((function(){return e(Date.now())}),1e3/60)},qt=["top","right","bottom","left","width","height","size","weight"],Ut="undefined"!=typeof MutationObserver,Xt=function(){function e(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=function(e){var t=!1,n=!1,r=0;function o(){t&&(t=!1,e()),n&&u()}function i(){Wt(o)}function u(){var e=Date.now();if(t){if(e-r<2)return;n=!0}else t=!0,n=!1,setTimeout(i,20);r=e}return u}(this.refresh.bind(this))}return e.prototype.addObserver=function(e){~this.observers_.indexOf(e)||this.observers_.push(e),this.connected_||this.connect_()},e.prototype.removeObserver=function(e){var t=this.observers_,n=t.indexOf(e);~n&&t.splice(n,1),!t.length&&this.connected_&&this.disconnect_()},e.prototype.refresh=function(){this.updateObservers_()&&this.refresh()},e.prototype.updateObservers_=function(){var e=this.observers_.filter((function(e){return e.gatherActive(),e.hasActive()}));return e.forEach((function(e){return e.broadcastActive()})),e.length>0},e.prototype.connect_=function(){Yt&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),Ut?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},e.prototype.disconnect_=function(){Yt&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},e.prototype.onTransitionEnd_=function(e){var t=e.propertyName,n=void 0===t?"":t;qt.some((function(e){return!!~n.indexOf(e)}))&&this.refresh()},e.getInstance=function(){return this.instance_||(this.instance_=new e),this.instance_},e.instance_=null,e}(),Gt=function(e,t){for(var n=0,r=Object.keys(t);n<r.length;n++){var o=r[n];Object.defineProperty(e,o,{value:t[o],enumerable:!1,writable:!1,configurable:!0})}return e},Kt=function(e){return e&&e.ownerDocument&&e.ownerDocument.defaultView||Bt},Jt=nn(0,0,0,0);function Zt(e){return parseFloat(e)||0}function Qt(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return t.reduce((function(t,n){return t+Zt(e["border-"+n+"-width"])}),0)}var en="undefined"!=typeof SVGGraphicsElement?function(e){return e instanceof Kt(e).SVGGraphicsElement}:function(e){return e instanceof Kt(e).SVGElement&&"function"==typeof e.getBBox};function tn(e){return Yt?en(e)?function(e){var t=e.getBBox();return nn(0,0,t.width,t.height)}(e):function(e){var t=e.clientWidth,n=e.clientHeight;if(!t&&!n)return Jt;var r=Kt(e).getComputedStyle(e),o=function(e){for(var t={},n=0,r=["top","right","bottom","left"];n<r.length;n++){var o=r[n],i=e["padding-"+o];t[o]=Zt(i)}return t}(r),i=o.left+o.right,u=o.top+o.bottom,c=Zt(r.width),a=Zt(r.height);if("border-box"===r.boxSizing&&(Math.round(c+i)!==t&&(c-=Qt(r,"left","right")+i),Math.round(a+u)!==n&&(a-=Qt(r,"top","bottom")+u)),!function(e){return e===Kt(e).document.documentElement}(e)){var s=Math.round(c+i)-t,l=Math.round(a+u)-n;1!==Math.abs(s)&&(c-=s),1!==Math.abs(l)&&(a-=l)}return nn(o.left,o.top,c,a)}(e):Jt}function nn(e,t,n,r){return{x:e,y:t,width:n,height:r}}var rn=function(){function e(e){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=nn(0,0,0,0),this.target=e}return e.prototype.isActive=function(){var e=tn(this.target);return this.contentRect_=e,e.width!==this.broadcastWidth||e.height!==this.broadcastHeight},e.prototype.broadcastRect=function(){var e=this.contentRect_;return this.broadcastWidth=e.width,this.broadcastHeight=e.height,e},e}(),on=function(e,t){var n,r,o,i,u,c,a,s=(r=(n=t).x,o=n.y,i=n.width,u=n.height,c="undefined"!=typeof DOMRectReadOnly?DOMRectReadOnly:Object,a=Object.create(c.prototype),Gt(a,{x:r,y:o,width:i,height:u,top:o,right:r+i,bottom:u+o,left:r}),a);Gt(this,{target:e,contentRect:s})},un=function(){function e(e,t,n){if(this.activeObservations_=[],this.observations_=new Vt,"function"!=typeof e)throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=e,this.controller_=t,this.callbackCtx_=n}return e.prototype.observe=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof Kt(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)||(t.set(e,new rn(e)),this.controller_.addObserver(this),this.controller_.refresh())}},e.prototype.unobserve=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof Kt(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)&&(t.delete(e),t.size||this.controller_.removeObserver(this))}},e.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},e.prototype.gatherActive=function(){var e=this;this.clearActive(),this.observations_.forEach((function(t){t.isActive()&&e.activeObservations_.push(t)}))},e.prototype.broadcastActive=function(){if(this.hasActive()){var e=this.callbackCtx_,t=this.activeObservations_.map((function(e){return new on(e.target,e.broadcastRect())}));this.callback_.call(e,t,e),this.clearActive()}},e.prototype.clearActive=function(){this.activeObservations_.splice(0)},e.prototype.hasActive=function(){return this.activeObservations_.length>0},e}(),cn="undefined"!=typeof WeakMap?new WeakMap:new Vt,an=function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var n=Xt.getInstance(),r=new un(t,n,this);cn.set(this,r)};["observe","unobserve","disconnect"].forEach((function(e){an.prototype[e]=function(){var t;return(t=cn.get(this))[e].apply(t,arguments)}}));const sn=void 0!==Bt.ResizeObserver?Bt.ResizeObserver:an;var ln=te(e.useLayoutEffect);const fn=C?ln:ne,dn=function(e){var t=s(st((function(){var t=Q(e);return t?{width:t.clientWidth,height:t.clientHeight}:void 0})),2),n=t[0],r=t[1];return fn((function(){var t=Q(e);if(t){var n=new sn((function(e){e.forEach((function(e){var t=e.target,n=t.clientWidth,o=t.clientHeight;r({width:n,height:o})}))}));return n.observe(t),function(){n.disconnect()}}}),[],e),n};var vn={top:NaN,left:NaN,bottom:NaN,right:NaN,height:NaN,width:NaN},hn=n({text:""},vn);const pn=function(t){var r=s((0,e.useState)(hn),2),o=r[0],i=r[1],u=(0,e.useRef)(o),c=(0,e.useRef)(!1);return u.current=o,ne((function(){var e=Q(t,document);if(e){var r=function(){var e,t=null,r=vn;window.getSelection&&(e=(t=window.getSelection())?t.toString():"")&&c.current&&(r=function(e){if(!e)return vn;if(e.rangeCount<1)return vn;var t=e.getRangeAt(0).getBoundingClientRect();return{height:t.height,width:t.width,top:t.top,left:t.left,right:t.right,bottom:t.bottom}}(t),i(n(n(n({},o),{text:e}),r)))},a=function(t){if(2!==t.button&&window.getSelection){u.current.text&&i(n({},hn)),c.current=!1;var r=window.getSelection();r&&(r.removeAllRanges(),c.current=e.contains(t.target))}};return e.addEventListener("mouseup",r),document.addEventListener("mousedown",a),function(){e.removeEventListener("mouseup",r),document.removeEventListener("mousedown",a)}}}),[],t),o},mn=function(t,n){var r,o=w(t),i=null!==(r=null==n?void 0:n.wait)&&void 0!==r?r:1e3,u=(0,e.useMemo)((function(){return H()((function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return o.current.apply(o,l([],s(e),!1))}),i,n)}),[]);return E((function(){u.cancel()})),{run:u,cancel:u.cancel,flush:u.flush}},gn=function(t,n){var r=s((0,e.useState)(t),2),o=r[0],i=r[1],u=mn((function(){i(t)}),n).run;return(0,e.useEffect)((function(){u()}),[t]),o},yn=function(t,n,r){var o=s((0,e.useState)({}),2),i=o[0],u=o[1],c=mn((function(){u({})}),r).run;(0,e.useEffect)((function(){return c()}),n),p(t,[i])},bn=function(t,n){var r=h(t),o=(0,e.useRef)(null),i=(0,e.useCallback)((function(){o.current&&clearTimeout(o.current)}),[]);return(0,e.useEffect)((function(){if(v(n)&&!(n<0))return o.current=setTimeout(r,n),i}),[n]),i};var wn={restoreOnUnmount:!1};const En=function(t,n){void 0===n&&(n=wn);var r=(0,e.useRef)(C?document.title:"");(0,e.useEffect)((function(){document.title=t}),[t]),E((function(){n.restoreOnUnmount&&(document.title=r.current)}))},Sn=function(t,n){var r=(0,e.useRef)(void 0);(0,e.useEffect)((function(){var e,o,i=(e=r.current,o=n,e?e.map((function(t,n){return Object.is(e[n],null==o?void 0:o[n])?-1:n})).filter((function(e){return e>=0})):o?o.map((function(e,t){return t})):[]),u=r.current;return r.current=n,t(i,u,n)}),n)},_n=t(e.useLayoutEffect),On=function(t,n){var r=n.containerTarget,o=n.wrapperTarget,i=n.itemHeight,u=n.overscan,c=void 0===u?5:u,a=w(i),l=dn(r),f=(0,e.useRef)(!1),d=s((0,e.useState)([]),2),m=d[0],g=d[1],y=s((0,e.useState)({}),2),b=y[0],E=y[1],S=function(e){return v(a.current)?e*a.current:t.slice(0,e).reduce((function(e,n,r){return e+a.current(r,t[r])}),0)},_=(0,e.useMemo)((function(){return v(a.current)?t.length*a.current:t.reduce((function(e,n,r){return e+a.current(r,t[r])}),0)}),[t]),O=function(){var e=Q(r);if(e){var n=e.scrollTop,o=e.clientHeight,i=function(e){if(v(a.current))return Math.floor(e/a.current);for(var n=0,r=0,o=0;o<t.length;o++)if((n+=a.current(o,t[o]))>=e){r=o;break}return r+1}(n),u=function(e,n){if(v(a.current))return Math.ceil(e/a.current);for(var r=0,o=0,i=n;i<t.length&&(o=i,!((r+=a.current(i,t[i]))>=e));i++);return o-n}(o,i),s=Math.max(0,i-c),l=Math.min(t.length,i+u+c),f=S(s);E({height:_-f+"px",marginTop:f+"px"}),g(t.slice(s,l).map((function(e,t){return{data:e,index:t+s}})))}};return p((function(){var e=Q(o);e&&Object.keys(b).forEach((function(t){return e.style[t]=b[t]}))}),[b]),(0,e.useEffect)((function(){(null==l?void 0:l.width)&&(null==l?void 0:l.height)&&O()}),[null==l?void 0:l.width,null==l?void 0:l.height,t]),_e("scroll",(function(e){f.current?f.current=!1:(e.preventDefault(),O())}),{target:r}),[m,h((function(e){var t=Q(r);t&&(f.current=!0,t.scrollTop=S(e),O())}))]};var Tn;!function(e){e[e.Connecting=0]="Connecting",e[e.Open=1]="Open",e[e.Closing=2]="Closing",e[e.Closed=3]="Closed"}(Tn||(Tn={}));const xn=function(t,n){void 0===n&&(n={});var r=n.reconnectLimit,o=void 0===r?3:r,i=n.reconnectInterval,u=void 0===i?3e3:i,c=n.manual,a=void 0!==c&&c,l=n.onOpen,f=n.onClose,d=n.onMessage,v=n.onError,p=n.protocols,m=w(l),g=w(f),y=w(d),b=w(v),S=(0,e.useRef)(0),_=(0,e.useRef)(void 0),O=(0,e.useRef)(void 0),T=s((0,e.useState)(),2),x=T[0],M=T[1],R=s((0,e.useState)(Tn.Closed),2),k=R[0],A=R[1],L=function(){var e;S.current<o&&(null===(e=O.current)||void 0===e?void 0:e.readyState)!==Tn.Open&&(_.current&&clearTimeout(_.current),_.current=setTimeout((function(){C(),S.current++}),u))},C=function(){_.current&&clearTimeout(_.current),O.current&&O.current.close();var e=new WebSocket(t,p);A(Tn.Connecting),e.onerror=function(t){var n;O.current===e&&(L(),null===(n=b.current)||void 0===n||n.call(b,t,e),A(e.readyState||Tn.Closed))},e.onopen=function(t){var n;O.current===e&&(null===(n=m.current)||void 0===n||n.call(m,t,e),S.current=0,A(e.readyState||Tn.Open))},e.onmessage=function(t){var n;O.current===e&&(null===(n=y.current)||void 0===n||n.call(y,t,e),M(t))},e.onclose=function(t){var n;null===(n=g.current)||void 0===n||n.call(g,t,e),O.current===e&&L(),O.current&&O.current!==e||A(e.readyState||Tn.Closed)},O.current=e},D=function(){S.current=0,C()},I=function(){var e;_.current&&clearTimeout(_.current),S.current=o,null===(e=O.current)||void 0===e||e.close(),O.current=void 0};return(0,e.useEffect)((function(){!a&&t&&D()}),[t,a]),E((function(){I()})),{latestMessage:x,sendMessage:h((function(e){var t;if(k!==Tn.Open)throw new Error("WebSocket disconnected");null===(t=O.current)||void 0===t||t.send(e)})),connect:h(D),disconnect:h(I),readyState:k,webSocketIns:O.current}},Mn=function(t,r){var o=(0,e.useRef)({});(0,e.useEffect)((function(){if(o.current){var e=Object.keys(n(n({},o.current),r)),i={};e.forEach((function(e){Object.is(o.current[e],r[e])||(i[e]={from:o.current[e],to:r[e]})})),Object.keys(i).length&&console.log("[why-did-you-update]",t,i)}o.current=r}))},Rn=function(e,t,n){void 0===n&&(n={});var r=w(e);Ke((function(){var e=Q(t);if(e){var o=new MutationObserver(r.current);return o.observe(e,n),function(){null==o||o.disconnect()}}}),[n],t)};var kn;!function(e){e.LIGHT="light",e.DARK="dark",e.SYSTEM="system"}(kn||(kn={}));var An=function(){var t=C?window.matchMedia("(prefers-color-scheme: dark)"):void 0,n=s((0,e.useState)((function(){return C&&(null==t?void 0:t.matches)?kn.DARK:kn.LIGHT})),2),r=n[0],o=n[1];return(0,e.useEffect)((function(){var e=function(e){e.matches?o(kn.DARK):o(kn.LIGHT)};return null==t||t.addEventListener("change",e),function(){null==t||t.removeEventListener("change",e)}}),[]),r};function Ln(t){void 0===t&&(t={});var n=t.localStorageKey,r=s((0,e.useState)((function(){return(null==n?void 0:n.length)&&localStorage.getItem(n)||kn.SYSTEM})),2),o=r[0],i=r[1],u=An();return{theme:o===kn.SYSTEM?u:o,themeMode:o,setThemeMode:h((function(e){i(e),(null==n?void 0:n.length)&&localStorage.setItem(n,e)}))}}})(),o})()));