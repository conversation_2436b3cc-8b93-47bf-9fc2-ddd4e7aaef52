import React, { useState, useEffect, useRef } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import './MiningPage.scss';

interface MiningStats {
  hashrate: number;
  totalEarnings: number;
  currentPower: number;
  miningTime: number;
  isMining: boolean;
}

const MiningPage: React.FC = () => {
  const swiperRef = useRef<any>(null);
  const [miningStats, setMiningStats] = useState<MiningStats>({
    hashrate: 0,
    totalEarnings: 0,
    currentPower: 100,
    miningTime: 0,
    isMining: false
  });

  useEffect(() => {
    // 完全按照原始JavaScript逻辑
    const updateButtonState = () => {
      if (swiperRef.current) {
        const swiper = swiperRef.current.swiper;
        const prevButton = document.querySelector(".custom-button-prev") as HTMLElement;
        const nextButton = document.querySelector(".custom-button-next") as HTMLElement;

        if (swiper.isBeginning) {
          if (prevButton) prevButton.style.display = "none";
        } else {
          if (prevButton) prevButton.style.display = "block";
        }

        if (swiper.isEnd) {
          if (nextButton) nextButton.style.display = "none";
        } else {
          if (nextButton) nextButton.style.display = "block";
        }
      }
    };

    // 初始化按钮状态
    updateButtonState();
  }, []);

  useEffect(() => {
    // 挖矿计时器
    let interval: NodeJS.Timeout;
    if (miningStats.isMining) {
      interval = setInterval(() => {
        setMiningStats(prev => ({
          ...prev,
          miningTime: prev.miningTime + 1,
          hashrate: prev.hashrate + Math.floor(Math.random() * 10) + 1,
          totalEarnings: prev.totalEarnings + (prev.currentPower * 0.0001)
        }));
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [miningStats.isMining]);

  const handleSlideChange = () => {
    if (swiperRef.current) {
      const swiper = swiperRef.current.swiper;
      const prevButton = document.querySelector(".custom-button-prev") as HTMLElement;
      const nextButton = document.querySelector(".custom-button-next") as HTMLElement;

      if (swiper.isBeginning) {
        if (prevButton) prevButton.style.display = "none";
      } else {
        if (prevButton) prevButton.style.display = "block";
      }

      if (swiper.isEnd) {
        if (nextButton) nextButton.style.display = "none";
      } else {
        if (nextButton) nextButton.style.display = "block";
      }
    }
  };

  const handleStartMining = () => {
    setMiningStats(prev => ({
      ...prev,
      isMining: true,
      miningTime: 0
    }));
  };

  const handleStopMining = () => {
    setMiningStats(prev => ({
      ...prev,
      isMining: false
    }));
  };

  const handleExchange = () => {
    if (miningStats.hashrate > 0) {
      alert(`成功兑换 ${miningStats.hashrate.toLocaleString()} 哈希值！`);
      setMiningStats(prev => ({
        ...prev,
        hashrate: 0
      }));
    } else {
      alert('没有可兑换的哈希值');
    }
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <>
      <header className="header">
        <div className="container">
          <p className="header__text">CoreX MINER</p>
          <a href="https://t.me/Core_xbot" className="header__link">
            Cloud Mining in Telegram
          </a>
        </div>
      </header>
      
      <main>
        <section>
          <div className="container">
            {/* 挖矿状态卡片 */}
            <div className="mining-status-card">
              <div className="mining-status-header">
                <h2>挖矿状态</h2>
                <div className={`status-indicator ${miningStats.isMining ? 'active' : 'inactive'}`}>
                  {miningStats.isMining ? '运行中' : '已停止'}
                </div>
              </div>
              
              <div className="mining-stats-grid">
                <div className="stat-item">
                  <div className="stat-value">{miningStats.currentPower}</div>
                  <div className="stat-label">当前算力 (MH/s)</div>
                </div>
                
                <div className="stat-item">
                  <div className="stat-value">{miningStats.hashrate.toLocaleString()}</div>
                  <div className="stat-label">已挖哈希值</div>
                </div>
                
                <div className="stat-item">
                  <div className="stat-value">{miningStats.totalEarnings.toFixed(4)}</div>
                  <div className="stat-label">总收益 (USDT)</div>
                </div>
                
                <div className="stat-item">
                  <div className="stat-value">{formatTime(miningStats.miningTime)}</div>
                  <div className="stat-label">挖矿时间</div>
                </div>
              </div>
            </div>

            {/* 轮播图区域 */}
            <div className="swiper swiper-custom">
              <Swiper
                ref={swiperRef}
                spaceBetween={30}
                slidesPerView={1}
                speed={500}
                navigation={{
                  nextEl: ".custom-button-next",
                  prevEl: ".custom-button-prev",
                }}
                onSlideChange={handleSlideChange}
                modules={[Navigation]}
              >
                <SwiperSlide>
                  <h1 className="hero__title">CALCUMINER</h1>
                  <video 
                    autoPlay 
                    muted 
                    loop 
                    playsInline 
                    className="hero__image"
                  >
                    <source src="/images/one.mp4" type="video/mp4" />
                  </video>
                </SwiperSlide>
                
                <SwiperSlide>
                  <h1 className="hero__title">BLOCK BREACKER</h1>
                  <video 
                    autoPlay 
                    muted 
                    loop 
                    playsInline 
                    className="hero__image"
                  >
                    <source src="/images/two.MP4" type="video/mp4" />
                  </video>
                </SwiperSlide>
                
                <SwiperSlide>
                  <h1 className="hero__title">QUANTUM CORE</h1>
                  <video 
                    autoPlay 
                    muted 
                    loop 
                    playsInline 
                    className="hero__image"
                  >
                    <source src="/images/three.MP4" type="video/mp4" />
                  </video>
                </SwiperSlide>
              </Swiper>
              
              <div className="custom-button-next">
                <img 
                  className="hero__arrow-icon" 
                  src="/images/arrow-rigth.png" 
                  alt="Next" 
                />
              </div>
              <div className="custom-button-prev">
                <img 
                  className="hero__arrow-icon" 
                  src="/images/arrow-left.png" 
                  alt="Prev" 
                />
              </div>
            </div>

            {/* 控制按钮区域 */}
            <div className="control-buttons">
              {!miningStats.isMining ? (
                <button className="btn start-btn" onClick={handleStartMining}>
                  开始挖矿
                </button>
              ) : (
                <button className="btn stop-btn" onClick={handleStopMining}>
                  停止挖矿
                </button>
              )}
              
              <button 
                className="btn exchange-btn" 
                onClick={handleExchange}
                disabled={miningStats.hashrate === 0}
              >
                兑换哈希值
              </button>
            </div>

            {/* 快速操作区域 */}
            <div className="quick-actions">
              <a href="https://t.me/core_xbot" className="action-link">
                <button className="btn">START MINING</button>
              </a>
              
              <div className="action-cards">
                <div className="action-card">
                  <h3>算力提升</h3>
                  <p>提升算力可以增加挖矿效率</p>
                  <button className="btn-small">购买算力</button>
                </div>
                
                <div className="action-card">
                  <h3>收益提现</h3>
                  <p>将收益转换为USDT</p>
                  <button className="btn-small">立即提现</button>
                </div>
                
                <div className="action-card">
                  <h3>邀请奖励</h3>
                  <p>邀请好友获得额外奖励</p>
                  <button className="btn-small">邀请好友</button>
                </div>
              </div>
            </div>
            
            <p className="text">
              *Choose your graphic card and start mining in 1 click
            </p>
          </div>
        </section>
      </main>
    </>
  );
};

export default MiningPage; 