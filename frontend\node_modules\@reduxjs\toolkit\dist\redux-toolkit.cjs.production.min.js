var e,n=this&&this.__extends||(e=function(n,t){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,n){e.__proto__=n}||function(e,n){for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&(e[t]=n[t])},e(n,t)},function(n,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=n}e(n,t),n.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),t=this&&this.__generator||function(e,n){var t,r,i,o,u={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(t)throw new TypeError("Generator is already executing.");for(;u;)try{if(t=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return u.label++,{value:o[1],done:!1};case 5:u.label++,r=o[1],o=[0];continue;case 7:o=u.ops.pop(),u.trys.pop();continue;default:if(!((i=(i=u.trys).length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){u=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){u.label=o[1];break}if(6===o[0]&&u.label<i[1]){u.label=i[1],i=o;break}if(i&&u.label<i[2]){u.label=i[2],u.ops.push(o);break}i[2]&&u.ops.pop(),u.trys.pop();continue}o=n.call(e,u)}catch(e){o=[6,e],r=0}finally{t=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}},r=this&&this.__spreadArray||function(e,n){for(var t=0,r=n.length,i=e.length;t<r;t++,i++)e[i]=n[t];return e},i=Object.create,o=Object.defineProperty,u=Object.defineProperties,a=Object.getOwnPropertyDescriptor,c=Object.getOwnPropertyDescriptors,f=Object.getOwnPropertyNames,l=Object.getOwnPropertySymbols,s=Object.getPrototypeOf,d=Object.prototype.hasOwnProperty,p=Object.prototype.propertyIsEnumerable,v=function(e,n,t){return n in e?o(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t},y=function(e,n){for(var t in n||(n={}))d.call(n,t)&&v(e,t,n[t]);if(l)for(var r=0,i=l(n);r<i.length;r++)p.call(n,t=i[r])&&v(e,t,n[t]);return e},h=function(e,n){return u(e,c(n))},g=function(e){return o(e,"__esModule",{value:!0})},b=function(e,n,t){if(n&&"object"==typeof n||"function"==typeof n)for(var r=function(r){d.call(e,r)||"default"===r||o(e,r,{get:function(){return n[r]},enumerable:!(t=a(n,r))||t.enumerable})},i=0,u=f(n);i<u.length;i++)r(u[i]);return e},m=function(e){return b(g(o(null!=e?i(s(e)):{},"default",e&&e.__esModule&&"default"in e?{get:function(){return e.default},enumerable:!0}:{value:e,enumerable:!0})),e)},w=function(e,n,t){return new Promise((function(r,i){var o=function(e){try{a(t.next(e))}catch(e){i(e)}},u=function(e){try{a(t.throw(e))}catch(e){i(e)}},a=function(e){return e.done?r(e.value):Promise.resolve(e.value).then(o,u)};a((t=t.apply(e,n)).next())}))};g(exports),function(e,n){for(var t in n)o(e,t,{get:n[t],enumerable:!0})}(exports,{EnhancerArray:function(){return F},MiddlewareArray:function(){return B},SHOULD_AUTOBATCH:function(){return Je},TaskAbortError:function(){return ke},addListener:function(){return Fe},autoBatchEnhancer:function(){return $e},clearAllListeners:function(){return Ue},configureStore:function(){return Y},createAction:function(){return T},createActionCreatorInvariantMiddleware:function(){return N},createAsyncThunk:function(){return ve},createDraftSafeSelector:function(){return P},createEntityAdapter:function(){return ce},createImmutableStateInvariantMiddleware:function(){return X},createListenerMiddleware:function(){return Ge},createNextState:function(){return j.default},createReducer:function(){return ee},createSelector:function(){return A.createSelector},createSerializableStateInvariantMiddleware:function(){return K},createSlice:function(){return ne},current:function(){return j.current},findNonSerializableValue:function(){return H},freeze:function(){return j.freeze},getDefaultMiddleware:function(){return Q},getType:function(){return z},isAction:function(){return C},isActionCreator:function(){return D},isAllOf:function(){return be},isAnyOf:function(){return ge},isAsyncThunkAction:function(){return Se},isDraft:function(){return j.isDraft},isFluxStandardAction:function(){return L},isFulfilled:function(){return Ee},isImmutableDefault:function(){return W},isPending:function(){return Oe},isPlain:function(){return G},isPlainObject:function(){return M},isRejected:function(){return je},isRejectedWithValue:function(){return Ae},miniSerializeError:function(){return pe},nanoid:function(){return fe},original:function(){return j.original},prepareAutoBatched:function(){return Ke},removeListener:function(){return We},unwrapResult:function(){return ye}});var O=m(require("immer"));b(exports,m(require("redux")));var j=m(require("immer")),A=m(require("reselect")),E=m(require("immer")),S=m(require("reselect")),P=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var t=S.createSelector.apply(void 0,e),i=function(e){for(var n=[],i=1;i<arguments.length;i++)n[i-1]=arguments[i];return t.apply(void 0,r([(0,E.isDraft)(e)?(0,E.current)(e):e],n))};return i},_=m(require("redux")),q=m(require("redux")),x="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!==arguments.length)return"object"==typeof arguments[0]?q.compose:q.compose.apply(null,arguments)};function M(e){if("object"!=typeof e||null===e)return!1;var n=Object.getPrototypeOf(e);if(null===n)return!0;for(var t=n;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return n===t}"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window;var k=m(require("redux-thunk")),I=function(e){return e&&"function"==typeof e.match};function T(e,n){function t(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];if(n){var i=n.apply(void 0,t);if(!i)throw new Error("prepareAction did not return an object");return y(y({type:e,payload:i.payload},"meta"in i&&{meta:i.meta}),"error"in i&&{error:i.error})}return{type:e,payload:t[0]}}return t.toString=function(){return""+e},t.type=e,t.match=function(n){return n.type===e},t}function C(e){return M(e)&&"type"in e}function D(e){return"function"==typeof e&&"type"in e&&I(e)}function L(e){return C(e)&&"string"==typeof e.type&&Object.keys(e).every(R)}function R(e){return["type","payload","error","meta"].indexOf(e)>-1}function z(e){return""+e}function N(e){return void 0===e&&(e={}),function(){return function(e){return function(n){return e(n)}}}}var V=m(require("immer")),B=function(e){function t(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var i=e.apply(this,n)||this;return Object.setPrototypeOf(i,t.prototype),i}return n(t,e),Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];return e.prototype.concat.apply(this,n)},t.prototype.prepend=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return 1===e.length&&Array.isArray(e[0])?new(t.bind.apply(t,r([void 0],e[0].concat(this)))):new(t.bind.apply(t,r([void 0],e.concat(this))))},t}(Array),F=function(e){function t(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var i=e.apply(this,n)||this;return Object.setPrototypeOf(i,t.prototype),i}return n(t,e),Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];return e.prototype.concat.apply(this,n)},t.prototype.prepend=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return 1===e.length&&Array.isArray(e[0])?new(t.bind.apply(t,r([void 0],e[0].concat(this)))):new(t.bind.apply(t,r([void 0],e.concat(this))))},t}(Array);function U(e){return(0,V.isDraftable)(e)?(0,V.default)(e,(function(){})):e}function W(e){return"object"!=typeof e||null==e||Object.isFrozen(e)}function X(e){return void 0===e&&(e={}),function(){return function(e){return function(n){return e(n)}}}}function G(e){var n=typeof e;return null==e||"string"===n||"boolean"===n||"number"===n||Array.isArray(e)||M(e)}function H(e,n,t,r,i,o){var u;if(void 0===n&&(n=""),void 0===t&&(t=G),void 0===i&&(i=[]),!t(e))return{keyPath:n||"<root>",value:e};if("object"!=typeof e||null===e)return!1;if(null==o?void 0:o.has(e))return!1;for(var a=null!=r?r(e):Object.entries(e),c=i.length>0,f=function(e,a){var f=n?n+"."+e:e;return c&&i.some((function(e){return e instanceof RegExp?e.test(f):f===e}))?"continue":t(a)?"object"==typeof a&&(u=H(a,f,t,r,i,o))?{value:u}:void 0:{value:{keyPath:f,value:a}}},l=0,s=a;l<s.length;l++){var d=s[l],p=f(d[0],d[1]);if("object"==typeof p)return p.value}return o&&J(e)&&o.add(e),!1}function J(e){if(!Object.isFrozen(e))return!1;for(var n=0,t=Object.values(e);n<t.length;n++){var r=t[n];if("object"==typeof r&&null!==r&&!J(r))return!1}return!0}function K(e){return void 0===e&&(e={}),function(){return function(e){return function(n){return e(n)}}}}function Q(e){void 0===e&&(e={});var n=e.thunk,t=void 0===n||n,r=new B;return t&&r.push("boolean"==typeof t?k.default:k.default.withExtraArgument(t.extraArgument)),r}function Y(e){var n,t=function(e){return Q(e)},i=e||{},o=i.reducer,u=void 0===o?void 0:o,a=i.middleware,c=void 0===a?t():a,f=i.devTools,l=void 0===f||f,s=i.preloadedState,d=void 0===s?void 0:s,p=i.enhancers,v=void 0===p?void 0:p;if("function"==typeof u)n=u;else{if(!M(u))throw new Error('"reducer" is a required argument, and must be a function or an object of functions that can be passed to combineReducers');n=(0,_.combineReducers)(u)}var h=c;"function"==typeof h&&(h=h(t));var g=_.applyMiddleware.apply(void 0,h),b=_.compose;l&&(b=x(y({trace:!1},"object"==typeof l&&l)));var m=new F(g),w=m;Array.isArray(v)?w=r([g],v):"function"==typeof v&&(w=v(m));var O=b.apply(void 0,w);return(0,_.createStore)(n,d,O)}var Z=m(require("immer"));function $(e){var n,t={},r=[],i={addCase:function(e,n){var r="string"==typeof e?e:e.type;if(!r)throw new Error("`builder.addCase` cannot be called with an empty action type");if(r in t)throw new Error("`builder.addCase` cannot be called with two reducers for the same action type");return t[r]=n,i},addMatcher:function(e,n){return r.push({matcher:e,reducer:n}),i},addDefaultCase:function(e){return n=e,i}};return e(i),[t,r,n]}function ee(e,n,t,i){void 0===t&&(t=[]);var o,u="function"==typeof n?$(n):[n,t,i],a=u[0],c=u[1],f=u[2];if("function"==typeof e)o=function(){return U(e())};else{var l=U(e);o=function(){return l}}function s(e,n){void 0===e&&(e=o());var t=r([a[n.type]],c.filter((function(e){return(0,e.matcher)(n)})).map((function(e){return e.reducer})));return 0===t.filter((function(e){return!!e})).length&&(t=[f]),t.reduce((function(e,t){if(t){var r;if((0,Z.isDraft)(e))return void 0===(r=t(e,n))?e:r;if((0,Z.isDraftable)(e))return(0,Z.default)(e,(function(e){return t(e,n)}));if(void 0===(r=t(e,n))){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return r}return e}),e)}return s.getInitialState=o,s}function ne(e){var n=e.name;if(!n)throw new Error("`name` is a required option for createSlice");var t,r="function"==typeof e.initialState?e.initialState:U(e.initialState),i=e.reducers||{},o=Object.keys(i),u={},a={},c={};function f(){var n="function"==typeof e.extraReducers?$(e.extraReducers):[e.extraReducers],t=n[0],i=n[1],o=void 0===i?[]:i,u=n[2],c=void 0===u?void 0:u,f=y(y({},void 0===t?{}:t),a);return ee(r,(function(e){for(var n in f)e.addCase(n,f[n]);for(var t=0,r=o;t<r.length;t++){var i=r[t];e.addMatcher(i.matcher,i.reducer)}c&&e.addDefaultCase(c)}))}return o.forEach((function(e){var t,r,o=i[e],f=n+"/"+e;"reducer"in o?(t=o.reducer,r=o.prepare):t=o,u[e]=t,a[f]=t,c[e]=r?T(f,r):T(f)})),{name:n,reducer:function(e,n){return t||(t=f()),t(e,n)},actions:c,caseReducers:u,getInitialState:function(){return t||(t=f()),t.getInitialState()}}}var te=m(require("immer"));function re(e){return function(n,t){var r=function(n){L(t)?e(t.payload,n):e(t,n)};return(0,te.isDraft)(n)?(r(n),n):(0,te.default)(n,r)}}function ie(e,n){return n(e)}function oe(e){return Array.isArray(e)||(e=Object.values(e)),e}function ue(e,n,t){for(var r=[],i=[],o=0,u=e=oe(e);o<u.length;o++){var a=u[o],c=ie(a,n);c in t.entities?i.push({id:c,changes:a}):r.push(a)}return[r,i]}function ae(e){function n(n,t){var r=ie(n,e);r in t.entities||(t.ids.push(r),t.entities[r]=n)}function t(e,t){for(var r=0,i=e=oe(e);r<i.length;r++)n(i[r],t)}function r(n,t){var r=ie(n,e);r in t.entities||t.ids.push(r),t.entities[r]=n}function i(e,n){var t=!1;e.forEach((function(e){e in n.entities&&(delete n.entities[e],t=!0)})),t&&(n.ids=n.ids.filter((function(e){return e in n.entities})))}function o(n,t){var r={},i={};if(n.forEach((function(e){e.id in t.entities&&(i[e.id]={id:e.id,changes:y(y({},i[e.id]?i[e.id].changes:null),e.changes)})})),(n=Object.values(i)).length>0){var o=n.filter((function(n){return function(n,t,r){var i=Object.assign({},r.entities[t.id],t.changes),o=ie(i,e),u=o!==t.id;return u&&(n[t.id]=o,delete r.entities[t.id]),r.entities[o]=i,u}(r,n,t)})).length>0;o&&(t.ids=Object.keys(t.entities))}}function u(n,r){var i=ue(n,e,r),u=i[0];o(i[1],r),t(u,r)}return{removeAll:(a=function(e){Object.assign(e,{ids:[],entities:{}})},c=re((function(e,n){return a(n)})),function(e){return c(e,void 0)}),addOne:re(n),addMany:re(t),setOne:re(r),setMany:re((function(e,n){for(var t=0,i=e=oe(e);t<i.length;t++)r(i[t],n)})),setAll:re((function(e,n){e=oe(e),n.ids=[],n.entities={},t(e,n)})),updateOne:re((function(e,n){return o([e],n)})),updateMany:re(o),upsertOne:re((function(e,n){return u([e],n)})),upsertMany:re(u),removeOne:re((function(e,n){return i([e],n)})),removeMany:re(i)};var a,c}function ce(e){void 0===e&&(e={});var n=y({sortComparer:!1,selectId:function(e){return e.id}},e),t=n.selectId,r=n.sortComparer,i={getInitialState:function(e){return void 0===e&&(e={}),Object.assign({ids:[],entities:{}},e)}},o={getSelectors:function(e){var n=function(e){return e.ids},t=function(e){return e.entities},r=P(n,t,(function(e,n){return e.map((function(e){return n[e]}))})),i=function(e,n){return n},o=function(e,n){return e[n]},u=P(n,(function(e){return e.length}));if(!e)return{selectIds:n,selectEntities:t,selectAll:r,selectTotal:u,selectById:P(t,i,o)};var a=P(e,t);return{selectIds:P(e,n),selectEntities:a,selectAll:P(e,r),selectTotal:P(e,u),selectById:P(a,i,o)}}},u=r?function(e,n){var t=ae(e);function r(n,t){var r=(n=oe(n)).filter((function(n){return!(ie(n,e)in t.entities)}));0!==r.length&&a(r,t)}function i(e,n){0!==(e=oe(e)).length&&a(e,n)}function o(n,t){for(var r=!1,i=0,o=n;i<o.length;i++){var u=o[i],a=t.entities[u.id];if(a){r=!0,Object.assign(a,u.changes);var f=e(a);u.id!==f&&(delete t.entities[u.id],t.entities[f]=a)}}r&&c(t)}function u(n,t){var i=ue(n,e,t),u=i[0];o(i[1],t),r(u,t)}function a(n,t){n.forEach((function(n){t.entities[e(n)]=n})),c(t)}function c(t){var r=Object.values(t.entities);r.sort(n);var i=r.map(e);(function(e,n){if(e.length!==n.length)return!1;for(var t=0;t<e.length&&t<n.length;t++)if(e[t]!==n[t])return!1;return!0})(t.ids,i)||(t.ids=i)}return{removeOne:t.removeOne,removeMany:t.removeMany,removeAll:t.removeAll,addOne:re((function(e,n){return r([e],n)})),updateOne:re((function(e,n){return o([e],n)})),upsertOne:re((function(e,n){return u([e],n)})),setOne:re((function(e,n){return i([e],n)})),setMany:re(i),setAll:re((function(e,n){e=oe(e),n.entities={},n.ids=[],r(e,n)})),addMany:re(r),updateMany:re(o),upsertMany:re(u)}}(t,r):ae(t);return y(y(y({selectId:t,sortComparer:r},i),o),u)}var fe=function(e){void 0===e&&(e=21);for(var n="",t=e;t--;)n+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return n},le=["name","message","stack","code"],se=function(e,n){this.payload=e,this.meta=n},de=function(e,n){this.payload=e,this.meta=n},pe=function(e){if("object"==typeof e&&null!==e){for(var n={},t=0,r=le;t<r.length;t++){var i=r[t];"string"==typeof e[i]&&(n[i]=e[i])}return n}return{message:String(e)}},ve=function(){function e(e,n,r){var i=T(e+"/fulfilled",(function(e,n,t,r){return{payload:e,meta:h(y({},r||{}),{arg:t,requestId:n,requestStatus:"fulfilled"})}})),o=T(e+"/pending",(function(e,n,t){return{payload:void 0,meta:h(y({},t||{}),{arg:n,requestId:e,requestStatus:"pending"})}})),u=T(e+"/rejected",(function(e,n,t,i,o){return{payload:i,error:(r&&r.serializeError||pe)(e||"Rejected"),meta:h(y({},o||{}),{arg:t,requestId:n,rejectedWithValue:!!i,requestStatus:"rejected",aborted:"AbortError"===(null==e?void 0:e.name),condition:"ConditionError"===(null==e?void 0:e.name)})}})),a="undefined"!=typeof AbortController?AbortController:function(){function e(){this.signal={aborted:!1,addEventListener:function(){},dispatchEvent:function(){return!1},onabort:function(){},removeEventListener:function(){},reason:void 0,throwIfAborted:function(){}}}return e.prototype.abort=function(){},e}();return Object.assign((function(e){return function(c,f,l){var s,d=(null==r?void 0:r.idGenerator)?r.idGenerator(e):fe(),p=new a;function v(e){s=e,p.abort()}var y=function(){return w(this,null,(function(){var a,y,h,g,b,m;return t(this,(function(t){switch(t.label){case 0:return t.trys.push([0,4,,5]),null===(w=g=null==(a=null==r?void 0:r.condition)?void 0:a.call(r,e,{getState:f,extra:l}))||"object"!=typeof w||"function"!=typeof w.then?[3,2]:[4,g];case 1:g=t.sent(),t.label=2;case 2:if(!1===g||p.signal.aborted)throw{name:"ConditionError",message:"Aborted due to condition callback returning false."};return b=new Promise((function(e,n){return p.signal.addEventListener("abort",(function(){return n({name:"AbortError",message:s||"Aborted"})}))})),c(o(d,e,null==(y=null==r?void 0:r.getPendingMeta)?void 0:y.call(r,{requestId:d,arg:e},{getState:f,extra:l}))),[4,Promise.race([b,Promise.resolve(n(e,{dispatch:c,getState:f,extra:l,requestId:d,signal:p.signal,abort:v,rejectWithValue:function(e,n){return new se(e,n)},fulfillWithValue:function(e,n){return new de(e,n)}})).then((function(n){if(n instanceof se)throw n;return n instanceof de?i(n.payload,d,e,n.meta):i(n,d,e)}))])];case 3:return h=t.sent(),[3,5];case 4:return m=t.sent(),h=m instanceof se?u(null,d,e,m.payload,m.meta):u(m,d,e),[3,5];case 5:return r&&!r.dispatchConditionRejection&&u.match(h)&&h.meta.condition||c(h),[2,h]}var w}))}))}();return Object.assign(y,{abort:v,requestId:d,arg:e,unwrap:function(){return y.then(ye)}})}}),{pending:o,rejected:u,fulfilled:i,typePrefix:e})}return e.withTypes=function(){return e},e}();function ye(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}var he=function(e,n){return I(e)?e.match(n):e(n)};function ge(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return function(n){return e.some((function(e){return he(e,n)}))}}function be(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return function(n){return e.every((function(e){return he(e,n)}))}}function me(e,n){if(!e||!e.meta)return!1;var t="string"==typeof e.meta.requestId,r=n.indexOf(e.meta.requestStatus)>-1;return t&&r}function we(e){return"function"==typeof e[0]&&"pending"in e[0]&&"fulfilled"in e[0]&&"rejected"in e[0]}function Oe(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return 0===e.length?function(e){return me(e,["pending"])}:we(e)?function(n){var t=e.map((function(e){return e.pending}));return ge.apply(void 0,t)(n)}:Oe()(e[0])}function je(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return 0===e.length?function(e){return me(e,["rejected"])}:we(e)?function(n){var t=e.map((function(e){return e.rejected}));return ge.apply(void 0,t)(n)}:je()(e[0])}function Ae(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var t=function(e){return e&&e.meta&&e.meta.rejectedWithValue};return 0===e.length||we(e)?function(n){return be(je.apply(void 0,e),t)(n)}:Ae()(e[0])}function Ee(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return 0===e.length?function(e){return me(e,["fulfilled"])}:we(e)?function(n){var t=e.map((function(e){return e.fulfilled}));return ge.apply(void 0,t)(n)}:Ee()(e[0])}function Se(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return 0===e.length?function(e){return me(e,["pending","fulfilled","rejected"])}:we(e)?function(n){for(var t=[],r=0,i=e;r<i.length;r++){var o=i[r];t.push(o.pending,o.rejected,o.fulfilled)}return ge.apply(void 0,t)(n)}:Se()(e[0])}var Pe=function(e,n){if("function"!=typeof e)throw new TypeError(n+" is not a function")},_e=function(){},qe=function(e,n){return void 0===n&&(n=_e),e.catch(n),e},xe=function(e,n){return e.addEventListener("abort",n,{once:!0}),function(){return e.removeEventListener("abort",n)}},Me=function(e,n){var t=e.signal;t.aborted||("reason"in t||Object.defineProperty(t,"reason",{enumerable:!0,value:n,configurable:!0,writable:!0}),e.abort(n))},ke=function(e){this.code=e,this.name="TaskAbortError",this.message="task cancelled (reason: "+e+")"},Ie=function(e){if(e.aborted)throw new ke(e.reason)};function Te(e,n){var t=_e;return new Promise((function(r,i){var o=function(){return i(new ke(e.reason))};e.aborted?o():(t=xe(e,o),n.finally((function(){return t()})).then(r,i))})).finally((function(){t=_e}))}var Ce=function(e){return function(n){return qe(Te(e,n).then((function(n){return Ie(e),n})))}},De=function(e){var n=Ce(e);return function(e){return n(new Promise((function(n){return setTimeout(n,e)})))}},Le=Object.assign,Re={},ze="listenerMiddleware",Ne=function(e){var n=e.type,t=e.actionCreator,r=e.matcher,i=e.predicate,o=e.effect;if(n)i=T(n).match;else if(t)n=t.type,i=t.match;else if(r)i=r;else if(!i)throw new Error("Creating or removing a listener requires one of the known fields for matching an action");return Pe(o,"options.listener"),{predicate:i,type:n,effect:o}},Ve=function(e){e.pending.forEach((function(e){Me(e,"listener-cancelled")}))},Be=function(e,n,t){try{e(n,t)}catch(e){setTimeout((function(){throw e}),0)}},Fe=T(ze+"/add"),Ue=T(ze+"/removeAll"),We=T(ze+"/remove"),Xe=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];console.error.apply(console,r([ze+"/error"],e))};function Ge(e){var n=this;void 0===e&&(e={});var r=new Map,i=e.extra,o=e.onError,u=void 0===o?Xe:o;Pe(u,"onError");var a=function(e){for(var n=0,t=Array.from(r.values());n<t.length;n++){var i=t[n];if(e(i))return i}},c=function(e){var n=a((function(n){return n.effect===e.effect}));return n||(n=function(e){var n=Ne(e),t=n.type,r=n.predicate,i=n.effect;return{id:fe(),effect:i,type:t,predicate:r,pending:new Set,unsubscribe:function(){throw new Error("Unsubscribe not initialized")}}}(e)),function(e){return e.unsubscribe=function(){return r.delete(e.id)},r.set(e.id,e),function(n){e.unsubscribe(),(null==n?void 0:n.cancelActive)&&Ve(e)}}(n)},f=function(e){var n=Ne(e),t=n.type,r=n.effect,i=n.predicate,o=a((function(e){return("string"==typeof t?e.type===t:e.predicate===i)&&e.effect===r}));return o&&(o.unsubscribe(),e.cancelActive&&Ve(o)),!!o},l=function(e,o,a,f){return w(n,null,(function(){var n,l,s,d;return t(this,(function(p){switch(p.label){case 0:n=new AbortController,l=function(e,n){return function(r,i){return qe(function(r,i){return w(void 0,null,(function(){var o,u,a,c;return t(this,(function(t){switch(t.label){case 0:Ie(n),o=function(){},u=new Promise((function(n,t){var i=e({predicate:r,effect:function(e,t){t.unsubscribe(),n([e,t.getState(),t.getOriginalState()])}});o=function(){i(),t()}})),a=[u],null!=i&&a.push(new Promise((function(e){return setTimeout(e,i,null)}))),t.label=1;case 1:return t.trys.push([1,,3,4]),[4,Te(n,Promise.race(a))];case 2:return c=t.sent(),Ie(n),[2,c];case 3:return o(),[7];case 4:return[2]}}))}))}(r,i))}}(c,n.signal),s=[],p.label=1;case 1:return p.trys.push([1,3,4,6]),e.pending.add(n),[4,Promise.resolve(e.effect(o,Le({},a,{getOriginalState:f,condition:function(e,n){return l(e,n).then(Boolean)},take:l,delay:De(n.signal),pause:Ce(n.signal),extra:i,signal:n.signal,fork:(v=n.signal,y=s,function(e,n){Pe(e,"taskExecutor");var r,i=new AbortController;r=i,xe(v,(function(){return Me(r,v.reason)}));var o,u,a=(o=function(){return w(void 0,null,(function(){var n;return t(this,(function(t){switch(t.label){case 0:return Ie(v),Ie(i.signal),[4,e({pause:Ce(i.signal),delay:De(i.signal),signal:i.signal})];case 1:return n=t.sent(),Ie(i.signal),[2,n]}}))}))},u=function(){return Me(i,"task-completed")},w(void 0,null,(function(){var e;return t(this,(function(n){switch(n.label){case 0:return n.trys.push([0,3,4,5]),[4,Promise.resolve()];case 1:return n.sent(),[4,o()];case 2:return[2,{status:"ok",value:n.sent()}];case 3:return[2,{status:(e=n.sent())instanceof ke?"cancelled":"rejected",error:e}];case 4:return null==u||u(),[7];case 5:return[2]}}))})));return(null==n?void 0:n.autoJoin)&&y.push(a),{result:Ce(v)(a),cancel:function(){Me(i,"task-cancelled")}}}),unsubscribe:e.unsubscribe,subscribe:function(){r.set(e.id,e)},cancelActiveListeners:function(){e.pending.forEach((function(e,t,r){e!==n&&(Me(e,"listener-cancelled"),r.delete(e))}))}})))];case 2:return p.sent(),[3,6];case 3:return(d=p.sent())instanceof ke||Be(u,d,{raisedBy:"effect"}),[3,6];case 4:return[4,Promise.allSettled(s)];case 5:return p.sent(),Me(n,"listener-completed"),e.pending.delete(n),[7];case 6:return[2]}var v,y}))}))},s=function(e){return function(){e.forEach(Ve),e.clear()}}(r);return{middleware:function(e){return function(n){return function(t){if(!C(t))return n(t);if(Fe.match(t))return c(t.payload);if(!Ue.match(t)){if(We.match(t))return f(t.payload);var i,o=e.getState(),a=function(){if(o===Re)throw new Error(ze+": getOriginalState can only be called synchronously");return o};try{if(i=n(t),r.size>0)for(var d=e.getState(),p=Array.from(r.values()),v=0,y=p;v<y.length;v++){var h=y[v],g=!1;try{g=h.predicate(t,d,o)}catch(e){g=!1,Be(u,e,{raisedBy:"predicate"})}g&&l(h,t,e,a)}}finally{o=Re}return i}s()}}},startListening:c,stopListening:f,clearListeners:s}}var He,Je="RTK_autoBatch",Ke=function(){return function(e){var n;return{payload:e,meta:(n={},n[Je]=!0,n)}}},Qe="function"==typeof queueMicrotask?queueMicrotask.bind("undefined"!=typeof window?window:"undefined"!=typeof global?global:globalThis):function(e){return(He||(He=Promise.resolve())).then(e).catch((function(e){return setTimeout((function(){throw e}),0)}))},Ye=function(e){return function(n){setTimeout(n,e)}},Ze="undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:Ye(10),$e=function(e){return void 0===e&&(e={type:"raf"}),function(n){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var i=n.apply(void 0,t),o=!0,u=!1,a=!1,c=new Set,f="tick"===e.type?Qe:"raf"===e.type?Ze:"callback"===e.type?e.queueNotification:Ye(e.timeout),l=function(){a=!1,u&&(u=!1,c.forEach((function(e){return e()})))};return Object.assign({},i,{subscribe:function(e){var n=i.subscribe((function(){return o&&e()}));return c.add(e),function(){n(),c.delete(e)}},dispatch:function(e){var n;try{return o=!(null==(n=null==e?void 0:e.meta)?void 0:n[Je]),(u=!o)&&(a||(a=!0,f(l))),i.dispatch(e)}finally{o=!0}}})}}};(0,O.enableES5)();
//# sourceMappingURL=redux-toolkit.cjs.production.min.js.map