import React, { useState } from 'react';
import { Card, Button, Toast } from 'antd-mobile';
import { 
  GiftOutline, 
  CheckOutline,
  StarOutline 
} from 'antd-mobile-icons';
import './TasksPage.scss';

// 任务类型
interface Task {
  id: number;
  title: string;
  description: string;
  reward: number;
  rewardType: 'GPU' | 'USDT';
  status: 'available' | 'in_progress' | 'completed' | 'claimed';
  progress?: number;
  target?: number;
  timeRemaining?: number;
  category: 'daily' | 'weekly' | 'achievement';
  difficulty: 'easy' | 'medium' | 'hard';
}

const TasksPage: React.FC = () => {
  const [tasks, setTasks] = useState<Task[]>([
    {
      id: 1,
      title: 'Complete Profile',
      description: 'Complete your profile information',
      reward: 50,
      rewardType: 'GPU',
      status: 'completed',
      category: 'daily',
      difficulty: 'easy',
    },
    {
      id: 2,
      title: 'First Mining Session',
      description: 'Start your first mining session',
      reward: 100,
      rewardType: 'GPU',
      status: 'completed',
      category: 'daily',
      difficulty: 'easy',
    },
    {
      id: 3,
      title: 'Invite 3 Friends',
      description: 'Invite 3 friends to join CoreX MINER',
      reward: 450,
      rewardType: 'GPU',
      status: 'in_progress',
      progress: 1,
      target: 3,
      category: 'weekly',
      difficulty: 'medium',
    },
    {
      id: 4,
      title: 'Mine for 24 Hours',
      description: 'Keep mining for 24 consecutive hours',
      reward: 200,
      rewardType: 'GPU',
      status: 'in_progress',
      progress: 12,
      target: 24,
      timeRemaining: 43200, // 12 hours in seconds
      category: 'weekly',
      difficulty: 'hard',
    },
    {
      id: 5,
      title: 'Reach 5000 GPU Power',
      description: 'Accumulate 5000 GPU mining power',
      reward: 500,
      rewardType: 'GPU',
      status: 'available',
      category: 'achievement',
      difficulty: 'hard',
    },
    {
      id: 6,
      title: 'Daily Login',
      description: 'Login to the app today',
      reward: 10,
      rewardType: 'GPU',
      status: 'available',
      category: 'daily',
      difficulty: 'easy',
    },
  ]);

  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const categories = [
    { value: 'all', label: 'All Tasks' },
    { value: 'daily', label: 'Daily' },
    { value: 'weekly', label: 'Weekly' },
    { value: 'achievement', label: 'Achievements' },
  ];

  const filteredTasks = selectedCategory === 'all' 
    ? tasks 
    : tasks.filter(task => task.category === selectedCategory);

  const handleClaimReward = async (taskId: number) => {
    try {
      // TODO: 调用领取奖励API
      await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟API调用
      
      setTasks(prev => prev.map(task => 
        task.id === taskId 
          ? { ...task, status: 'claimed' as const }
          : task
      ));
      
      Toast.show('奖励领取成功！');
    } catch (error) {
      Toast.show('领取失败，请重试');
    }
  };

  const handleStartTask = (taskId: number) => {
    setTasks(prev => prev.map(task => 
      task.id === taskId 
        ? { ...task, status: 'in_progress' as const }
        : task
    ));
    Toast.show('任务已开始');
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return '#52c41a';
      case 'medium': return '#faad14';
      case 'hard': return '#ff4d4f';
      default: return '#666';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return '#52c41a';
      case 'in_progress': return '#1890ff';
      case 'claimed': return '#666';
      default: return '#faad14';
    }
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  return (
    <div className="tasks-page">
      {/* 任务统计 */}
      <Card className="stats-card">
        <div className="stats__header">
          <GiftOutline className="stats-icon" />
          <h2>Task Progress</h2>
        </div>
        <div className="stats__content">
          <div className="stat-item">
            <span className="stat-number">
              {tasks.filter(t => t.status === 'completed' || t.status === 'claimed').length}
            </span>
            <span className="stat-label">Completed</span>
          </div>
          <div className="stat-item">
            <span className="stat-number">
              {tasks.filter(t => t.status === 'in_progress').length}
            </span>
            <span className="stat-label">In Progress</span>
          </div>
          <div className="stat-item">
            <span className="stat-number">
              {tasks.filter(t => t.status === 'available').length}
            </span>
            <span className="stat-label">Available</span>
          </div>
        </div>
      </Card>

      {/* 分类筛选 */}
      <Card className="filter-card">
        <div className="filter__buttons">
          {categories.map(category => (
            <Button
              key={category.value}
              className={`filter-button ${selectedCategory === category.value ? 'active' : ''}`}
              size="small"
              onClick={() => setSelectedCategory(category.value)}
            >
              {category.label}
            </Button>
          ))}
        </div>
      </Card>

      {/* 任务列表 */}
      <div className="tasks-list">
        {filteredTasks.map((task) => (
          <Card key={task.id} className="task-card">
            <div className="task__header">
              <div className="task__title">
                <h3>{task.title}</h3>
                <span 
                  className="difficulty-badge"
                  style={{ backgroundColor: getDifficultyColor(task.difficulty) }}
                >
                  {task.difficulty.toUpperCase()}
                </span>
              </div>
              <div className="task__reward">
                <GiftOutline />
                <span>{task.reward} {task.rewardType}</span>
              </div>
            </div>

            <p className="task__description">{task.description}</p>

            {task.progress !== undefined && task.target && (
              <div className="task__progress">
                <div className="progress-bar">
                  <div 
                    className="progress-fill" 
                    style={{ width: `${(task.progress / task.target) * 100}%` }}
                  ></div>
                  <span className="progress-text">{task.progress}/{task.target}</span>
                </div>
                {task.timeRemaining && (
                  <div className="time-remaining">
                    <div className="clock-icon">⏰</div>
                    <span>{formatTime(task.timeRemaining)}</span>
                  </div>
                )}
              </div>
            )}

            <div className="task__status">
              <span 
                className="status-badge"
                style={{ backgroundColor: getStatusColor(task.status) }}
              >
                {task.status.replace('_', ' ').toUpperCase()}
              </span>

              {task.status === 'completed' && (
                <Button
                  size="small"
                  color="primary"
                  onClick={() => handleClaimReward(task.id)}
                >
                  <CheckOutline />
                  Claim Reward
                </Button>
              )}

              {task.status === 'available' && (
                <Button
                  size="small"
                  color="primary"
                  onClick={() => handleStartTask(task.id)}
                >
                  <StarOutline />
                  Start Task
                </Button>
              )}
            </div>
          </Card>
        ))}
      </div>

      {/* 空状态 */}
      {filteredTasks.length === 0 && (
        <Card className="empty-card">
          <div className="empty__content">
            <GiftOutline className="empty-icon" />
            <h3>No tasks available</h3>
            <p>Check back later for new tasks</p>
          </div>
        </Card>
      )}
    </div>
  );
};

export default TasksPage; 