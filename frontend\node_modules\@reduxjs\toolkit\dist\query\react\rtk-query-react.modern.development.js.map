{"version": 3, "sources": ["../../../src/query/react/index.ts", "../../../src/query/react/buildHooks.ts", "../../../src/query/react/useSerializedStableValue.ts", "../../../src/query/react/constants.ts", "../../../src/query/react/useShallowStableValue.ts", "../../../src/query/defaultSerializeQueryArgs.ts", "../../../src/query/endpointDefinitions.ts", "../../../src/query/utils/capitalize.ts", "../../../src/query/tsHelpers.ts", "../../../src/query/react/module.ts", "../../../src/query/react/ApiProvider.tsx", "rtk-query-react.modern.development.js"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,OAAA,EAAA,UAAA,EAAA,cAAA,EAAA,MAAA,wBAAA,CAAA;;ACCA,OAAA,EAAA,cAAA,EAAA,MAAA,kBAAA,CAAA;AAGA,OAAA,EAAA,WAAA,EAAA,aAAA,EAAA,SAAA,IAAA,UAAA,EAAA,eAAA,EAAA,OAAA,IAAA,QAAA,EAAA,MAAA,IAAA,OAAA,EAAA,QAAA,EAAA,MAAA,OAAA,CAAA;AASA,OAAA,EAAA,WAAA,EAAA,SAAA,EAAA,MAAA,wBAAA,CAAA;AAwBA,OAAA,EAAA,YAAA,IAAA,aAAA,EAAA,MAAA,aAAA,CAAA;;ACrCA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,OAAA,EAAA,MAAA,OAAA,CAAA;AAIO,SAAA,kBAAA,CACL,SAAA,EACA,SAAA,EACA,kBAAA,EACA,YAAA;IAEA,MAAM,QAAA,GAAW,OAAA,CACf,GAAA,EAAA,CAAO,CAAA;QACL,SAAA;QACA,UAAA,EACE,OAAO,SAAA,IAAa,QAAA,CAAA,CAAA,CAChB,SAAA,CAAU,EAAE,SAAA,EAAW,kBAAA,EAAoB,YAAA,EAAA,CAAA,CAAA,CAAA,CAC3C,SAAA;KAAA,CAAA,EAER,CAAC,SAAA,EAAW,SAAA,EAAW,kBAAA,EAAoB,YAAA,CAAA,CAAA,CAAA;IAE7C,MAAM,MAAA,GAAQ,MAAA,CAAO,QAAA,CAAA,CAAA;IACrB,SAAA,CAAU,GAAA,EAAA;QACR,IAAI,MAAA,CAAM,OAAA,CAAQ,UAAA,KAAe,QAAA,CAAS,UAAA,EAAY;YACpD,MAAA,CAAM,OAAA,GAAU,QAAA,CAAA;SAAA;IAAA,CAAA,EAEjB,CAAC,QAAA,CAAA,CAAA,CAAA;IAEJ,OAAO,MAAA,CAAM,OAAA,CAAQ,UAAA,KAAe,QAAA,CAAS,UAAA,CAAA,CAAA,CACzC,MAAA,CAAM,OAAA,CAAQ,SAAA,CAAA,CAAA,CACd,SAAA,CAAA;AAAA,CAAA;;AC7BC,IAAM,mBAAA,GAAsB,MAAA,EAAA,CAAA;;ACAnC,OAAA,EAAA,SAAA,IAAA,UAAA,EAAA,MAAA,IAAA,OAAA,EAAA,MAAA,OAAA,CAAA;AACA,OAAA,EAAA,YAAA,EAAA,MAAA,aAAA,CAAA;AAEO,SAAA,qBAAA,CAAkC,KAAA;IACvC,MAAM,MAAA,GAAQ,OAAA,CAAO,KAAA,CAAA,CAAA;IACrB,UAAA,CAAU,GAAA,EAAA;QACR,IAAI,CAAC,YAAA,CAAa,MAAA,CAAM,OAAA,EAAS,KAAA,CAAA,EAAQ;YACvC,MAAA,CAAM,OAAA,GAAU,KAAA,CAAA;SAAA;IAAA,CAAA,EAEjB,CAAC,KAAA,CAAA,CAAA,CAAA;IAEJ,OAAO,YAAA,CAAa,MAAA,CAAM,OAAA,EAAS,KAAA,CAAA,CAAA,CAAA,CAAS,MAAA,CAAM,OAAA,CAAA,CAAA,CAAU,KAAA,CAAA;AAAA,CAAA;;ACT9D,OAAA,EAAA,aAAA,EAAA,MAAA,kBAAA,CAAA;AAEA,IAAM,KAAA,GAA0C,OAAA,CAAA,CAAA,CAC5C,IAAI,OAAA,EAAA,CAAA,CAAA,CACJ,KAAA,CAAA,CAAA;AAEG,IAAM,yBAAA,GAAqD,CAAC,EACjE,YAAA,EACA,SAAA,EAAA,EAAA,EAAA;IAEA,IAAI,UAAA,GAAa,EAAA,CAAA;IAEjB,MAAM,MAAA,GAAS,KAAA,IAAA,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAO,GAAA,CAAI,SAAA,CAAA,CAAA;IAE1B,IAAI,OAAO,MAAA,KAAW,QAAA,EAAU;QAC9B,UAAA,GAAa,MAAA,CAAA;KAAA;SACR;QACL,MAAM,WAAA,GAAc,IAAA,CAAK,SAAA,CAAU,SAAA,EAAW,CAAC,GAAA,EAAK,KAAA,EAAA,EAAA,CAClD,aAAA,CAAc,KAAA,CAAA,CAAA,CAAA,CACV,MAAA,CAAO,IAAA,CAAK,KAAA,CAAA,CACT,IAAA,EAAA,CACA,MAAA,CAAY,CAAC,GAAA,EAAK,IAAA,EAAA,EAAA;YACjB,GAAA,CAAI,IAAA,CAAA,GAAQ,KAAA,CAAc,IAAA,CAAA,CAAA;YAC1B,OAAO,GAAA,CAAA;QAAA,CAAA,EACN,EAAA,CAAA,CAAA,CAAA,CACL,KAAA,CAAA,CAAA;QAEN,IAAI,aAAA,CAAc,SAAA,CAAA,EAAY;YAC5B,KAAA,IAAA,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAO,GAAA,CAAI,SAAA,EAAW,WAAA,CAAA,CAAA;SAAA;QAExB,UAAA,GAAa,WAAA,CAAA;KAAA;IAGf,OAAO,GAAG,YAAA,IAAgB,UAAA,GAAA,CAAA;AAAA,CAAA,CAAA;;AJwBrB,IAAM,yBAAA,GACX,OAAO,MAAA,KAAW,WAAA,IAClB,CAAC,CAAC,MAAA,CAAO,QAAA,IACT,CAAC,CAAC,MAAA,CAAO,QAAA,CAAS,aAAA,CAAA,CAAA,CACd,eAAA,CAAA,CAAA,CACA,UAAA,CAAA;AA8dN,IAAM,4BAAA,GAAgE,CAAC,CAAA,EAAA,EAAA,CAAM,CAAA,CAAA;AAQ7E,IAAM,2BAAA,GAA4D,CAChE,QAAA,EAAA,EAAA;IAEA,IAAI,QAAA,CAAS,eAAA,EAAiB;QAC5B,OAAO,aAAA,CAAA,cAAA,CAAA,EAAA,EACF,QAAA,CAAA,EADE;YAEL,eAAA,EAAiB,KAAA;YACjB,UAAA,EAAY,IAAA;YACZ,SAAA,EAAW,QAAA,CAAS,IAAA,KAAS,KAAA,CAAA,CAAA,CAAA,CAAY,KAAA,CAAA,CAAA,CAAQ,IAAA;YACjD,MAAA,EAAQ,WAAA,CAAY,OAAA;SAAA,CAAA,CAAA;KAAA;IAGxB,OAAO,QAAA,CAAA;AAAA,CAAA,CAAA;AAiBF,SAAA,UAAA,CAA6D,EAClE,GAAA,EACA,aAAA,EAAe,EACb,KAAA,EACA,WAAA,EACA,WAAA,EACA,QAAA,EACA,6BAAA,EAAA,EAEF,kBAAA,EACA,OAAA,EAAA;IAOA,MAAM,0BAAA,GAGM,6BAAA,CAAA,CAAA,CAAgC,CAAC,EAAA,EAAA,EAAA,CAAO,EAAA,EAAA,CAAA,CAAA,CAAO,UAAA,CAAA;IAE3D,OAAO,EAAE,eAAA,EAAiB,iBAAA,EAAmB,WAAA,EAAA,CAAA;IAE7C,SAAA,qBAAA,CACE,YAAA,EACA,UAAA,EACA,SAAA;QAKA,IAAI,CAAA,UAAA,IAAA,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAY,YAAA,CAAA,IAAgB,YAAA,CAAa,eAAA,EAAiB;YAC5D,MAAM,EAAE,YAAA,EAAA,GAAiB,UAAA,CAAA;YACzB,MAAM,kBAAA,GAAqB,OAAA,CAAQ,mBAAA,CAAoB,YAAA,CAAA,CAAA;YACvD,IACE,kBAAA,CAAmB;gBACjB,SAAA,EAAW,UAAA,CAAW,YAAA;gBACtB,kBAAA;gBACA,YAAA;aAAA,CAAA,KAEF,kBAAA,CAAmB;gBACjB,SAAA;gBACA,kBAAA;gBACA,YAAA;aAAA,CAAA;gBAGF,UAAA,GAAa,KAAA,CAAA,CAAA;SAAA;QAIjB,IAAI,IAAA,GAAO,YAAA,CAAa,SAAA,CAAA,CAAA,CAAY,YAAA,CAAa,IAAA,CAAA,CAAA,CAAO,UAAA,IAAA,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAY,IAAA,CAAA;QACpE,IAAI,IAAA,KAAS,KAAA,CAAA;YAAW,IAAA,GAAO,YAAA,CAAa,IAAA,CAAA;QAE5C,MAAM,OAAA,GAAU,IAAA,KAAS,KAAA,CAAA,CAAA;QAGzB,MAAM,UAAA,GAAa,YAAA,CAAa,SAAA,CAAA;QAEhC,MAAM,SAAA,GAAY,CAAC,OAAA,IAAW,UAAA,CAAA;QAE9B,MAAM,SAAA,GAAY,YAAA,CAAa,SAAA,IAAc,UAAA,IAAc,OAAA,CAAA;QAE3D,OAAO,aAAA,CAAA,cAAA,CAAA,EAAA,EACF,YAAA,CAAA,EADE;YAEL,IAAA;YACA,WAAA,EAAa,YAAA,CAAa,IAAA;YAC1B,UAAA;YACA,SAAA;YACA,SAAA;SAAA,CAAA,CAAA;IAAA,CAAA;IAIJ,SAAA,WAAA,CACE,YAAA,EACA,cAAA;QAEA,MAAM,QAAA,GAAW,WAAA,EAAA,CAAA;QACjB,MAAM,oBAAA,GAAuB,qBAAA,CAAsB,cAAA,CAAA,CAAA;QAEnD,OAAO,WAAA,CACL,CAAC,GAAA,EAAU,OAAA,EAAA,EAAA,CACT,QAAA,CACG,GAAA,CAAI,IAAA,CAAK,QAAA,CAAkC,YAAA,EAAc,GAAA,EAAK,cAAA,CAAA,cAAA,CAAA,EAAA,EAC1D,oBAAA,CAAA,EACA,OAAA,CAAA,CAAA,CAAA,EAGT,CAAC,YAAA,EAAc,QAAA,EAAU,oBAAA,CAAA,CAAA,CAAA;IAAA,CAAA;IAI7B,SAAA,eAAA,CAAyB,IAAA;QACvB,MAAM,oBAAA,GAAkD,CACtD,GAAA,EACA,EACE,kBAAA,EACA,cAAA,EACA,yBAAA,EACA,IAAA,GAAO,KAAA,EACP,eAAA,GAAkB,CAAA,EAAA,GAChB,EAAA,EAAA,EAAA;YAEJ,MAAM,EAAE,QAAA,EAAA,GAAa,GAAA,CAAI,SAAA,CAAU,IAAA,CAAA,CAAA;YAInC,MAAM,QAAA,GAAW,WAAA,EAAA,CAAA;YACjB,MAAM,SAAA,GAAY,kBAAA,CAChB,IAAA,CAAA,CAAA,CAAO,SAAA,CAAA,CAAA,CAAY,GAAA,EAMnB,yBAAA,EACA,OAAA,CAAQ,mBAAA,CAAoB,IAAA,CAAA,EAC5B,IAAA,CAAA,CAAA;YAEF,MAAM,yBAAA,GAA4B,qBAAA,CAAsB;gBACtD,kBAAA;gBACA,cAAA;gBACA,eAAA;aAAA,CAAA,CAAA;YAGF,MAAM,yBAAA,GAA4B,OAAA,CAAO,KAAA,CAAA,CAAA;YAEzC,MAAM,UAAA,GAAa,OAAA,EAAA,CAAA;YAEnB,IAAI,EAAE,aAAA,EAAe,SAAA,EAAA,GAAc,UAAA,CAAW,OAAA,IAAW,EAAA,CAAA;YAIzD,IAAI,4BAAA,GAA+B,KAAA,CAAA;YACnC,IAAI,aAAA,IAAiB,SAAA,EAAW;gBAE9B,MAAM,aAAA,GAAgB,QAAA,CACpB,GAAA,CAAI,eAAA,CAAgB,0BAAA,CAA2B;oBAC7C,aAAA;oBACA,SAAA;iBAAA,CAAA,CAAA,CAAA;gBAIJ,IAAI,IAAA,EAAuC;oBACzC,IAAI,OAAO,aAAA,KAAkB,SAAA,EAAW;wBACtC,MAAM,IAAI,KAAA,CACR,yDAAyD,GAAA,CAAI,WAAA;qEAAA,CAAA,CAAA;qBAAA;iBAAA;gBAMnE,4BAAA,GAA+B,CAAC,CAAC,aAAA,CAAA;aAAA;YAGnC,MAAM,mBAAA,GACJ,CAAC,4BAAA,IAAgC,yBAAA,CAA0B,OAAA,CAAA;YAE7D,0BAAA,CAA2B,GAAA,EAAA;gBACzB,yBAAA,CAA0B,OAAA,GAAU,4BAAA,CAAA;YAAA,CAAA,CAAA,CAAA;YAGtC,0BAAA,CAA2B,GAAA,EAAA;gBACzB,IAAI,mBAAA,EAAqB;oBACvB,UAAA,CAAW,OAAA,GAAU,KAAA,CAAA,CAAA;iBAAA;YAAA,CAAA,EAEtB,CAAC,mBAAA,CAAA,CAAA,CAAA;YAEJ,0BAAA,CAA2B,GAAA,EAAA;gBA3uBjC,IAAA,EAAA,CAAA;gBA4uBQ,MAAM,WAAA,GAAc,UAAA,CAAW,OAAA,CAAA;gBAC/B,IACE,OAAO,OAAA,KAAY,WAAA,IACnB,KAAA,EACA;oBAEA,OAAA,CAAQ,GAAA,CAAI,mBAAA,CAAA,CAAA;iBAAA;gBAGd,IAAI,SAAA,KAAc,SAAA,EAAW;oBAC3B,WAAA,IAAA,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAa,WAAA,EAAA,CAAA;oBACb,UAAA,CAAW,OAAA,GAAU,KAAA,CAAA,CAAA;oBACrB,OAAA;iBAAA;gBAGF,MAAM,uBAAA,GAA0B,CAAA,EAAA,GAAA,UAAA,CAAW,OAAA,CAAA,IAAX,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAoB,mBAAA,CAAA;gBAEpD,IAAI,CAAC,WAAA,IAAe,WAAA,CAAY,GAAA,KAAQ,SAAA,EAAW;oBACjD,WAAA,IAAA,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAa,WAAA,EAAA,CAAA;oBACb,MAAM,OAAA,GAAU,QAAA,CACd,QAAA,CAAS,SAAA,EAAW;wBAClB,mBAAA,EAAqB,yBAAA;wBACrB,YAAA,EAAc,yBAAA;qBAAA,CAAA,CAAA,CAAA;oBAIlB,UAAA,CAAW,OAAA,GAAU,OAAA,CAAA;iBAAA;qBAAA,IACZ,yBAAA,KAA8B,uBAAA,EAAyB;oBAChE,WAAA,CAAY,yBAAA,CAA0B,yBAAA,CAAA,CAAA;iBAAA;YAAA,CAAA,EAEvC;gBACD,QAAA;gBACA,QAAA;gBACA,yBAAA;gBACA,SAAA;gBACA,yBAAA;gBACA,mBAAA;aAAA,CAAA,CAAA;YAGF,UAAA,CAAU,GAAA,EAAA;gBACR,OAAO,GAAA,EAAA;oBApxBf,IAAA,EAAA,CAAA;oBAqxBU,CAAA,EAAA,GAAA,UAAA,CAAW,OAAA,CAAA,IAAX,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAoB,WAAA,EAAA,CAAA;oBACpB,UAAA,CAAW,OAAA,GAAU,KAAA,CAAA,CAAA;gBAAA,CAAA,CAAA;YAAA,CAAA,EAEtB,EAAA,CAAA,CAAA;YAEH,OAAO,QAAA,CACL,GAAA,EAAA,CAAO,CAAA;gBAIL,OAAA,EAAS,GAAA,EAAA;oBA/xBnB,IAAA,EAAA,CAAA;oBAgyBY,IAAI,CAAC,UAAA,CAAW,OAAA;wBACd,MAAM,IAAI,KAAA,CACR,uDAAA,CAAA,CAAA;oBAEJ,OAAO,CAAA,EAAA,GAAA,UAAA,CAAW,OAAA,CAAA,IAAX,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAoB,OAAA,EAAA,CAAA;gBAAA,CAAA;aAAA,CAAA,EAG/B,EAAA,CAAA,CAAA;QAAA,CAAA,CAAA;QAIJ,MAAM,wBAAA,GAA0D,CAAC,EAC/D,kBAAA,EACA,cAAA,EACA,eAAA,GAAkB,CAAA,EAAA,GAChB,EAAA,EAAA,EAAA;YACF,MAAM,EAAE,QAAA,EAAA,GAAa,GAAA,CAAI,SAAA,CAAU,IAAA,CAAA,CAAA;YAInC,MAAM,QAAA,GAAW,WAAA,EAAA,CAAA;YAEjB,MAAM,CAAC,GAAA,EAAK,MAAA,CAAA,GAAU,QAAA,CAAc,mBAAA,CAAA,CAAA;YACpC,MAAM,UAAA,GAAa,OAAA,EAAA,CAAA;YAEnB,MAAM,yBAAA,GAA4B,qBAAA,CAAsB;gBACtD,kBAAA;gBACA,cAAA;gBACA,eAAA;aAAA,CAAA,CAAA;YAGF,0BAAA,CAA2B,GAAA,EAAA;gBA/zBjC,IAAA,EAAA,EAAA,EAAA,CAAA;gBAg0BQ,MAAM,uBAAA,GAA0B,CAAA,EAAA,GAAA,UAAA,CAAW,OAAA,CAAA,IAAX,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAoB,mBAAA,CAAA;gBAEpD,IAAI,yBAAA,KAA8B,uBAAA,EAAyB;oBACzD,CAAA,EAAA,GAAA,UAAA,CAAW,OAAA,CAAA,IAAX,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAoB,yBAAA,CAClB,yBAAA,CAAA,CAAA;iBAAA;YAAA,CAAA,EAGH,CAAC,yBAAA,CAAA,CAAA,CAAA;YAEJ,MAAM,sBAAA,GAAyB,OAAA,CAAO,yBAAA,CAAA,CAAA;YACtC,0BAAA,CAA2B,GAAA,EAAA;gBACzB,sBAAA,CAAuB,OAAA,GAAU,yBAAA,CAAA;YAAA,CAAA,EAChC,CAAC,yBAAA,CAAA,CAAA,CAAA;YAEJ,MAAM,OAAA,GAAU,WAAA,CACd,UAAU,IAAA,EAAU,gBAAA,GAAmB,KAAA;gBACrC,IAAI,OAAA,CAAA;gBAEJ,KAAA,CAAM,GAAA,EAAA;oBAl1BhB,IAAA,EAAA,CAAA;oBAm1BY,CAAA,EAAA,GAAA,UAAA,CAAW,OAAA,CAAA,IAAX,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAoB,WAAA,EAAA,CAAA;oBAEpB,UAAA,CAAW,OAAA,GAAU,OAAA,GAAU,QAAA,CAC7B,QAAA,CAAS,IAAA,EAAK;wBACZ,mBAAA,EAAqB,sBAAA,CAAuB,OAAA;wBAC5C,YAAA,EAAc,CAAC,gBAAA;qBAAA,CAAA,CAAA,CAAA;oBAInB,MAAA,CAAO,IAAA,CAAA,CAAA;gBAAA,CAAA,CAAA,CAAA;gBAGT,OAAO,OAAA,CAAA;YAAA,CAAA,EAET,CAAC,QAAA,EAAU,QAAA,CAAA,CAAA,CAAA;YAIb,UAAA,CAAU,GAAA,EAAA;gBACR,OAAO,GAAA,EAAA;oBAt2Bf,IAAA,EAAA,CAAA;oBAu2BU,CAAA,EAAA,GAAA,UAAA,IAAA,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAY,OAAA,CAAA,IAAZ,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAqB,WAAA,EAAA,CAAA;gBAAA,CAAA,CAAA;YAAA,CAAA,EAEtB,EAAA,CAAA,CAAA;YAGH,UAAA,CAAU,GAAA,EAAA;gBACR,IAAI,GAAA,KAAQ,mBAAA,IAAuB,CAAC,UAAA,CAAW,OAAA,EAAS;oBACtD,OAAA,CAAQ,GAAA,EAAK,IAAA,CAAA,CAAA;iBAAA;YAAA,CAAA,EAEd,CAAC,GAAA,EAAK,OAAA,CAAA,CAAA,CAAA;YAET,OAAO,QAAA,CAAQ,GAAA,EAAA,CAAM,CAAC,OAAA,EAAS,GAAA,CAAA,EAAe,CAAC,OAAA,EAAS,GAAA,CAAA,CAAA,CAAA;QAAA,CAAA,CAAA;QAG1D,MAAM,aAAA,GAAoC,CACxC,GAAA,EACA,EAAE,IAAA,GAAO,KAAA,EAAO,gBAAA,EAAA,GAAqB,EAAA,EAAA,EAAA;YAErC,MAAM,EAAE,MAAA,EAAA,GAAW,GAAA,CAAI,SAAA,CAAU,IAAA,CAAA,CAAA;YAIjC,MAAM,SAAA,GAAY,kBAAA,CAChB,IAAA,CAAA,CAAA,CAAO,SAAA,CAAA,CAAA,CAAY,GAAA,EACnB,kBAAA,EACA,OAAA,CAAQ,mBAAA,CAAoB,IAAA,CAAA,EAC5B,IAAA,CAAA,CAAA;YAKF,MAAM,SAAA,GAAY,OAAA,EAAA,CAAA;YAElB,MAAM,mBAAA,GAA0D,QAAA,CAC9D,GAAA,EAAA,CACE,cAAA,CACE;gBACE,MAAA,CAAO,SAAA,CAAA;gBACP,CAAC,CAAA,EAAiB,UAAA,EAAA,EAAA,CAAoB,UAAA;gBACtC,CAAC,CAAA,EAAA,EAAA,CAAoB,SAAA;aAAA,EAEvB,qBAAA,CAAA,EAEJ,CAAC,MAAA,EAAQ,SAAA,CAAA,CAAA,CAAA;YAGX,MAAM,aAAA,GAAoD,QAAA,CACxD,GAAA,EAAA,CACE,gBAAA,CAAA,CAAA,CACI,cAAA,CAAe,CAAC,mBAAA,CAAA,EAAsB,gBAAA,CAAA,CAAA,CAAA,CACtC,mBAAA,EACN,CAAC,mBAAA,EAAqB,gBAAA,CAAA,CAAA,CAAA;YAGxB,MAAM,YAAA,GAAe,WAAA,CACnB,CAAC,KAAA,EAAA,EAAA,CACC,aAAA,CAAc,KAAA,EAAO,SAAA,CAAU,OAAA,CAAA,EACjC,aAAA,CAAA,CAAA;YAGF,MAAM,KAAA,GAAQ,QAAA,EAAA,CAAA;YACd,MAAM,YAAA,GAAe,mBAAA,CACnB,KAAA,CAAM,QAAA,EAAA,EACN,SAAA,CAAU,OAAA,CAAA,CAAA;YAEZ,yBAAA,CAA0B,GAAA,EAAA;gBACxB,SAAA,CAAU,OAAA,GAAU,YAAA,CAAA;YAAA,CAAA,EACnB,CAAC,YAAA,CAAA,CAAA,CAAA;YAEJ,OAAO,YAAA,CAAA;QAAA,CAAA,CAAA;QAGT,OAAO;YACL,aAAA;YACA,oBAAA;YACA,wBAAA;YACA,YAAA,CAAa,OAAA;gBACX,MAAM,CAAC,OAAA,EAAS,GAAA,CAAA,GAAO,wBAAA,CAAyB,OAAA,CAAA,CAAA;gBAChD,MAAM,iBAAA,GAAoB,aAAA,CAAc,GAAA,EAAK,aAAA,CAAA,cAAA,CAAA,EAAA,EACxC,OAAA,CAAA,EADwC;oBAE3C,IAAA,EAAM,GAAA,KAAQ,mBAAA;iBAAA,CAAA,CAAA,CAAA;gBAGhB,MAAM,IAAA,GAAO,QAAA,CAAQ,GAAA,EAAA,CAAO,CAAA,EAAE,OAAA,EAAS,GAAA,EAAA,CAAA,EAAQ,CAAC,GAAA,CAAA,CAAA,CAAA;gBAChD,OAAO,QAAA,CACL,GAAA,EAAA,CAAM,CAAC,OAAA,EAAS,iBAAA,EAAmB,IAAA,CAAA,EACnC,CAAC,OAAA,EAAS,iBAAA,EAAmB,IAAA,CAAA,CAAA,CAAA;YAAA,CAAA;YAGjC,QAAA,CAAS,GAAA,EAAK,OAAA;gBACZ,MAAM,wBAAA,GAA2B,oBAAA,CAAqB,GAAA,EAAK,OAAA,CAAA,CAAA;gBAC3D,MAAM,iBAAA,GAAoB,aAAA,CAAc,GAAA,EAAK,cAAA,CAAA;oBAC3C,gBAAA,EACE,GAAA,KAAQ,SAAA,IAAa,CAAA,OAAA,IAAA,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAS,IAAA,CAAA,CAAA,CAAA,CAC1B,KAAA,CAAA,CAAA,CAAA,CACA,2BAAA;iBAAA,EACH,OAAA,CAAA,CAAA,CAAA;gBAGL,MAAM,EAAE,IAAA,EAAM,MAAA,EAAQ,SAAA,EAAW,SAAA,EAAW,OAAA,EAAS,KAAA,EAAA,GACnD,iBAAA,CAAA;gBACF,aAAA,CAAc,EAAE,IAAA,EAAM,MAAA,EAAQ,SAAA,EAAW,SAAA,EAAW,OAAA,EAAS,KAAA,EAAA,CAAA,CAAA;gBAE7D,OAAO,QAAA,CACL,GAAA,EAAA,CAAO,cAAA,CAAA,cAAA,CAAA,EAAA,EAAK,iBAAA,CAAA,EAAsB,wBAAA,CAAA,EAClC,CAAC,iBAAA,EAAmB,wBAAA,CAAA,CAAA,CAAA;YAAA,CAAA;SAAA,CAAA;IAAA,CAAA;IAM5B,SAAA,iBAAA,CAA2B,IAAA;QACzB,OAAO,CAAC,EACN,gBAAA,GAAmB,4BAAA,EACnB,aAAA,EAAA,GACE,EAAA,EAAA,EAAA;YACF,MAAM,EAAE,MAAA,EAAQ,QAAA,EAAA,GAAa,GAAA,CAAI,SAAA,CAAU,IAAA,CAAA,CAAA;YAI3C,MAAM,QAAA,GAAW,WAAA,EAAA,CAAA;YACjB,MAAM,CAAC,OAAA,EAAS,UAAA,CAAA,GAAc,QAAA,EAAA,CAAA;YAE9B,UAAA,CACE,GAAA,EAAA,CAAM,GAAA,EAAA;gBACJ,IAAI,CAAC,CAAA,OAAA,IAAA,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAS,GAAA,CAAI,aAAA,CAAA,EAAe;oBAC/B,OAAA,IAAA,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAS,KAAA,EAAA,CAAA;iBAAA;YAAA,CAAA,EAGb,CAAC,OAAA,CAAA,CAAA,CAAA;YAGH,MAAM,eAAA,GAAkB,WAAA,CACtB,UAAU,GAAA;gBACR,MAAM,QAAA,GAAU,QAAA,CAAS,QAAA,CAAS,GAAA,EAAK,EAAE,aAAA,EAAA,CAAA,CAAA,CAAA;gBACzC,UAAA,CAAW,QAAA,CAAA,CAAA;gBACX,OAAO,QAAA,CAAA;YAAA,CAAA,EAET,CAAC,QAAA,EAAU,QAAA,EAAU,aAAA,CAAA,CAAA,CAAA;YAGvB,MAAM,EAAE,SAAA,EAAA,GAAc,OAAA,IAAW,EAAA,CAAA;YACjC,MAAM,gBAAA,GAAmB,QAAA,CACvB,GAAA,EAAA,CACE,cAAA,CACE,CAAC,MAAA,CAAO,EAAE,aAAA,EAAe,SAAA,EAAW,OAAA,IAAA,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAS,SAAA,EAAA,CAAA,CAAA,EAC7C,gBAAA,CAAA,EAEJ,CAAC,MAAA,EAAQ,OAAA,EAAS,gBAAA,EAAkB,aAAA,CAAA,CAAA,CAAA;YAGtC,MAAM,YAAA,GAAe,WAAA,CAAY,gBAAA,EAAkB,aAAA,CAAA,CAAA;YACnD,MAAM,YAAA,GACJ,aAAA,IAAiB,IAAA,CAAA,CAAA,CAAO,OAAA,IAAA,IAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAS,GAAA,CAAI,YAAA,CAAA,CAAA,CAAe,KAAA,CAAA,CAAA;YACtD,MAAM,KAAA,GAAQ,WAAA,CAAY,GAAA,EAAA;gBACxB,KAAA,CAAM,GAAA,EAAA;oBACJ,IAAI,OAAA,EAAS;wBACX,UAAA,CAAW,KAAA,CAAA,CAAA,CAAA;qBAAA;oBAEb,IAAI,aAAA,EAAe;wBACjB,QAAA,CACE,GAAA,CAAI,eAAA,CAAgB,oBAAA,CAAqB;4BACvC,SAAA;4BACA,aAAA;yBAAA,CAAA,CAAA,CAAA;qBAAA;gBAAA,CAAA,CAAA,CAAA;YAAA,CAAA,EAKP,CAAC,QAAA,EAAU,aAAA,EAAe,OAAA,EAAS,SAAA,CAAA,CAAA,CAAA;YAEtC,MAAM,EACJ,YAAA,EACA,IAAA,EACA,MAAA,EACA,SAAA,EACA,SAAA,EACA,OAAA,EACA,KAAA,EAAA,GACE,YAAA,CAAA;YACJ,aAAA,CAAc;gBACZ,YAAA;gBACA,IAAA;gBACA,MAAA;gBACA,SAAA;gBACA,SAAA;gBACA,OAAA;gBACA,KAAA;aAAA,CAAA,CAAA;YAGF,MAAM,UAAA,GAAa,QAAA,CACjB,GAAA,EAAA,CAAO,aAAA,CAAA,cAAA,CAAA,EAAA,EAAK,YAAA,CAAA,EAAL,EAAmB,YAAA,EAAc,KAAA,EAAA,CAAA,EACxC,CAAC,YAAA,EAAc,YAAA,EAAc,KAAA,CAAA,CAAA,CAAA;YAG/B,OAAO,QAAA,CACL,GAAA,EAAA,CAAM,CAAC,eAAA,EAAiB,UAAA,CAAA,EACxB,CAAC,eAAA,EAAiB,UAAA,CAAA,CAAA,CAAA;QAAA,CAAA,CAAA;IAAA,CAAA;AAAA,CAAA;;AKn2BnB,IAAK,cAAA,CAAA;AAAL,CAAA,UAAK,eAAA;IACV,eAAA,CAAA,OAAA,CAAA,GAAQ,OAAA,CAAA;IACR,eAAA,CAAA,UAAA,CAAA,GAAW,UAAA,CAAA;AAAA,CAAA,CAAA,CAFD,cAAA,IAAA,CAAA,cAAA,GAAA,EAAA,CAAA,CAAA,CAAA;AAybL,SAAA,iBAAA,CACL,CAAA;IAEA,OAAO,CAAA,CAAE,IAAA,KAAS,cAAA,CAAe,KAAA,CAAA;AAAA,CAAA;AAG5B,SAAA,oBAAA,CACL,CAAA;IAEA,OAAO,CAAA,CAAE,IAAA,KAAS,cAAA,CAAe,QAAA,CAAA;AAAA,CAAA;;AC1oB5B,SAAA,UAAA,CAAoB,GAAA;IACzB,OAAO,GAAA,CAAI,OAAA,CAAQ,GAAA,CAAI,CAAA,CAAA,EAAI,GAAA,CAAI,CAAA,CAAA,CAAG,WAAA,EAAA,CAAA,CAAA;AAAA,CAAA;;ACK7B,SAAA,UAAA,CACL,MAAA,EAAA,GACG,IAAA;IAEH,MAAA,CAAO,MAAA,CAAO,MAAA,EAAQ,GAAG,IAAA,CAAA,CAAA;AAAA,CAAA;;ACM3B,OAAA,EAAA,WAAA,IAAA,aAAA,EAAA,WAAA,IAAA,aAAA,EAAA,QAAA,IAAA,UAAA,EAAA,KAAA,IAAA,OAAA,EAAA,MAAA,aAAA,CAAA;AASO,IAAM,oBAAA,GAAuC,eAAA,CAAA,MAAA,EAAA,CAAA;AAkG7C,IAAM,gBAAA,GAAmB,CAAC,EAC/B,KAAA,GAAQ,OAAA,EACR,WAAA,GAAc,aAAA,EACd,WAAA,GAAc,aAAA,EACd,QAAA,GAAW,UAAA,EACX,6BAAA,GAAgC,KAAA,EAAA,GACL,EAAA,EAAA,EAAA,CAAkC,CAAA;IAC7D,IAAA,EAAM,oBAAA;IACN,IAAA,CAAK,GAAA,EAAK,EAAE,kBAAA,EAAA,EAAsB,OAAA;QAChC,MAAM,MAAA,GAAS,GAAA,CAAA;QAOf,MAAM,EAAE,eAAA,EAAiB,iBAAA,EAAmB,WAAA,EAAA,GAAgB,UAAA,CAAW;YACrE,GAAA;YACA,aAAA,EAAe;gBACb,KAAA;gBACA,WAAA;gBACA,WAAA;gBACA,QAAA;gBACA,6BAAA;aAAA;YAEF,kBAAA;YACA,OAAA;SAAA,CAAA,CAAA;QAEF,UAAA,CAAW,MAAA,EAAQ,EAAE,WAAA,EAAA,CAAA,CAAA;QACrB,UAAA,CAAW,OAAA,EAAS,EAAE,KAAA,EAAA,CAAA,CAAA;QAEtB,OAAO;YACL,cAAA,CAAe,YAAA,EAAc,UAAA;gBAC3B,IAAI,iBAAA,CAAkB,UAAA,CAAA,EAAa;oBACjC,MAAM,EACJ,QAAA,EACA,YAAA,EACA,wBAAA,EACA,aAAA,EACA,oBAAA,EAAA,GACE,eAAA,CAAgB,YAAA,CAAA,CAAA;oBACpB,UAAA,CAAW,MAAA,CAAO,SAAA,CAAU,YAAA,CAAA,EAAe;wBACzC,QAAA;wBACA,YAAA;wBACA,wBAAA;wBACA,aAAA;wBACA,oBAAA;qBAAA,CAAA,CAAA;oBAEA,GAAA,CAAY,MAAM,UAAA,CAAW,YAAA,CAAA,OAAA,CAAA,GAAwB,QAAA,CAAA;oBACrD,GAAA,CAAY,UAAU,UAAA,CAAW,YAAA,CAAA,OAAA,CAAA,GACjC,YAAA,CAAA;iBAAA;qBAAA,IACO,oBAAA,CAAqB,UAAA,CAAA,EAAa;oBAC3C,MAAM,WAAA,GAAc,iBAAA,CAAkB,YAAA,CAAA,CAAA;oBACtC,UAAA,CAAW,MAAA,CAAO,SAAA,CAAU,YAAA,CAAA,EAAe;wBACzC,WAAA;qBAAA,CAAA,CAAA;oBAEA,GAAA,CAAY,MAAM,UAAA,CAAW,YAAA,CAAA,UAAA,CAAA,GAA2B,WAAA,CAAA;iBAAA;YAAA,CAAA;SAAA,CAAA;IAAA,CAAA;CAAA,CAAA,CAAA;;AThLpE,cAAA,wBAAA,CAAA;;AUHA,OAAA,EAAA,cAAA,EAAA,MAAA,kBAAA,CAAA;AAEA,OAAA,EAAA,SAAA,IAAA,UAAA,EAAA,MAAA,OAAA,CAAA;AACA,OAAA,KAAA,MAAA,OAAA,CAAA;AAEA,OAAA,EAAA,QAAA,EAAA,MAAA,aAAA,CAAA;AACA,OAAA,EAAA,cAAA,EAAA,MAAA,wBAAA,CAAA;AA2BO,SAAA,WAAA,CAAuD,KAAA;IAM5D,MAAM,CAAC,KAAA,CAAA,GAAS,KAAA,CAAM,QAAA,CAAS,GAAA,EAAA,CAC7B,cAAA,CAAe;QACb,OAAA,EAAS;YAAA,CACN,KAAA,CAAM,GAAA,CAAI,WAAA,CAAA,EAAc,KAAA,CAAM,GAAA,CAAI,OAAA;SAAA;QAErC,UAAA,EAAY,CAAC,GAAA,EAAA,EAAA,CAAQ,GAAA,EAAA,CAAM,MAAA,CAAO,KAAA,CAAM,GAAA,CAAI,UAAA,CAAA;KAAA,CAAA,CAAA,CAAA;IAIhD,UAAA,CACE,GAAA,EAAA,CACE,KAAA,CAAM,cAAA,KAAmB,KAAA,CAAA,CAAA,CACrB,KAAA,CAAA,CAAA,CAAA,CACA,cAAA,CAAe,KAAA,CAAM,QAAA,EAAU,KAAA,CAAM,cAAA,CAAA,EAC3C,CAAC,KAAA,CAAM,cAAA,EAAgB,KAAA,CAAM,QAAA,CAAA,CAAA,CAAA;IAG/B,OACE,eAAA,CAAA,KAAA,CAAA,aAAA,CAAC,QAAA,EAAD;QAAU,KAAA;QAAc,OAAA,EAAS,KAAA,CAAM,OAAA;KAAA,EACpC,KAAA,CAAM,QAAA,CAAA,CAAA;AAAA,CAAA;;AVpDb,IAAM,SAAA,GAA4B,eAAA,CAAA,cAAA,CAChC,UAAA,EAAA,EACA,gBAAA,EAAA,CAAA,CAAA", "sourcesContent": ["import { coreModule, buildCreateApi } from '@reduxjs/toolkit/query'\r\nimport { reactHooksModule, reactHooksModuleName } from './module'\r\n\r\nexport * from '@reduxjs/toolkit/query'\r\nexport { ApiProvider } from './ApiProvider'\r\n\r\nconst createApi = /* @__PURE__ */ buildCreateApi(\r\n  coreModule(),\r\n  reactHooksModule()\r\n)\r\n\r\nexport type {\r\n  TypedUseQueryHookResult,\r\n  TypedUseQueryStateResult,\r\n  TypedUseQuerySubscriptionResult,\r\n  TypedUseMutationResult,\r\n} from './buildHooks'\r\nexport { createApi, reactHooksModule, reactHooksModuleName }\r\n", "import type { AnyAction, ThunkAction, ThunkDispatch } from '@reduxjs/toolkit'\r\nimport { createSelector } from '@reduxjs/toolkit'\r\nimport type { Selector } from '@reduxjs/toolkit'\r\nimport type { DependencyList } from 'react'\r\nimport {\r\n  useCallback,\r\n  useDebugValue,\r\n  useEffect,\r\n  useLayoutEffect,\r\n  useMemo,\r\n  useRef,\r\n  useState,\r\n} from 'react'\r\nimport { QueryStatus, skipToken } from '@reduxjs/toolkit/query'\r\nimport type {\r\n  QuerySubState,\r\n  SubscriptionOptions,\r\n  QueryKeys,\r\n  RootState,\r\n} from '@reduxjs/toolkit/query'\r\nimport type {\r\n  EndpointDefinitions,\r\n  MutationDefinition,\r\n  QueryDefinition,\r\n  QueryArgFrom,\r\n  ResultTypeFrom,\r\n} from '@reduxjs/toolkit/query'\r\nimport type {\r\n  QueryResultSelectorResult,\r\n  MutationResultSelectorResult,\r\n  SkipToken,\r\n} from '@reduxjs/toolkit/query'\r\nimport type {\r\n  QueryActionCreatorResult,\r\n  MutationActionCreatorResult,\r\n} from '@reduxjs/toolkit/query'\r\nimport type { SerializeQueryArgs } from '@reduxjs/toolkit/query'\r\nimport { shallowEqual } from 'react-redux'\r\nimport type { Api, ApiContext } from '@reduxjs/toolkit/query'\r\nimport type {\r\n  TSHelpersId,\r\n  TSHelpersNoInfer,\r\n  TSHelpersOverride,\r\n} from '@reduxjs/toolkit/query'\r\nimport type {\r\n  ApiEndpointMutation,\r\n  ApiEndpointQuery,\r\n  CoreModule,\r\n  PrefetchOptions,\r\n} from '@reduxjs/toolkit/query'\r\nimport type { ReactHooksModuleOptions } from './module'\r\nimport { useStableQueryArgs } from './useSerializedStableValue'\r\nimport type { UninitializedValue } from './constants'\r\nimport { UNINITIALIZED_VALUE } from './constants'\r\nimport { useShallowStableValue } from './useShallowStableValue'\r\nimport type { BaseQueryFn } from '../baseQueryTypes'\r\nimport { defaultSerializeQueryArgs } from '../defaultSerializeQueryArgs'\r\n\r\n// Copy-pasted from React-Redux\r\nexport const useIsomorphicLayoutEffect =\r\n  typeof window !== 'undefined' &&\r\n  !!window.document &&\r\n  !!window.document.createElement\r\n    ? useLayoutEffect\r\n    : useEffect\r\n\r\nexport interface QueryHooks<\r\n  Definition extends QueryDefinition<any, any, any, any, any>\r\n> {\r\n  useQuery: UseQuery<Definition>\r\n  useLazyQuery: UseLazyQuery<Definition>\r\n  useQuerySubscription: UseQuerySubscription<Definition>\r\n  useLazyQuerySubscription: UseLazyQuerySubscription<Definition>\r\n  useQueryState: UseQueryState<Definition>\r\n}\r\n\r\nexport interface MutationHooks<\r\n  Definition extends MutationDefinition<any, any, any, any, any>\r\n> {\r\n  useMutation: UseMutation<Definition>\r\n}\r\n\r\n/**\r\n * A React hook that automatically triggers fetches of data from an endpoint, 'subscribes' the component to the cached data, and reads the request status and cached data from the Redux store. The component will re-render as the loading status changes and the data becomes available.\r\n *\r\n * The query arg is used as a cache key. Changing the query arg will tell the hook to re-fetch the data if it does not exist in the cache already, and the hook will return the data for that query arg once it's available.\r\n *\r\n * This hook combines the functionality of both [`useQueryState`](#usequerystate) and [`useQuerySubscription`](#usequerysubscription) together, and is intended to be used in the majority of situations.\r\n *\r\n * #### Features\r\n *\r\n * - Automatically triggers requests to retrieve data based on the hook argument and whether cached data exists by default\r\n * - 'Subscribes' the component to keep cached data in the store, and 'unsubscribes' when the component unmounts\r\n * - Accepts polling/re-fetching options to trigger automatic re-fetches when the corresponding criteria is met\r\n * - Returns the latest request status and cached data from the Redux store\r\n * - Re-renders as the request status changes and data becomes available\r\n */\r\nexport type UseQuery<D extends QueryDefinition<any, any, any, any>> = <\r\n  R extends Record<string, any> = UseQueryStateDefaultResult<D>\r\n>(\r\n  arg: QueryArgFrom<D> | SkipToken,\r\n  options?: UseQuerySubscriptionOptions & UseQueryStateOptions<D, R>\r\n) => UseQueryHookResult<D, R>\r\n\r\nexport type UseQueryHookResult<\r\n  D extends QueryDefinition<any, any, any, any>,\r\n  R = UseQueryStateDefaultResult<D>\r\n> = UseQueryStateResult<D, R> & UseQuerySubscriptionResult<D>\r\n\r\n/**\r\n * Helper type to manually type the result\r\n * of the `useQuery` hook in userland code.\r\n */\r\nexport type TypedUseQueryHookResult<\r\n  ResultType,\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  R = UseQueryStateDefaultResult<\r\n    QueryDefinition<QueryArg, BaseQuery, string, ResultType, string>\r\n  >\r\n> = TypedUseQueryStateResult<ResultType, QueryArg, BaseQuery, R> &\r\n  TypedUseQuerySubscriptionResult<ResultType, QueryArg, BaseQuery>\r\n\r\ninterface UseQuerySubscriptionOptions extends SubscriptionOptions {\r\n  /**\r\n   * Prevents a query from automatically running.\r\n   *\r\n   * @remarks\r\n   * When `skip` is true (or `skipToken` is passed in as `arg`):\r\n   *\r\n   * - **If the query has cached data:**\r\n   *   * The cached data **will not be used** on the initial load, and will ignore updates from any identical query until the `skip` condition is removed\r\n   *   * The query will have a status of `uninitialized`\r\n   *   * If `skip: false` is set after the initial load, the cached result will be used\r\n   * - **If the query does not have cached data:**\r\n   *   * The query will have a status of `uninitialized`\r\n   *   * The query will not exist in the state when viewed with the dev tools\r\n   *   * The query will not automatically fetch on mount\r\n   *   * The query will not automatically run when additional components with the same query are added that do run\r\n   *\r\n   * @example\r\n   * ```tsx\r\n   * // codeblock-meta no-transpile title=\"Skip example\"\r\n   * const Pokemon = ({ name, skip }: { name: string; skip: boolean }) => {\r\n   *   const { data, error, status } = useGetPokemonByNameQuery(name, {\r\n   *     skip,\r\n   *   });\r\n   *\r\n   *   return (\r\n   *     <div>\r\n   *       {name} - {status}\r\n   *     </div>\r\n   *   );\r\n   * };\r\n   * ```\r\n   */\r\n  skip?: boolean\r\n  /**\r\n   * Defaults to `false`. This setting allows you to control whether if a cached result is already available, RTK Query will only serve a cached result, or if it should `refetch` when set to `true` or if an adequate amount of time has passed since the last successful query result.\r\n   * - `false` - Will not cause a query to be performed _unless_ it does not exist yet.\r\n   * - `true` - Will always refetch when a new subscriber to a query is added. Behaves the same as calling the `refetch` callback or passing `forceRefetch: true` in the action creator.\r\n   * - `number` - **Value is in seconds**. If a number is provided and there is an existing query in the cache, it will compare the current time vs the last fulfilled timestamp, and only refetch if enough time has elapsed.\r\n   *\r\n   * If you specify this option alongside `skip: true`, this **will not be evaluated** until `skip` is false.\r\n   */\r\n  refetchOnMountOrArgChange?: boolean | number\r\n}\r\n\r\n/**\r\n * A React hook that automatically triggers fetches of data from an endpoint, and 'subscribes' the component to the cached data.\r\n *\r\n * The query arg is used as a cache key. Changing the query arg will tell the hook to re-fetch the data if it does not exist in the cache already.\r\n *\r\n * Note that this hook does not return a request status or cached data. For that use-case, see [`useQuery`](#usequery) or [`useQueryState`](#usequerystate).\r\n *\r\n * #### Features\r\n *\r\n * - Automatically triggers requests to retrieve data based on the hook argument and whether cached data exists by default\r\n * - 'Subscribes' the component to keep cached data in the store, and 'unsubscribes' when the component unmounts\r\n * - Accepts polling/re-fetching options to trigger automatic re-fetches when the corresponding criteria is met\r\n */\r\nexport type UseQuerySubscription<\r\n  D extends QueryDefinition<any, any, any, any>\r\n> = (\r\n  arg: QueryArgFrom<D> | SkipToken,\r\n  options?: UseQuerySubscriptionOptions\r\n) => UseQuerySubscriptionResult<D>\r\n\r\nexport type UseQuerySubscriptionResult<\r\n  D extends QueryDefinition<any, any, any, any>\r\n> = Pick<QueryActionCreatorResult<D>, 'refetch'>\r\n\r\n/**\r\n * Helper type to manually type the result\r\n * of the `useQuerySubscription` hook in userland code.\r\n */\r\nexport type TypedUseQuerySubscriptionResult<\r\n  ResultType,\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn\r\n> = UseQuerySubscriptionResult<\r\n  QueryDefinition<QueryArg, BaseQuery, string, ResultType, string>\r\n>\r\n\r\nexport type UseLazyQueryLastPromiseInfo<\r\n  D extends QueryDefinition<any, any, any, any>\r\n> = {\r\n  lastArg: QueryArgFrom<D>\r\n}\r\n\r\n/**\r\n * A React hook similar to [`useQuery`](#usequery), but with manual control over when the data fetching occurs.\r\n *\r\n * This hook includes the functionality of [`useLazyQuerySubscription`](#uselazyquerysubscription).\r\n *\r\n * #### Features\r\n *\r\n * - Manual control over firing a request to retrieve data\r\n * - 'Subscribes' the component to keep cached data in the store, and 'unsubscribes' when the component unmounts\r\n * - Returns the latest request status and cached data from the Redux store\r\n * - Re-renders as the request status changes and data becomes available\r\n * - Accepts polling/re-fetching options to trigger automatic re-fetches when the corresponding criteria is met and the fetch has been manually called at least once\r\n *\r\n * #### Note\r\n *\r\n * When the trigger function returned from a LazyQuery is called, it always initiates a new request to the server even if there is cached data. Set `preferCacheValue`(the second argument to the function) as `true` if you want it to immediately return a cached value if one exists.\r\n */\r\nexport type UseLazyQuery<D extends QueryDefinition<any, any, any, any>> = <\r\n  R extends Record<string, any> = UseQueryStateDefaultResult<D>\r\n>(\r\n  options?: SubscriptionOptions & Omit<UseQueryStateOptions<D, R>, 'skip'>\r\n) => [\r\n  LazyQueryTrigger<D>,\r\n  UseQueryStateResult<D, R>,\r\n  UseLazyQueryLastPromiseInfo<D>\r\n]\r\n\r\nexport type LazyQueryTrigger<D extends QueryDefinition<any, any, any, any>> = {\r\n  /**\r\n   * Triggers a lazy query.\r\n   *\r\n   * By default, this will start a new request even if there is already a value in the cache.\r\n   * If you want to use the cache value and only start a request if there is no cache value, set the second argument to `true`.\r\n   *\r\n   * @remarks\r\n   * If you need to access the error or success payload immediately after a lazy query, you can chain .unwrap().\r\n   *\r\n   * @example\r\n   * ```ts\r\n   * // codeblock-meta title=\"Using .unwrap with async await\"\r\n   * try {\r\n   *   const payload = await getUserById(1).unwrap();\r\n   *   console.log('fulfilled', payload)\r\n   * } catch (error) {\r\n   *   console.error('rejected', error);\r\n   * }\r\n   * ```\r\n   */\r\n  (\r\n    arg: QueryArgFrom<D>,\r\n    preferCacheValue?: boolean\r\n  ): QueryActionCreatorResult<D>\r\n}\r\n\r\n/**\r\n * A React hook similar to [`useQuerySubscription`](#usequerysubscription), but with manual control over when the data fetching occurs.\r\n *\r\n * Note that this hook does not return a request status or cached data. For that use-case, see [`useLazyQuery`](#uselazyquery).\r\n *\r\n * #### Features\r\n *\r\n * - Manual control over firing a request to retrieve data\r\n * - 'Subscribes' the component to keep cached data in the store, and 'unsubscribes' when the component unmounts\r\n * - Accepts polling/re-fetching options to trigger automatic re-fetches when the corresponding criteria is met and the fetch has been manually called at least once\r\n */\r\nexport type UseLazyQuerySubscription<\r\n  D extends QueryDefinition<any, any, any, any>\r\n> = (\r\n  options?: SubscriptionOptions\r\n) => readonly [LazyQueryTrigger<D>, QueryArgFrom<D> | UninitializedValue]\r\n\r\nexport type QueryStateSelector<\r\n  R extends Record<string, any>,\r\n  D extends QueryDefinition<any, any, any, any>\r\n> = (state: UseQueryStateDefaultResult<D>) => R\r\n\r\n/**\r\n * A React hook that reads the request status and cached data from the Redux store. The component will re-render as the loading status changes and the data becomes available.\r\n *\r\n * Note that this hook does not trigger fetching new data. For that use-case, see [`useQuery`](#usequery) or [`useQuerySubscription`](#usequerysubscription).\r\n *\r\n * #### Features\r\n *\r\n * - Returns the latest request status and cached data from the Redux store\r\n * - Re-renders as the request status changes and data becomes available\r\n */\r\nexport type UseQueryState<D extends QueryDefinition<any, any, any, any>> = <\r\n  R extends Record<string, any> = UseQueryStateDefaultResult<D>\r\n>(\r\n  arg: QueryArgFrom<D> | SkipToken,\r\n  options?: UseQueryStateOptions<D, R>\r\n) => UseQueryStateResult<D, R>\r\n\r\nexport type UseQueryStateOptions<\r\n  D extends QueryDefinition<any, any, any, any>,\r\n  R extends Record<string, any>\r\n> = {\r\n  /**\r\n   * Prevents a query from automatically running.\r\n   *\r\n   * @remarks\r\n   * When skip is true:\r\n   *\r\n   * - **If the query has cached data:**\r\n   *   * The cached data **will not be used** on the initial load, and will ignore updates from any identical query until the `skip` condition is removed\r\n   *   * The query will have a status of `uninitialized`\r\n   *   * If `skip: false` is set after skipping the initial load, the cached result will be used\r\n   * - **If the query does not have cached data:**\r\n   *   * The query will have a status of `uninitialized`\r\n   *   * The query will not exist in the state when viewed with the dev tools\r\n   *   * The query will not automatically fetch on mount\r\n   *   * The query will not automatically run when additional components with the same query are added that do run\r\n   *\r\n   * @example\r\n   * ```ts\r\n   * // codeblock-meta title=\"Skip example\"\r\n   * const Pokemon = ({ name, skip }: { name: string; skip: boolean }) => {\r\n   *   const { data, error, status } = useGetPokemonByNameQuery(name, {\r\n   *     skip,\r\n   *   });\r\n   *\r\n   *   return (\r\n   *     <div>\r\n   *       {name} - {status}\r\n   *     </div>\r\n   *   );\r\n   * };\r\n   * ```\r\n   */\r\n  skip?: boolean\r\n  /**\r\n   * `selectFromResult` allows you to get a specific segment from a query result in a performant manner.\r\n   * When using this feature, the component will not rerender unless the underlying data of the selected item has changed.\r\n   * If the selected item is one element in a larger collection, it will disregard changes to elements in the same collection.\r\n   *\r\n   * @example\r\n   * ```ts\r\n   * // codeblock-meta title=\"Using selectFromResult to extract a single result\"\r\n   * function PostsList() {\r\n   *   const { data: posts } = api.useGetPostsQuery();\r\n   *\r\n   *   return (\r\n   *     <ul>\r\n   *       {posts?.data?.map((post) => (\r\n   *         <PostById key={post.id} id={post.id} />\r\n   *       ))}\r\n   *     </ul>\r\n   *   );\r\n   * }\r\n   *\r\n   * function PostById({ id }: { id: number }) {\r\n   *   // Will select the post with the given id, and will only rerender if the given posts data changes\r\n   *   const { post } = api.useGetPostsQuery(undefined, {\r\n   *     selectFromResult: ({ data }) => ({ post: data?.find((post) => post.id === id) }),\r\n   *   });\r\n   *\r\n   *   return <li>{post?.name}</li>;\r\n   * }\r\n   * ```\r\n   */\r\n  selectFromResult?: QueryStateSelector<R, D>\r\n}\r\n\r\nexport type UseQueryStateResult<\r\n  _ extends QueryDefinition<any, any, any, any>,\r\n  R\r\n> = TSHelpersNoInfer<R>\r\n\r\n/**\r\n * Helper type to manually type the result\r\n * of the `useQueryState` hook in userland code.\r\n */\r\nexport type TypedUseQueryStateResult<\r\n  ResultType,\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  R = UseQueryStateDefaultResult<\r\n    QueryDefinition<QueryArg, BaseQuery, string, ResultType, string>\r\n  >\r\n> = TSHelpersNoInfer<R>\r\n\r\ntype UseQueryStateBaseResult<D extends QueryDefinition<any, any, any, any>> =\r\n  QuerySubState<D> & {\r\n    /**\r\n     * Where `data` tries to hold data as much as possible, also re-using\r\n     * data from the last arguments passed into the hook, this property\r\n     * will always contain the received data from the query, for the current query arguments.\r\n     */\r\n    currentData?: ResultTypeFrom<D>\r\n    /**\r\n     * Query has not started yet.\r\n     */\r\n    isUninitialized: false\r\n    /**\r\n     * Query is currently loading for the first time. No data yet.\r\n     */\r\n    isLoading: false\r\n    /**\r\n     * Query is currently fetching, but might have data from an earlier request.\r\n     */\r\n    isFetching: false\r\n    /**\r\n     * Query has data from a successful load.\r\n     */\r\n    isSuccess: false\r\n    /**\r\n     * Query is currently in \"error\" state.\r\n     */\r\n    isError: false\r\n  }\r\n\r\ntype UseQueryStateDefaultResult<D extends QueryDefinition<any, any, any, any>> =\r\n  TSHelpersId<\r\n    | TSHelpersOverride<\r\n        Extract<\r\n          UseQueryStateBaseResult<D>,\r\n          { status: QueryStatus.uninitialized }\r\n        >,\r\n        { isUninitialized: true }\r\n      >\r\n    | TSHelpersOverride<\r\n        UseQueryStateBaseResult<D>,\r\n        | { isLoading: true; isFetching: boolean; data: undefined }\r\n        | ({\r\n            isSuccess: true\r\n            isFetching: true\r\n            error: undefined\r\n          } & Required<\r\n            Pick<UseQueryStateBaseResult<D>, 'data' | 'fulfilledTimeStamp'>\r\n          >)\r\n        | ({\r\n            isSuccess: true\r\n            isFetching: false\r\n            error: undefined\r\n          } & Required<\r\n            Pick<\r\n              UseQueryStateBaseResult<D>,\r\n              'data' | 'fulfilledTimeStamp' | 'currentData'\r\n            >\r\n          >)\r\n        | ({ isError: true } & Required<\r\n            Pick<UseQueryStateBaseResult<D>, 'error'>\r\n          >)\r\n      >\r\n  > & {\r\n    /**\r\n     * @deprecated will be removed in the next version\r\n     * please use the `isLoading`, `isFetching`, `isSuccess`, `isError`\r\n     * and `isUninitialized` flags instead\r\n     */\r\n    status: QueryStatus\r\n  }\r\n\r\nexport type MutationStateSelector<\r\n  R extends Record<string, any>,\r\n  D extends MutationDefinition<any, any, any, any>\r\n> = (state: MutationResultSelectorResult<D>) => R\r\n\r\nexport type UseMutationStateOptions<\r\n  D extends MutationDefinition<any, any, any, any>,\r\n  R extends Record<string, any>\r\n> = {\r\n  selectFromResult?: MutationStateSelector<R, D>\r\n  fixedCacheKey?: string\r\n}\r\n\r\nexport type UseMutationStateResult<\r\n  D extends MutationDefinition<any, any, any, any>,\r\n  R\r\n> = TSHelpersNoInfer<R> & {\r\n  originalArgs?: QueryArgFrom<D>\r\n  /**\r\n   * Resets the hook state to it's initial `uninitialized` state.\r\n   * This will also remove the last result from the cache.\r\n   */\r\n  reset: () => void\r\n}\r\n\r\n/**\r\n * Helper type to manually type the result\r\n * of the `useMutation` hook in userland code.\r\n */\r\nexport type TypedUseMutationResult<\r\n  ResultType,\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  R = MutationResultSelectorResult<\r\n    MutationDefinition<QueryArg, BaseQuery, string, ResultType, string>\r\n  >\r\n> = UseMutationStateResult<\r\n  MutationDefinition<QueryArg, BaseQuery, string, ResultType, string>,\r\n  R\r\n>\r\n\r\n/**\r\n * A React hook that lets you trigger an update request for a given endpoint, and subscribes the component to read the request status from the Redux store. The component will re-render as the loading status changes.\r\n *\r\n * #### Features\r\n *\r\n * - Manual control over firing a request to alter data on the server or possibly invalidate the cache\r\n * - 'Subscribes' the component to keep cached data in the store, and 'unsubscribes' when the component unmounts\r\n * - Returns the latest request status and cached data from the Redux store\r\n * - Re-renders as the request status changes and data becomes available\r\n */\r\nexport type UseMutation<D extends MutationDefinition<any, any, any, any>> = <\r\n  R extends Record<string, any> = MutationResultSelectorResult<D>\r\n>(\r\n  options?: UseMutationStateOptions<D, R>\r\n) => readonly [MutationTrigger<D>, UseMutationStateResult<D, R>]\r\n\r\nexport type MutationTrigger<D extends MutationDefinition<any, any, any, any>> =\r\n  {\r\n    /**\r\n     * Triggers the mutation and returns a Promise.\r\n     * @remarks\r\n     * If you need to access the error or success payload immediately after a mutation, you can chain .unwrap().\r\n     *\r\n     * @example\r\n     * ```ts\r\n     * // codeblock-meta title=\"Using .unwrap with async await\"\r\n     * try {\r\n     *   const payload = await addPost({ id: 1, name: 'Example' }).unwrap();\r\n     *   console.log('fulfilled', payload)\r\n     * } catch (error) {\r\n     *   console.error('rejected', error);\r\n     * }\r\n     * ```\r\n     */\r\n    (arg: QueryArgFrom<D>): MutationActionCreatorResult<D>\r\n  }\r\n\r\nconst defaultQueryStateSelector: QueryStateSelector<any, any> = (x) => x\r\nconst defaultMutationStateSelector: MutationStateSelector<any, any> = (x) => x\r\n\r\n/**\r\n * Wrapper around `defaultQueryStateSelector` to be used in `useQuery`.\r\n * We want the initial render to already come back with\r\n * `{ isUninitialized: false, isFetching: true, isLoading: true }`\r\n * to prevent that the library user has to do an additional check for `isUninitialized`/\r\n */\r\nconst noPendingQueryStateSelector: QueryStateSelector<any, any> = (\r\n  selected\r\n) => {\r\n  if (selected.isUninitialized) {\r\n    return {\r\n      ...selected,\r\n      isUninitialized: false,\r\n      isFetching: true,\r\n      isLoading: selected.data !== undefined ? false : true,\r\n      status: QueryStatus.pending,\r\n    } as any\r\n  }\r\n  return selected\r\n}\r\n\r\ntype GenericPrefetchThunk = (\r\n  endpointName: any,\r\n  arg: any,\r\n  options: PrefetchOptions\r\n) => ThunkAction<void, any, any, AnyAction>\r\n\r\n/**\r\n *\r\n * @param opts.api - An API with defined endpoints to create hooks for\r\n * @param opts.moduleOptions.batch - The version of the `batchedUpdates` function to be used\r\n * @param opts.moduleOptions.useDispatch - The version of the `useDispatch` hook to be used\r\n * @param opts.moduleOptions.useSelector - The version of the `useSelector` hook to be used\r\n * @returns An object containing functions to generate hooks based on an endpoint\r\n */\r\nexport function buildHooks<Definitions extends EndpointDefinitions>({\r\n  api,\r\n  moduleOptions: {\r\n    batch,\r\n    useDispatch,\r\n    useSelector,\r\n    useStore,\r\n    unstable__sideEffectsInRender,\r\n  },\r\n  serializeQueryArgs,\r\n  context,\r\n}: {\r\n  api: Api<any, Definitions, any, any, CoreModule>\r\n  moduleOptions: Required<ReactHooksModuleOptions>\r\n  serializeQueryArgs: SerializeQueryArgs<any>\r\n  context: ApiContext<Definitions>\r\n}) {\r\n  const usePossiblyImmediateEffect: (\r\n    effect: () => void | undefined,\r\n    deps?: DependencyList\r\n  ) => void = unstable__sideEffectsInRender ? (cb) => cb() : useEffect\r\n\r\n  return { buildQueryHooks, buildMutationHook, usePrefetch }\r\n\r\n  function queryStatePreSelector(\r\n    currentState: QueryResultSelectorResult<any>,\r\n    lastResult: UseQueryStateDefaultResult<any> | undefined,\r\n    queryArgs: any\r\n  ): UseQueryStateDefaultResult<any> {\r\n    // if we had a last result and the current result is uninitialized,\r\n    // we might have called `api.util.resetApiState`\r\n    // in this case, reset the hook\r\n    if (lastResult?.endpointName && currentState.isUninitialized) {\r\n      const { endpointName } = lastResult\r\n      const endpointDefinition = context.endpointDefinitions[endpointName]\r\n      if (\r\n        serializeQueryArgs({\r\n          queryArgs: lastResult.originalArgs,\r\n          endpointDefinition,\r\n          endpointName,\r\n        }) ===\r\n        serializeQueryArgs({\r\n          queryArgs,\r\n          endpointDefinition,\r\n          endpointName,\r\n        })\r\n      )\r\n        lastResult = undefined\r\n    }\r\n\r\n    // data is the last known good request result we have tracked - or if none has been tracked yet the last good result for the current args\r\n    let data = currentState.isSuccess ? currentState.data : lastResult?.data\r\n    if (data === undefined) data = currentState.data\r\n\r\n    const hasData = data !== undefined\r\n\r\n    // isFetching = true any time a request is in flight\r\n    const isFetching = currentState.isLoading\r\n    // isLoading = true only when loading while no data is present yet (initial load with no data in the cache)\r\n    const isLoading = !hasData && isFetching\r\n    // isSuccess = true when data is present\r\n    const isSuccess = currentState.isSuccess || (isFetching && hasData)\r\n\r\n    return {\r\n      ...currentState,\r\n      data,\r\n      currentData: currentState.data,\r\n      isFetching,\r\n      isLoading,\r\n      isSuccess,\r\n    } as UseQueryStateDefaultResult<any>\r\n  }\r\n\r\n  function usePrefetch<EndpointName extends QueryKeys<Definitions>>(\r\n    endpointName: EndpointName,\r\n    defaultOptions?: PrefetchOptions\r\n  ) {\r\n    const dispatch = useDispatch<ThunkDispatch<any, any, AnyAction>>()\r\n    const stableDefaultOptions = useShallowStableValue(defaultOptions)\r\n\r\n    return useCallback(\r\n      (arg: any, options?: PrefetchOptions) =>\r\n        dispatch(\r\n          (api.util.prefetch as GenericPrefetchThunk)(endpointName, arg, {\r\n            ...stableDefaultOptions,\r\n            ...options,\r\n          })\r\n        ),\r\n      [endpointName, dispatch, stableDefaultOptions]\r\n    )\r\n  }\r\n\r\n  function buildQueryHooks(name: string): QueryHooks<any> {\r\n    const useQuerySubscription: UseQuerySubscription<any> = (\r\n      arg: any,\r\n      {\r\n        refetchOnReconnect,\r\n        refetchOnFocus,\r\n        refetchOnMountOrArgChange,\r\n        skip = false,\r\n        pollingInterval = 0,\r\n      } = {}\r\n    ) => {\r\n      const { initiate } = api.endpoints[name] as ApiEndpointQuery<\r\n        QueryDefinition<any, any, any, any, any>,\r\n        Definitions\r\n      >\r\n      const dispatch = useDispatch<ThunkDispatch<any, any, AnyAction>>()\r\n      const stableArg = useStableQueryArgs(\r\n        skip ? skipToken : arg,\r\n        // Even if the user provided a per-endpoint `serializeQueryArgs` with\r\n        // a consistent return value, _here_ we want to use the default behavior\r\n        // so we can tell if _anything_ actually changed. Otherwise, we can end up\r\n        // with a case where the query args did change but the serialization doesn't,\r\n        // and then we never try to initiate a refetch.\r\n        defaultSerializeQueryArgs,\r\n        context.endpointDefinitions[name],\r\n        name\r\n      )\r\n      const stableSubscriptionOptions = useShallowStableValue({\r\n        refetchOnReconnect,\r\n        refetchOnFocus,\r\n        pollingInterval,\r\n      })\r\n\r\n      const lastRenderHadSubscription = useRef(false)\r\n\r\n      const promiseRef = useRef<QueryActionCreatorResult<any>>()\r\n\r\n      let { queryCacheKey, requestId } = promiseRef.current || {}\r\n\r\n      // HACK Because the latest state is in the middleware, we actually\r\n      // dispatch an action that will be intercepted and returned.\r\n      let currentRenderHasSubscription = false\r\n      if (queryCacheKey && requestId) {\r\n        // This _should_ return a boolean, even if the types don't line up\r\n        const returnedValue = dispatch(\r\n          api.internalActions.internal_probeSubscription({\r\n            queryCacheKey,\r\n            requestId,\r\n          })\r\n        )\r\n\r\n        if (process.env.NODE_ENV !== 'production') {\r\n          if (typeof returnedValue !== 'boolean') {\r\n            throw new Error(\r\n              `Warning: Middleware for RTK-Query API at reducerPath \"${api.reducerPath}\" has not been added to the store.\r\n    You must add the middleware for RTK-Query to function correctly!`\r\n            )\r\n          }\r\n        }\r\n\r\n        currentRenderHasSubscription = !!returnedValue\r\n      }\r\n\r\n      const subscriptionRemoved =\r\n        !currentRenderHasSubscription && lastRenderHadSubscription.current\r\n\r\n      usePossiblyImmediateEffect(() => {\r\n        lastRenderHadSubscription.current = currentRenderHasSubscription\r\n      })\r\n\r\n      usePossiblyImmediateEffect((): void | undefined => {\r\n        if (subscriptionRemoved) {\r\n          promiseRef.current = undefined\r\n        }\r\n      }, [subscriptionRemoved])\r\n\r\n      usePossiblyImmediateEffect((): void | undefined => {\r\n        const lastPromise = promiseRef.current\r\n        if (\r\n          typeof process !== 'undefined' &&\r\n          process.env.NODE_ENV === 'removeMeOnCompilation'\r\n        ) {\r\n          // this is only present to enforce the rule of hooks to keep `isSubscribed` in the dependency array\r\n          console.log(subscriptionRemoved)\r\n        }\r\n\r\n        if (stableArg === skipToken) {\r\n          lastPromise?.unsubscribe()\r\n          promiseRef.current = undefined\r\n          return\r\n        }\r\n\r\n        const lastSubscriptionOptions = promiseRef.current?.subscriptionOptions\r\n\r\n        if (!lastPromise || lastPromise.arg !== stableArg) {\r\n          lastPromise?.unsubscribe()\r\n          const promise = dispatch(\r\n            initiate(stableArg, {\r\n              subscriptionOptions: stableSubscriptionOptions,\r\n              forceRefetch: refetchOnMountOrArgChange,\r\n            })\r\n          )\r\n\r\n          promiseRef.current = promise\r\n        } else if (stableSubscriptionOptions !== lastSubscriptionOptions) {\r\n          lastPromise.updateSubscriptionOptions(stableSubscriptionOptions)\r\n        }\r\n      }, [\r\n        dispatch,\r\n        initiate,\r\n        refetchOnMountOrArgChange,\r\n        stableArg,\r\n        stableSubscriptionOptions,\r\n        subscriptionRemoved,\r\n      ])\r\n\r\n      useEffect(() => {\r\n        return () => {\r\n          promiseRef.current?.unsubscribe()\r\n          promiseRef.current = undefined\r\n        }\r\n      }, [])\r\n\r\n      return useMemo(\r\n        () => ({\r\n          /**\r\n           * A method to manually refetch data for the query\r\n           */\r\n          refetch: () => {\r\n            if (!promiseRef.current)\r\n              throw new Error(\r\n                'Cannot refetch a query that has not been started yet.'\r\n              )\r\n            return promiseRef.current?.refetch()\r\n          },\r\n        }),\r\n        []\r\n      )\r\n    }\r\n\r\n    const useLazyQuerySubscription: UseLazyQuerySubscription<any> = ({\r\n      refetchOnReconnect,\r\n      refetchOnFocus,\r\n      pollingInterval = 0,\r\n    } = {}) => {\r\n      const { initiate } = api.endpoints[name] as ApiEndpointQuery<\r\n        QueryDefinition<any, any, any, any, any>,\r\n        Definitions\r\n      >\r\n      const dispatch = useDispatch<ThunkDispatch<any, any, AnyAction>>()\r\n\r\n      const [arg, setArg] = useState<any>(UNINITIALIZED_VALUE)\r\n      const promiseRef = useRef<QueryActionCreatorResult<any> | undefined>()\r\n\r\n      const stableSubscriptionOptions = useShallowStableValue({\r\n        refetchOnReconnect,\r\n        refetchOnFocus,\r\n        pollingInterval,\r\n      })\r\n\r\n      usePossiblyImmediateEffect(() => {\r\n        const lastSubscriptionOptions = promiseRef.current?.subscriptionOptions\r\n\r\n        if (stableSubscriptionOptions !== lastSubscriptionOptions) {\r\n          promiseRef.current?.updateSubscriptionOptions(\r\n            stableSubscriptionOptions\r\n          )\r\n        }\r\n      }, [stableSubscriptionOptions])\r\n\r\n      const subscriptionOptionsRef = useRef(stableSubscriptionOptions)\r\n      usePossiblyImmediateEffect(() => {\r\n        subscriptionOptionsRef.current = stableSubscriptionOptions\r\n      }, [stableSubscriptionOptions])\r\n\r\n      const trigger = useCallback(\r\n        function (arg: any, preferCacheValue = false) {\r\n          let promise: QueryActionCreatorResult<any>\r\n\r\n          batch(() => {\r\n            promiseRef.current?.unsubscribe()\r\n\r\n            promiseRef.current = promise = dispatch(\r\n              initiate(arg, {\r\n                subscriptionOptions: subscriptionOptionsRef.current,\r\n                forceRefetch: !preferCacheValue,\r\n              })\r\n            )\r\n\r\n            setArg(arg)\r\n          })\r\n\r\n          return promise!\r\n        },\r\n        [dispatch, initiate]\r\n      )\r\n\r\n      /* cleanup on unmount */\r\n      useEffect(() => {\r\n        return () => {\r\n          promiseRef?.current?.unsubscribe()\r\n        }\r\n      }, [])\r\n\r\n      /* if \"cleanup on unmount\" was triggered from a fast refresh, we want to reinstate the query */\r\n      useEffect(() => {\r\n        if (arg !== UNINITIALIZED_VALUE && !promiseRef.current) {\r\n          trigger(arg, true)\r\n        }\r\n      }, [arg, trigger])\r\n\r\n      return useMemo(() => [trigger, arg] as const, [trigger, arg])\r\n    }\r\n\r\n    const useQueryState: UseQueryState<any> = (\r\n      arg: any,\r\n      { skip = false, selectFromResult } = {}\r\n    ) => {\r\n      const { select } = api.endpoints[name] as ApiEndpointQuery<\r\n        QueryDefinition<any, any, any, any, any>,\r\n        Definitions\r\n      >\r\n      const stableArg = useStableQueryArgs(\r\n        skip ? skipToken : arg,\r\n        serializeQueryArgs,\r\n        context.endpointDefinitions[name],\r\n        name\r\n      )\r\n\r\n      type ApiRootState = Parameters<ReturnType<typeof select>>[0]\r\n\r\n      const lastValue = useRef<any>()\r\n\r\n      const selectDefaultResult: Selector<ApiRootState, any, [any]> = useMemo(\r\n        () =>\r\n          createSelector(\r\n            [\r\n              select(stableArg),\r\n              (_: ApiRootState, lastResult: any) => lastResult,\r\n              (_: ApiRootState) => stableArg,\r\n            ],\r\n            queryStatePreSelector\r\n          ),\r\n        [select, stableArg]\r\n      )\r\n\r\n      const querySelector: Selector<ApiRootState, any, [any]> = useMemo(\r\n        () =>\r\n          selectFromResult\r\n            ? createSelector([selectDefaultResult], selectFromResult)\r\n            : selectDefaultResult,\r\n        [selectDefaultResult, selectFromResult]\r\n      )\r\n\r\n      const currentState = useSelector(\r\n        (state: RootState<Definitions, any, any>) =>\r\n          querySelector(state, lastValue.current),\r\n        shallowEqual\r\n      )\r\n\r\n      const store = useStore<RootState<Definitions, any, any>>()\r\n      const newLastValue = selectDefaultResult(\r\n        store.getState(),\r\n        lastValue.current\r\n      )\r\n      useIsomorphicLayoutEffect(() => {\r\n        lastValue.current = newLastValue\r\n      }, [newLastValue])\r\n\r\n      return currentState\r\n    }\r\n\r\n    return {\r\n      useQueryState,\r\n      useQuerySubscription,\r\n      useLazyQuerySubscription,\r\n      useLazyQuery(options) {\r\n        const [trigger, arg] = useLazyQuerySubscription(options)\r\n        const queryStateResults = useQueryState(arg, {\r\n          ...options,\r\n          skip: arg === UNINITIALIZED_VALUE,\r\n        })\r\n\r\n        const info = useMemo(() => ({ lastArg: arg }), [arg])\r\n        return useMemo(\r\n          () => [trigger, queryStateResults, info],\r\n          [trigger, queryStateResults, info]\r\n        )\r\n      },\r\n      useQuery(arg, options) {\r\n        const querySubscriptionResults = useQuerySubscription(arg, options)\r\n        const queryStateResults = useQueryState(arg, {\r\n          selectFromResult:\r\n            arg === skipToken || options?.skip\r\n              ? undefined\r\n              : noPendingQueryStateSelector,\r\n          ...options,\r\n        })\r\n\r\n        const { data, status, isLoading, isSuccess, isError, error } =\r\n          queryStateResults\r\n        useDebugValue({ data, status, isLoading, isSuccess, isError, error })\r\n\r\n        return useMemo(\r\n          () => ({ ...queryStateResults, ...querySubscriptionResults }),\r\n          [queryStateResults, querySubscriptionResults]\r\n        )\r\n      },\r\n    }\r\n  }\r\n\r\n  function buildMutationHook(name: string): UseMutation<any> {\r\n    return ({\r\n      selectFromResult = defaultMutationStateSelector,\r\n      fixedCacheKey,\r\n    } = {}) => {\r\n      const { select, initiate } = api.endpoints[name] as ApiEndpointMutation<\r\n        MutationDefinition<any, any, any, any, any>,\r\n        Definitions\r\n      >\r\n      const dispatch = useDispatch<ThunkDispatch<any, any, AnyAction>>()\r\n      const [promise, setPromise] = useState<MutationActionCreatorResult<any>>()\r\n\r\n      useEffect(\r\n        () => () => {\r\n          if (!promise?.arg.fixedCacheKey) {\r\n            promise?.reset()\r\n          }\r\n        },\r\n        [promise]\r\n      )\r\n\r\n      const triggerMutation = useCallback(\r\n        function (arg: Parameters<typeof initiate>['0']) {\r\n          const promise = dispatch(initiate(arg, { fixedCacheKey }))\r\n          setPromise(promise)\r\n          return promise\r\n        },\r\n        [dispatch, initiate, fixedCacheKey]\r\n      )\r\n\r\n      const { requestId } = promise || {}\r\n      const mutationSelector = useMemo(\r\n        () =>\r\n          createSelector(\r\n            [select({ fixedCacheKey, requestId: promise?.requestId })],\r\n            selectFromResult\r\n          ),\r\n        [select, promise, selectFromResult, fixedCacheKey]\r\n      )\r\n\r\n      const currentState = useSelector(mutationSelector, shallowEqual)\r\n      const originalArgs =\r\n        fixedCacheKey == null ? promise?.arg.originalArgs : undefined\r\n      const reset = useCallback(() => {\r\n        batch(() => {\r\n          if (promise) {\r\n            setPromise(undefined)\r\n          }\r\n          if (fixedCacheKey) {\r\n            dispatch(\r\n              api.internalActions.removeMutationResult({\r\n                requestId,\r\n                fixedCacheKey,\r\n              })\r\n            )\r\n          }\r\n        })\r\n      }, [dispatch, fixedCacheKey, promise, requestId])\r\n\r\n      const {\r\n        endpointName,\r\n        data,\r\n        status,\r\n        isLoading,\r\n        isSuccess,\r\n        isError,\r\n        error,\r\n      } = currentState\r\n      useDebugValue({\r\n        endpointName,\r\n        data,\r\n        status,\r\n        isLoading,\r\n        isSuccess,\r\n        isError,\r\n        error,\r\n      })\r\n\r\n      const finalState = useMemo(\r\n        () => ({ ...currentState, originalArgs, reset }),\r\n        [currentState, originalArgs, reset]\r\n      )\r\n\r\n      return useMemo(\r\n        () => [triggerMutation, finalState] as const,\r\n        [triggerMutation, finalState]\r\n      )\r\n    }\r\n  }\r\n}\r\n", "import { useEffect, useRef, useMemo } from 'react'\r\nimport type { SerializeQueryArgs } from '@reduxjs/toolkit/query'\r\nimport type { EndpointDefinition } from '@reduxjs/toolkit/query'\r\n\r\nexport function useStableQueryArgs<T>(\r\n  queryArgs: T,\r\n  serialize: SerializeQueryArgs<any>,\r\n  endpointDefinition: EndpointDefinition<any, any, any, any>,\r\n  endpointName: string\r\n) {\r\n  const incoming = useMemo(\r\n    () => ({\r\n      queryArgs,\r\n      serialized:\r\n        typeof queryArgs == 'object'\r\n          ? serialize({ queryArgs, endpointDefinition, endpointName })\r\n          : queryArgs,\r\n    }),\r\n    [queryArgs, serialize, endpointDefinition, endpointName]\r\n  )\r\n  const cache = useRef(incoming)\r\n  useEffect(() => {\r\n    if (cache.current.serialized !== incoming.serialized) {\r\n      cache.current = incoming\r\n    }\r\n  }, [incoming])\r\n\r\n  return cache.current.serialized === incoming.serialized\r\n    ? cache.current.queryArgs\r\n    : queryArgs\r\n}\r\n", "export const UNINITIALIZED_VALUE = Symbol()\r\nexport type UninitializedValue = typeof UNINITIALIZED_VALUE\r\n", "import { useEffect, useRef } from 'react'\r\nimport { shallowEqual } from 'react-redux'\r\n\r\nexport function useShallowStableValue<T>(value: T) {\r\n  const cache = useRef(value)\r\n  useEffect(() => {\r\n    if (!shallowEqual(cache.current, value)) {\r\n      cache.current = value\r\n    }\r\n  }, [value])\r\n\r\n  return shallowEqual(cache.current, value) ? cache.current : value\r\n}\r\n", "import type { QueryCacheKey } from './core/apiState'\r\nimport type { EndpointDefinition } from './endpointDefinitions'\r\nimport { isPlainObject } from '@reduxjs/toolkit'\r\n\r\nconst cache: WeakMap<any, string> | undefined = WeakMap\r\n  ? new WeakMap()\r\n  : undefined\r\n\r\nexport const defaultSerializeQueryArgs: SerializeQueryArgs<any> = ({\r\n  endpointName,\r\n  queryArgs,\r\n}) => {\r\n  let serialized = ''\r\n\r\n  const cached = cache?.get(queryArgs)\r\n\r\n  if (typeof cached === 'string') {\r\n    serialized = cached\r\n  } else {\r\n    const stringified = JSON.stringify(queryArgs, (key, value) =>\r\n      isPlainObject(value)\r\n        ? Object.keys(value)\r\n            .sort()\r\n            .reduce<any>((acc, key) => {\r\n              acc[key] = (value as any)[key]\r\n              return acc\r\n            }, {})\r\n        : value\r\n    )\r\n    if (isPlainObject(queryArgs)) {\r\n      cache?.set(queryArgs, stringified)\r\n    }\r\n    serialized = stringified\r\n  }\r\n  // Sort the object keys before stringifying, to prevent useQuery({ a: 1, b: 2 }) having a different cache key than useQuery({ b: 2, a: 1 })\r\n  return `${endpointName}(${serialized})`\r\n}\r\n\r\nexport type SerializeQueryArgs<QueryArgs, ReturnType = string> = (_: {\r\n  queryArgs: QueryArgs\r\n  endpointDefinition: EndpointDefinition<any, any, any, any>\r\n  endpointName: string\r\n}) => ReturnType\r\n\r\nexport type InternalSerializeQueryArgs = (_: {\r\n  queryArgs: any\r\n  endpointDefinition: EndpointDefinition<any, any, any, any>\r\n  endpointName: string\r\n}) => QueryCacheKey\r\n", "import type { AnyAction, ThunkDispatch } from '@reduxjs/toolkit'\r\nimport type { SerializeQueryArgs } from './defaultSerializeQueryArgs'\r\nimport type { QuerySubState, RootState } from './core/apiState'\r\nimport type {\r\n  BaseQueryExtraOptions,\r\n  BaseQueryFn,\r\n  BaseQueryResult,\r\n  BaseQueryArg,\r\n  BaseQueryApi,\r\n  QueryReturnValue,\r\n  BaseQueryError,\r\n  BaseQueryMeta,\r\n} from './baseQueryTypes'\r\nimport type {\r\n  HasRequiredProps,\r\n  MaybePromise,\r\n  OmitFromUnion,\r\n  CastAny,\r\n  NonUndefined,\r\n  UnwrapPromise,\r\n} from './tsHelpers'\r\nimport type { NEVER } from './fakeBaseQuery'\r\nimport type { Api } from '@reduxjs/toolkit/query'\r\n\r\nconst resultType = /* @__PURE__ */ Symbol()\r\nconst baseQuery = /* @__PURE__ */ Symbol()\r\n\r\ninterface EndpointDefinitionWithQuery<\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  ResultType\r\n> {\r\n  /**\r\n   * `query` can be a function that returns either a `string` or an `object` which is passed to your `baseQuery`. If you are using [fetchBaseQuery](./fetchBaseQuery), this can return either a `string` or an `object` of properties in `FetchArgs`. If you use your own custom [`baseQuery`](../../rtk-query/usage/customizing-queries), you can customize this behavior to your liking.\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"query example\"\r\n   *\r\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   * type PostsResponse = Post[]\r\n   *\r\n   * const api = createApi({\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   tagTypes: ['Post'],\r\n   *   endpoints: (build) => ({\r\n   *     getPosts: build.query<PostsResponse, void>({\r\n   *       // highlight-start\r\n   *       query: () => 'posts',\r\n   *       // highlight-end\r\n   *     }),\r\n   *     addPost: build.mutation<Post, Partial<Post>>({\r\n   *      // highlight-start\r\n   *      query: (body) => ({\r\n   *        url: `posts`,\r\n   *        method: 'POST',\r\n   *        body,\r\n   *      }),\r\n   *      // highlight-end\r\n   *      invalidatesTags: [{ type: 'Post', id: 'LIST' }],\r\n   *    }),\r\n   *   })\r\n   * })\r\n   * ```\r\n   */\r\n  query(arg: QueryArg): BaseQueryArg<BaseQuery>\r\n  queryFn?: never\r\n  /**\r\n   * A function to manipulate the data returned by a query or mutation.\r\n   */\r\n  transformResponse?(\r\n    baseQueryReturnValue: BaseQueryResult<BaseQuery>,\r\n    meta: BaseQueryMeta<BaseQuery>,\r\n    arg: QueryArg\r\n  ): ResultType | Promise<ResultType>\r\n  /**\r\n   * A function to manipulate the data returned by a failed query or mutation.\r\n   */\r\n  transformErrorResponse?(\r\n    baseQueryReturnValue: BaseQueryError<BaseQuery>,\r\n    meta: BaseQueryMeta<BaseQuery>,\r\n    arg: QueryArg\r\n  ): unknown\r\n  /**\r\n   * Defaults to `true`.\r\n   *\r\n   * Most apps should leave this setting on. The only time it can be a performance issue\r\n   * is if an API returns extremely large amounts of data (e.g. 10,000 rows per request) and\r\n   * you're unable to paginate it.\r\n   *\r\n   * For details of how this works, please see the below. When it is set to `false`,\r\n   * every request will cause subscribed components to rerender, even when the data has not changed.\r\n   *\r\n   * @see https://redux-toolkit.js.org/api/other-exports#copywithstructuralsharing\r\n   */\r\n  structuralSharing?: boolean\r\n}\r\n\r\ninterface EndpointDefinitionWithQueryFn<\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  ResultType\r\n> {\r\n  /**\r\n   * Can be used in place of `query` as an inline function that bypasses `baseQuery` completely for the endpoint.\r\n   *\r\n   * @example\r\n   * ```ts\r\n   * // codeblock-meta title=\"Basic queryFn example\"\r\n   *\r\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   * type PostsResponse = Post[]\r\n   *\r\n   * const api = createApi({\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   endpoints: (build) => ({\r\n   *     getPosts: build.query<PostsResponse, void>({\r\n   *       query: () => 'posts',\r\n   *     }),\r\n   *     flipCoin: build.query<'heads' | 'tails', void>({\r\n   *       // highlight-start\r\n   *       queryFn(arg, queryApi, extraOptions, baseQuery) {\r\n   *         const randomVal = Math.random()\r\n   *         if (randomVal < 0.45) {\r\n   *           return { data: 'heads' }\r\n   *         }\r\n   *         if (randomVal < 0.9) {\r\n   *           return { data: 'tails' }\r\n   *         }\r\n   *         return { error: { status: 500, statusText: 'Internal Server Error', data: \"Coin landed on it's edge!\" } }\r\n   *       }\r\n   *       // highlight-end\r\n   *     })\r\n   *   })\r\n   * })\r\n   * ```\r\n   */\r\n  queryFn(\r\n    arg: QueryArg,\r\n    api: BaseQueryApi,\r\n    extraOptions: BaseQueryExtraOptions<BaseQuery>,\r\n    baseQuery: (arg: Parameters<BaseQuery>[0]) => ReturnType<BaseQuery>\r\n  ): MaybePromise<QueryReturnValue<ResultType, BaseQueryError<BaseQuery>>>\r\n  query?: never\r\n  transformResponse?: never\r\n  transformErrorResponse?: never\r\n  /**\r\n   * Defaults to `true`.\r\n   *\r\n   * Most apps should leave this setting on. The only time it can be a performance issue\r\n   * is if an API returns extremely large amounts of data (e.g. 10,000 rows per request) and\r\n   * you're unable to paginate it.\r\n   *\r\n   * For details of how this works, please see the below. When it is set to `false`,\r\n   * every request will cause subscribed components to rerender, even when the data has not changed.\r\n   *\r\n   * @see https://redux-toolkit.js.org/api/other-exports#copywithstructuralsharing\r\n   */\r\n  structuralSharing?: boolean\r\n}\r\n\r\nexport interface BaseEndpointTypes<\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  ResultType\r\n> {\r\n  QueryArg: QueryArg\r\n  BaseQuery: BaseQuery\r\n  ResultType: ResultType\r\n}\r\n\r\nexport type BaseEndpointDefinition<\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  ResultType\r\n> = (\r\n  | ([CastAny<BaseQueryResult<BaseQuery>, {}>] extends [NEVER]\r\n      ? never\r\n      : EndpointDefinitionWithQuery<QueryArg, BaseQuery, ResultType>)\r\n  | EndpointDefinitionWithQueryFn<QueryArg, BaseQuery, ResultType>\r\n) & {\r\n  /* phantom type */\r\n  [resultType]?: ResultType\r\n  /* phantom type */\r\n  [baseQuery]?: BaseQuery\r\n} & HasRequiredProps<\r\n    BaseQueryExtraOptions<BaseQuery>,\r\n    { extraOptions: BaseQueryExtraOptions<BaseQuery> },\r\n    { extraOptions?: BaseQueryExtraOptions<BaseQuery> }\r\n  >\r\n\r\nexport enum DefinitionType {\r\n  query = 'query',\r\n  mutation = 'mutation',\r\n}\r\n\r\nexport type GetResultDescriptionFn<\r\n  TagTypes extends string,\r\n  ResultType,\r\n  QueryArg,\r\n  ErrorType,\r\n  MetaType\r\n> = (\r\n  result: ResultType | undefined,\r\n  error: ErrorType | undefined,\r\n  arg: QueryArg,\r\n  meta: MetaType\r\n) => ReadonlyArray<TagDescription<TagTypes>>\r\n\r\nexport type FullTagDescription<TagType> = {\r\n  type: TagType\r\n  id?: number | string\r\n}\r\nexport type TagDescription<TagType> = TagType | FullTagDescription<TagType>\r\nexport type ResultDescription<\r\n  TagTypes extends string,\r\n  ResultType,\r\n  QueryArg,\r\n  ErrorType,\r\n  MetaType\r\n> =\r\n  | ReadonlyArray<TagDescription<TagTypes>>\r\n  | GetResultDescriptionFn<TagTypes, ResultType, QueryArg, ErrorType, MetaType>\r\n\r\n/** @deprecated please use `onQueryStarted` instead */\r\nexport interface QueryApi<ReducerPath extends string, Context extends {}> {\r\n  /** @deprecated please use `onQueryStarted` instead */\r\n  dispatch: ThunkDispatch<any, any, AnyAction>\r\n  /** @deprecated please use `onQueryStarted` instead */\r\n  getState(): RootState<any, any, ReducerPath>\r\n  /** @deprecated please use `onQueryStarted` instead */\r\n  extra: unknown\r\n  /** @deprecated please use `onQueryStarted` instead */\r\n  requestId: string\r\n  /** @deprecated please use `onQueryStarted` instead */\r\n  context: Context\r\n}\r\n\r\nexport interface QueryTypes<\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  TagTypes extends string,\r\n  ResultType,\r\n  ReducerPath extends string = string\r\n> extends BaseEndpointTypes<QueryArg, BaseQuery, ResultType> {\r\n  /**\r\n   * The endpoint definition type. To be used with some internal generic types.\r\n   * @example\r\n   * ```ts\r\n   * const useMyWrappedHook: UseQuery<typeof api.endpoints.query.Types.QueryDefinition> = ...\r\n   * ```\r\n   */\r\n  QueryDefinition: QueryDefinition<\r\n    QueryArg,\r\n    BaseQuery,\r\n    TagTypes,\r\n    ResultType,\r\n    ReducerPath\r\n  >\r\n  TagTypes: TagTypes\r\n  ReducerPath: ReducerPath\r\n}\r\n\r\nexport interface QueryExtraOptions<\r\n  TagTypes extends string,\r\n  ResultType,\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  ReducerPath extends string = string\r\n> {\r\n  type: DefinitionType.query\r\n  /**\r\n   * Used by `query` endpoints. Determines which 'tag' is attached to the cached data returned by the query.\r\n   * Expects an array of tag type strings, an array of objects of tag types with ids, or a function that returns such an array.\r\n   * 1.  `['Post']` - equivalent to `2`\r\n   * 2.  `[{ type: 'Post' }]` - equivalent to `1`\r\n   * 3.  `[{ type: 'Post', id: 1 }]`\r\n   * 4.  `(result, error, arg) => ['Post']` - equivalent to `5`\r\n   * 5.  `(result, error, arg) => [{ type: 'Post' }]` - equivalent to `4`\r\n   * 6.  `(result, error, arg) => [{ type: 'Post', id: 1 }]`\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"providesTags example\"\r\n   *\r\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   * type PostsResponse = Post[]\r\n   *\r\n   * const api = createApi({\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   tagTypes: ['Posts'],\r\n   *   endpoints: (build) => ({\r\n   *     getPosts: build.query<PostsResponse, void>({\r\n   *       query: () => 'posts',\r\n   *       // highlight-start\r\n   *       providesTags: (result) =>\r\n   *         result\r\n   *           ? [\r\n   *               ...result.map(({ id }) => ({ type: 'Posts' as const, id })),\r\n   *               { type: 'Posts', id: 'LIST' },\r\n   *             ]\r\n   *           : [{ type: 'Posts', id: 'LIST' }],\r\n   *       // highlight-end\r\n   *     })\r\n   *   })\r\n   * })\r\n   * ```\r\n   */\r\n  providesTags?: ResultDescription<\r\n    TagTypes,\r\n    ResultType,\r\n    QueryArg,\r\n    BaseQueryError<BaseQuery>,\r\n    BaseQueryMeta<BaseQuery>\r\n  >\r\n  /**\r\n   * Not to be used. A query should not invalidate tags in the cache.\r\n   */\r\n  invalidatesTags?: never\r\n\r\n  /**\r\n   * Can be provided to return a custom cache key value based on the query arguments.\r\n   *\r\n   * This is primarily intended for cases where a non-serializable value is passed as part of the query arg object and should be excluded from the cache key.  It may also be used for cases where an endpoint should only have a single cache entry, such as an infinite loading / pagination implementation.\r\n   *\r\n   * Unlike the `createApi` version which can _only_ return a string, this per-endpoint option can also return an an object, number, or boolean.  If it returns a string, that value will be used as the cache key directly.  If it returns an object / number / boolean, that value will be passed to the built-in `defaultSerializeQueryArgs`.  This simplifies the use case of stripping out args you don't want included in the cache key.\r\n   *\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"serializeQueryArgs : exclude value\"\r\n   *\r\n   * import { createApi, fetchBaseQuery, defaultSerializeQueryArgs } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   *\r\n   * interface MyApiClient {\r\n   *   fetchPost: (id: string) => Promise<Post>\r\n   * }\r\n   *\r\n   * createApi({\r\n   *  baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *  endpoints: (build) => ({\r\n   *    // Example: an endpoint with an API client passed in as an argument,\r\n   *    // but only the item ID should be used as the cache key\r\n   *    getPost: build.query<Post, { id: string; client: MyApiClient }>({\r\n   *      queryFn: async ({ id, client }) => {\r\n   *        const post = await client.fetchPost(id)\r\n   *        return { data: post }\r\n   *      },\r\n   *      // highlight-start\r\n   *      serializeQueryArgs: ({ queryArgs, endpointDefinition, endpointName }) => {\r\n   *        const { id } = queryArgs\r\n   *        // This can return a string, an object, a number, or a boolean.\r\n   *        // If it returns an object, number or boolean, that value\r\n   *        // will be serialized automatically via `defaultSerializeQueryArgs`\r\n   *        return { id } // omit `client` from the cache key\r\n   *\r\n   *        // Alternately, you can use `defaultSerializeQueryArgs` yourself:\r\n   *        // return defaultSerializeQueryArgs({\r\n   *        //   endpointName,\r\n   *        //   queryArgs: { id },\r\n   *        //   endpointDefinition\r\n   *        // })\r\n   *        // Or  create and return a string yourself:\r\n   *        // return `getPost(${id})`\r\n   *      },\r\n   *      // highlight-end\r\n   *    }),\r\n   *  }),\r\n   *})\r\n   * ```\r\n   */\r\n  serializeQueryArgs?: SerializeQueryArgs<\r\n    QueryArg,\r\n    string | number | boolean | Record<any, any>\r\n  >\r\n\r\n  /**\r\n   * Can be provided to merge an incoming response value into the current cache data.\r\n   * If supplied, no automatic structural sharing will be applied - it's up to\r\n   * you to update the cache appropriately.\r\n   *\r\n   * Since RTKQ normally replaces cache entries with the new response, you will usually\r\n   * need to use this with the `serializeQueryArgs` or `forceRefetch` options to keep\r\n   * an existing cache entry so that it can be updated.\r\n   *\r\n   * Since this is wrapped with Immer, you may either mutate the `currentCacheValue` directly,\r\n   * or return a new value, but _not_ both at once.\r\n   *\r\n   * Will only be called if the existing `currentCacheData` is _not_ `undefined` - on first response,\r\n   * the cache entry will just save the response data directly.\r\n   *\r\n   * Useful if you don't want a new request to completely override the current cache value,\r\n   * maybe because you have manually updated it from another source and don't want those\r\n   * updates to get lost.\r\n   *\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"merge: pagination\"\r\n   *\r\n   * import { createApi, fetchBaseQuery, defaultSerializeQueryArgs } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   *\r\n   * createApi({\r\n   *  baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *  endpoints: (build) => ({\r\n   *    listItems: build.query<string[], number>({\r\n   *      query: (pageNumber) => `/listItems?page=${pageNumber}`,\r\n   *     // Only have one cache entry because the arg always maps to one string\r\n   *     serializeQueryArgs: ({ endpointName }) => {\r\n   *       return endpointName\r\n   *      },\r\n   *      // Always merge incoming data to the cache entry\r\n   *      merge: (currentCache, newItems) => {\r\n   *        currentCache.push(...newItems)\r\n   *      },\r\n   *      // Refetch when the page arg changes\r\n   *      forceRefetch({ currentArg, previousArg }) {\r\n   *        return currentArg !== previousArg\r\n   *      },\r\n   *    }),\r\n   *  }),\r\n   *})\r\n   * ```\r\n   */\r\n  merge?(\r\n    currentCacheData: ResultType,\r\n    responseData: ResultType,\r\n    otherArgs: {\r\n      arg: QueryArg\r\n      baseQueryMeta: BaseQueryMeta<BaseQuery>\r\n      requestId: string\r\n      fulfilledTimeStamp: number\r\n    }\r\n  ): ResultType | void\r\n\r\n  /**\r\n   * Check to see if the endpoint should force a refetch in cases where it normally wouldn't.\r\n   * This is primarily useful for \"infinite scroll\" / pagination use cases where\r\n   * RTKQ is keeping a single cache entry that is added to over time, in combination\r\n   * with `serializeQueryArgs` returning a fixed cache key and a `merge` callback\r\n   * set to add incoming data to the cache entry each time.\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"forceRefresh: pagination\"\r\n   *\r\n   * import { createApi, fetchBaseQuery, defaultSerializeQueryArgs } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   *\r\n   * createApi({\r\n   *  baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *  endpoints: (build) => ({\r\n   *    listItems: build.query<string[], number>({\r\n   *      query: (pageNumber) => `/listItems?page=${pageNumber}`,\r\n   *     // Only have one cache entry because the arg always maps to one string\r\n   *     serializeQueryArgs: ({ endpointName }) => {\r\n   *       return endpointName\r\n   *      },\r\n   *      // Always merge incoming data to the cache entry\r\n   *      merge: (currentCache, newItems) => {\r\n   *        currentCache.push(...newItems)\r\n   *      },\r\n   *      // Refetch when the page arg changes\r\n   *      forceRefetch({ currentArg, previousArg }) {\r\n   *        return currentArg !== previousArg\r\n   *      },\r\n   *    }),\r\n   *  }),\r\n   *})\r\n   * ```\r\n   */\r\n  forceRefetch?(params: {\r\n    currentArg: QueryArg | undefined\r\n    previousArg: QueryArg | undefined\r\n    state: RootState<any, any, string>\r\n    endpointState?: QuerySubState<any>\r\n  }): boolean\r\n\r\n  /**\r\n   * All of these are `undefined` at runtime, purely to be used in TypeScript declarations!\r\n   */\r\n  Types?: QueryTypes<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>\r\n}\r\n\r\nexport type QueryDefinition<\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  TagTypes extends string,\r\n  ResultType,\r\n  ReducerPath extends string = string\r\n> = BaseEndpointDefinition<QueryArg, BaseQuery, ResultType> &\r\n  QueryExtraOptions<TagTypes, ResultType, QueryArg, BaseQuery, ReducerPath>\r\n\r\nexport interface MutationTypes<\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  TagTypes extends string,\r\n  ResultType,\r\n  ReducerPath extends string = string\r\n> extends BaseEndpointTypes<QueryArg, BaseQuery, ResultType> {\r\n  /**\r\n   * The endpoint definition type. To be used with some internal generic types.\r\n   * @example\r\n   * ```ts\r\n   * const useMyWrappedHook: UseMutation<typeof api.endpoints.query.Types.MutationDefinition> = ...\r\n   * ```\r\n   */\r\n  MutationDefinition: MutationDefinition<\r\n    QueryArg,\r\n    BaseQuery,\r\n    TagTypes,\r\n    ResultType,\r\n    ReducerPath\r\n  >\r\n  TagTypes: TagTypes\r\n  ReducerPath: ReducerPath\r\n}\r\n\r\nexport interface MutationExtraOptions<\r\n  TagTypes extends string,\r\n  ResultType,\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  ReducerPath extends string = string\r\n> {\r\n  type: DefinitionType.mutation\r\n  /**\r\n   * Used by `mutation` endpoints. Determines which cached data should be either re-fetched or removed from the cache.\r\n   * Expects the same shapes as `providesTags`.\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"invalidatesTags example\"\r\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   * type PostsResponse = Post[]\r\n   *\r\n   * const api = createApi({\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   tagTypes: ['Posts'],\r\n   *   endpoints: (build) => ({\r\n   *     getPosts: build.query<PostsResponse, void>({\r\n   *       query: () => 'posts',\r\n   *       providesTags: (result) =>\r\n   *         result\r\n   *           ? [\r\n   *               ...result.map(({ id }) => ({ type: 'Posts' as const, id })),\r\n   *               { type: 'Posts', id: 'LIST' },\r\n   *             ]\r\n   *           : [{ type: 'Posts', id: 'LIST' }],\r\n   *     }),\r\n   *     addPost: build.mutation<Post, Partial<Post>>({\r\n   *       query(body) {\r\n   *         return {\r\n   *           url: `posts`,\r\n   *           method: 'POST',\r\n   *           body,\r\n   *         }\r\n   *       },\r\n   *       // highlight-start\r\n   *       invalidatesTags: [{ type: 'Posts', id: 'LIST' }],\r\n   *       // highlight-end\r\n   *     }),\r\n   *   })\r\n   * })\r\n   * ```\r\n   */\r\n  invalidatesTags?: ResultDescription<\r\n    TagTypes,\r\n    ResultType,\r\n    QueryArg,\r\n    BaseQueryError<BaseQuery>,\r\n    BaseQueryMeta<BaseQuery>\r\n  >\r\n  /**\r\n   * Not to be used. A mutation should not provide tags to the cache.\r\n   */\r\n  providesTags?: never\r\n\r\n  /**\r\n   * All of these are `undefined` at runtime, purely to be used in TypeScript declarations!\r\n   */\r\n  Types?: MutationTypes<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>\r\n}\r\n\r\nexport type MutationDefinition<\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  TagTypes extends string,\r\n  ResultType,\r\n  ReducerPath extends string = string\r\n> = BaseEndpointDefinition<QueryArg, BaseQuery, ResultType> &\r\n  MutationExtraOptions<TagTypes, ResultType, QueryArg, BaseQuery, ReducerPath>\r\n\r\nexport type EndpointDefinition<\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  TagTypes extends string,\r\n  ResultType,\r\n  ReducerPath extends string = string\r\n> =\r\n  | QueryDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>\r\n  | MutationDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>\r\n\r\nexport type EndpointDefinitions = Record<\r\n  string,\r\n  EndpointDefinition<any, any, any, any>\r\n>\r\n\r\nexport function isQueryDefinition(\r\n  e: EndpointDefinition<any, any, any, any>\r\n): e is QueryDefinition<any, any, any, any> {\r\n  return e.type === DefinitionType.query\r\n}\r\n\r\nexport function isMutationDefinition(\r\n  e: EndpointDefinition<any, any, any, any>\r\n): e is MutationDefinition<any, any, any, any> {\r\n  return e.type === DefinitionType.mutation\r\n}\r\n\r\nexport type EndpointBuilder<\r\n  BaseQuery extends BaseQueryFn,\r\n  TagTypes extends string,\r\n  ReducerPath extends string\r\n> = {\r\n  /**\r\n   * An endpoint definition that retrieves data, and may provide tags to the cache.\r\n   *\r\n   * @example\r\n   * ```js\r\n   * // codeblock-meta title=\"Example of all query endpoint options\"\r\n   * const api = createApi({\r\n   *  baseQuery,\r\n   *  endpoints: (build) => ({\r\n   *    getPost: build.query({\r\n   *      query: (id) => ({ url: `post/${id}` }),\r\n   *      // Pick out data and prevent nested properties in a hook or selector\r\n   *      transformResponse: (response) => response.data,\r\n   *      // Pick out error and prevent nested properties in a hook or selector\r\n   *      transformErrorResponse: (response) => response.error,\r\n   *      // `result` is the server response\r\n   *      providesTags: (result, error, id) => [{ type: 'Post', id }],\r\n   *      // trigger side effects or optimistic updates\r\n   *      onQueryStarted(id, { dispatch, getState, extra, requestId, queryFulfilled, getCacheEntry, updateCachedData }) {},\r\n   *      // handle subscriptions etc\r\n   *      onCacheEntryAdded(id, { dispatch, getState, extra, requestId, cacheEntryRemoved, cacheDataLoaded, getCacheEntry, updateCachedData }) {},\r\n   *    }),\r\n   *  }),\r\n   *});\r\n   *```\r\n   */\r\n  query<ResultType, QueryArg>(\r\n    definition: OmitFromUnion<\r\n      QueryDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>,\r\n      'type'\r\n    >\r\n  ): QueryDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>\r\n  /**\r\n   * An endpoint definition that alters data on the server or will possibly invalidate the cache.\r\n   *\r\n   * @example\r\n   * ```js\r\n   * // codeblock-meta title=\"Example of all mutation endpoint options\"\r\n   * const api = createApi({\r\n   *   baseQuery,\r\n   *   endpoints: (build) => ({\r\n   *     updatePost: build.mutation({\r\n   *       query: ({ id, ...patch }) => ({ url: `post/${id}`, method: 'PATCH', body: patch }),\r\n   *       // Pick out data and prevent nested properties in a hook or selector\r\n   *       transformResponse: (response) => response.data,\r\n   *       // Pick out error and prevent nested properties in a hook or selector\r\n   *       transformErrorResponse: (response) => response.error,\r\n   *       // `result` is the server response\r\n   *       invalidatesTags: (result, error, id) => [{ type: 'Post', id }],\r\n   *      // trigger side effects or optimistic updates\r\n   *      onQueryStarted(id, { dispatch, getState, extra, requestId, queryFulfilled, getCacheEntry }) {},\r\n   *      // handle subscriptions etc\r\n   *      onCacheEntryAdded(id, { dispatch, getState, extra, requestId, cacheEntryRemoved, cacheDataLoaded, getCacheEntry }) {},\r\n   *     }),\r\n   *   }),\r\n   * });\r\n   * ```\r\n   */\r\n  mutation<ResultType, QueryArg>(\r\n    definition: OmitFromUnion<\r\n      MutationDefinition<\r\n        QueryArg,\r\n        BaseQuery,\r\n        TagTypes,\r\n        ResultType,\r\n        ReducerPath\r\n      >,\r\n      'type'\r\n    >\r\n  ): MutationDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>\r\n}\r\n\r\nexport type AssertTagTypes = <T extends FullTagDescription<string>>(t: T) => T\r\n\r\nexport function calculateProvidedBy<ResultType, QueryArg, ErrorType, MetaType>(\r\n  description:\r\n    | ResultDescription<string, ResultType, QueryArg, ErrorType, MetaType>\r\n    | undefined,\r\n  result: ResultType | undefined,\r\n  error: ErrorType | undefined,\r\n  queryArg: QueryArg,\r\n  meta: MetaType | undefined,\r\n  assertTagTypes: AssertTagTypes\r\n): readonly FullTagDescription<string>[] {\r\n  if (isFunction(description)) {\r\n    return description(\r\n      result as ResultType,\r\n      error as undefined,\r\n      queryArg,\r\n      meta as MetaType\r\n    )\r\n      .map(expandTagDescription)\r\n      .map(assertTagTypes)\r\n  }\r\n  if (Array.isArray(description)) {\r\n    return description.map(expandTagDescription).map(assertTagTypes)\r\n  }\r\n  return []\r\n}\r\n\r\nfunction isFunction<T>(t: T): t is Extract<T, Function> {\r\n  return typeof t === 'function'\r\n}\r\n\r\nexport function expandTagDescription(\r\n  description: TagDescription<string>\r\n): FullTagDescription<string> {\r\n  return typeof description === 'string' ? { type: description } : description\r\n}\r\n\r\nexport type QueryArgFrom<D extends BaseEndpointDefinition<any, any, any>> =\r\n  D extends BaseEndpointDefinition<infer QA, any, any> ? QA : unknown\r\nexport type ResultTypeFrom<D extends BaseEndpointDefinition<any, any, any>> =\r\n  D extends BaseEndpointDefinition<any, any, infer RT> ? RT : unknown\r\n\r\nexport type ReducerPathFrom<\r\n  D extends EndpointDefinition<any, any, any, any, any>\r\n> = D extends EndpointDefinition<any, any, any, any, infer RP> ? RP : unknown\r\n\r\nexport type TagTypesFrom<D extends EndpointDefinition<any, any, any, any>> =\r\n  D extends EndpointDefinition<any, any, infer RP, any> ? RP : unknown\r\n\r\nexport type TagTypesFromApi<T> = T extends Api<any, any, any, infer TagTypes>\r\n  ? TagTypes\r\n  : never\r\n\r\nexport type DefinitionsFromApi<T> = T extends Api<\r\n  any,\r\n  infer Definitions,\r\n  any,\r\n  any\r\n>\r\n  ? Definitions\r\n  : never\r\n\r\nexport type TransformedResponse<\r\n  NewDefinitions extends EndpointDefinitions,\r\n  K,\r\n  ResultType\r\n> = K extends keyof NewDefinitions\r\n  ? NewDefinitions[K]['transformResponse'] extends undefined\r\n    ? ResultType\r\n    : UnwrapPromise<\r\n        ReturnType<NonUndefined<NewDefinitions[K]['transformResponse']>>\r\n      >\r\n  : ResultType\r\n\r\nexport type OverrideResultType<Definition, NewResultType> =\r\n  Definition extends QueryDefinition<\r\n    infer QueryArg,\r\n    infer BaseQuery,\r\n    infer TagTypes,\r\n    any,\r\n    infer ReducerPath\r\n  >\r\n    ? QueryDefinition<QueryArg, BaseQuery, TagTypes, NewResultType, ReducerPath>\r\n    : Definition extends MutationDefinition<\r\n        infer QueryArg,\r\n        infer BaseQuery,\r\n        infer TagTypes,\r\n        any,\r\n        infer ReducerPath\r\n      >\r\n    ? MutationDefinition<\r\n        QueryArg,\r\n        BaseQuery,\r\n        TagTypes,\r\n        NewResultType,\r\n        ReducerPath\r\n      >\r\n    : never\r\n\r\nexport type UpdateDefinitions<\r\n  Definitions extends EndpointDefinitions,\r\n  NewTagTypes extends string,\r\n  NewDefinitions extends EndpointDefinitions\r\n> = {\r\n  [K in keyof Definitions]: Definitions[K] extends QueryDefinition<\r\n    infer QueryArg,\r\n    infer BaseQuery,\r\n    any,\r\n    infer ResultType,\r\n    infer ReducerPath\r\n  >\r\n    ? QueryDefinition<\r\n        QueryArg,\r\n        BaseQuery,\r\n        NewTagTypes,\r\n        TransformedResponse<NewDefinitions, K, ResultType>,\r\n        ReducerPath\r\n      >\r\n    : Definitions[K] extends MutationDefinition<\r\n        infer QueryArg,\r\n        infer BaseQuery,\r\n        any,\r\n        infer ResultType,\r\n        infer ReducerPath\r\n      >\r\n    ? MutationDefinition<\r\n        QueryArg,\r\n        BaseQuery,\r\n        NewTagTypes,\r\n        TransformedResponse<NewDefinitions, K, ResultType>,\r\n        ReducerPath\r\n      >\r\n    : never\r\n}\r\n", "export function capitalize(str: string) {\r\n  return str.replace(str[0], str[0].toUpperCase())\r\n}\r\n", "export type Id<T> = { [K in keyof T]: T[K] } & {}\r\nexport type WithRequiredProp<T, K extends keyof T> = Omit<T, K> &\r\n  Required<Pick<T, K>>\r\nexport type Override<T1, T2> = T2 extends any ? Omit<T1, keyof T2> & T2 : never\r\nexport function assertCast<T>(v: any): asserts v is T {}\r\n\r\nexport function safeAssign<T extends object>(\r\n  target: T,\r\n  ...args: Array<Partial<NoInfer<T>>>\r\n) {\r\n  Object.assign(target, ...args)\r\n}\r\n\r\n/**\r\n * Convert a Union type `(A|B)` to an intersection type `(A&B)`\r\n */\r\nexport type UnionToIntersection<U> = (\r\n  U extends any ? (k: U) => void : never\r\n) extends (k: infer I) => void\r\n  ? I\r\n  : never\r\n\r\nexport type NonOptionalKeys<T> = {\r\n  [K in keyof T]-?: undefined extends T[K] ? never : K\r\n}[keyof T]\r\n\r\nexport type HasRequiredProps<T, True, False> = NonOptionalKeys<T> extends never\r\n  ? False\r\n  : True\r\n\r\nexport type OptionalIfAllPropsOptional<T> = HasRequiredProps<T, T, T | never>\r\n\r\nexport type NoInfer<T> = [T][T extends any ? 0 : never]\r\n\r\nexport type NonUndefined<T> = T extends undefined ? never : T\r\n\r\nexport type UnwrapPromise<T> = T extends PromiseLike<infer V> ? V : T\r\n\r\nexport type MaybePromise<T> = T | PromiseLike<T>\r\n\r\nexport type OmitFromUnion<T, K extends keyof T> = T extends any\r\n  ? Omit<T, K>\r\n  : never\r\n\r\nexport type IsAny<T, True, False = never> = true | false extends (\r\n  T extends never ? true : false\r\n)\r\n  ? True\r\n  : False\r\n\r\nexport type CastAny<T, CastTo> = IsAny<T, CastTo, T>\r\n", "import type { Mu<PERSON>Hooks, QueryHooks } from './buildHooks'\r\nimport { buildHooks } from './buildHooks'\r\nimport { isQueryDefinition, isMutationDefinition } from '../endpointDefinitions'\r\nimport type {\r\n  EndpointDefinitions,\r\n  QueryDefinition,\r\n  MutationDefinition,\r\n  QueryArgFrom,\r\n} from '@reduxjs/toolkit/query'\r\nimport type { Api, Module } from '../apiTypes'\r\nimport { capitalize } from '../utils'\r\nimport { safeAssign } from '../tsHelpers'\r\nimport type { BaseQueryFn } from '@reduxjs/toolkit/query'\r\n\r\nimport type { HooksWithUniqueNames } from './namedHooks'\r\n\r\nimport {\r\n  useDispatch as rrUseDispatch,\r\n  useSelector as rrUseSelector,\r\n  useStore as rrUseStore,\r\n  batch as rrBatch,\r\n} from 'react-redux'\r\nimport type { QueryKeys } from '../core/apiState'\r\nimport type { PrefetchOptions } from '../core/module'\r\n\r\nexport const reactHooksModuleName = /* @__PURE__ */ Symbol()\r\nexport type ReactHooksModule = typeof reactHooksModuleName\r\n\r\ndeclare module '@reduxjs/toolkit/query' {\r\n  export interface ApiModules<\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    BaseQuery extends BaseQueryFn,\r\n    Definitions extends EndpointDefinitions,\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    ReducerPath extends string,\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    TagTypes extends string\r\n  > {\r\n    [reactHooksModuleName]: {\r\n      /**\r\n       *  Endpoints based on the input endpoints provided to `createApi`, containing `select`, `hooks` and `action matchers`.\r\n       */\r\n      endpoints: {\r\n        [K in keyof Definitions]: Definitions[K] extends QueryDefinition<\r\n          any,\r\n          any,\r\n          any,\r\n          any,\r\n          any\r\n        >\r\n          ? QueryHooks<Definitions[K]>\r\n          : Definitions[K] extends MutationDefinition<any, any, any, any, any>\r\n          ? MutationHooks<Definitions[K]>\r\n          : never\r\n      }\r\n      /**\r\n       * A hook that accepts a string endpoint name, and provides a callback that when called, pre-fetches the data for that endpoint.\r\n       */\r\n      usePrefetch<EndpointName extends QueryKeys<Definitions>>(\r\n        endpointName: EndpointName,\r\n        options?: PrefetchOptions\r\n      ): (\r\n        arg: QueryArgFrom<Definitions[EndpointName]>,\r\n        options?: PrefetchOptions\r\n      ) => void\r\n    } & HooksWithUniqueNames<Definitions>\r\n  }\r\n}\r\n\r\ntype RR = typeof import('react-redux')\r\n\r\nexport interface ReactHooksModuleOptions {\r\n  /**\r\n   * The version of the `batchedUpdates` function to be used\r\n   */\r\n  batch?: RR['batch']\r\n  /**\r\n   * The version of the `useDispatch` hook to be used\r\n   */\r\n  useDispatch?: RR['useDispatch']\r\n  /**\r\n   * The version of the `useSelector` hook to be used\r\n   */\r\n  useSelector?: RR['useSelector']\r\n  /**\r\n   * The version of the `useStore` hook to be used\r\n   */\r\n  useStore?: RR['useStore']\r\n  /**\r\n   * Enables performing asynchronous tasks immediately within a render.\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * import {\r\n   *   buildCreateApi,\r\n   *   coreModule,\r\n   *   reactHooksModule\r\n   * } from '@reduxjs/toolkit/query/react'\r\n   *\r\n   * const createApi = buildCreateApi(\r\n   *   coreModule(),\r\n   *   reactHooksModule({ unstable__sideEffectsInRender: true })\r\n   * )\r\n   * ```\r\n   */\r\n  unstable__sideEffectsInRender?: boolean\r\n}\r\n\r\n/**\r\n * Creates a module that generates react hooks from endpoints, for use with `buildCreateApi`.\r\n *\r\n *  @example\r\n * ```ts\r\n * const MyContext = React.createContext<ReactReduxContextValue>(null as any);\r\n * const customCreateApi = buildCreateApi(\r\n *   coreModule(),\r\n *   reactHooksModule({ useDispatch: createDispatchHook(MyContext) })\r\n * );\r\n * ```\r\n *\r\n * @returns A module for use with `buildCreateApi`\r\n */\r\nexport const reactHooksModule = ({\r\n  batch = rrBatch,\r\n  useDispatch = rrUseDispatch,\r\n  useSelector = rrUseSelector,\r\n  useStore = rrUseStore,\r\n  unstable__sideEffectsInRender = false,\r\n}: ReactHooksModuleOptions = {}): Module<ReactHooksModule> => ({\r\n  name: reactHooksModuleName,\r\n  init(api, { serializeQueryArgs }, context) {\r\n    const anyApi = api as any as Api<\r\n      any,\r\n      Record<string, any>,\r\n      string,\r\n      string,\r\n      ReactHooksModule\r\n    >\r\n    const { buildQueryHooks, buildMutationHook, usePrefetch } = buildHooks({\r\n      api,\r\n      moduleOptions: {\r\n        batch,\r\n        useDispatch,\r\n        useSelector,\r\n        useStore,\r\n        unstable__sideEffectsInRender,\r\n      },\r\n      serializeQueryArgs,\r\n      context,\r\n    })\r\n    safeAssign(anyApi, { usePrefetch })\r\n    safeAssign(context, { batch })\r\n\r\n    return {\r\n      injectEndpoint(endpointName, definition) {\r\n        if (isQueryDefinition(definition)) {\r\n          const {\r\n            useQuery,\r\n            useLazyQuery,\r\n            useLazyQuerySubscription,\r\n            useQueryState,\r\n            useQuerySubscription,\r\n          } = buildQueryHooks(endpointName)\r\n          safeAssign(anyApi.endpoints[endpointName], {\r\n            useQuery,\r\n            useLazyQuery,\r\n            useLazyQuerySubscription,\r\n            useQueryState,\r\n            useQuerySubscription,\r\n          })\r\n          ;(api as any)[`use${capitalize(endpointName)}Query`] = useQuery\r\n          ;(api as any)[`useLazy${capitalize(endpointName)}Query`] =\r\n            useLazyQuery\r\n        } else if (isMutationDefinition(definition)) {\r\n          const useMutation = buildMutationHook(endpointName)\r\n          safeAssign(anyApi.endpoints[endpointName], {\r\n            useMutation,\r\n          })\r\n          ;(api as any)[`use${capitalize(endpointName)}Mutation`] = useMutation\r\n        }\r\n      },\r\n    }\r\n  },\r\n})\r\n", "import { configureStore } from '@reduxjs/toolkit'\r\nimport type { Context } from 'react'\r\nimport { useEffect } from 'react'\r\nimport React from 'react'\r\nimport type { ReactReduxContextValue } from 'react-redux'\r\nimport { Provider } from 'react-redux'\r\nimport { setupListeners } from '@reduxjs/toolkit/query'\r\nimport type { Api } from '@reduxjs/toolkit/query'\r\n\r\n/**\r\n * Can be used as a `Provider` if you **do not already have a Redux store**.\r\n *\r\n * @example\r\n * ```tsx\r\n * // codeblock-meta no-transpile title=\"Basic usage - wrap your App with ApiProvider\"\r\n * import * as React from 'react';\r\n * import { ApiProvider } from '@reduxjs/toolkit/query/react';\r\n * import { Pokemon } from './features/Pokemon';\r\n *\r\n * function App() {\r\n *   return (\r\n *     <ApiProvider api={api}>\r\n *       <Pokemon />\r\n *     </ApiProvider>\r\n *   );\r\n * }\r\n * ```\r\n *\r\n * @remarks\r\n * Using this together with an existing redux store, both will\r\n * conflict with each other - please use the traditional redux setup\r\n * in that case.\r\n */\r\nexport function ApiProvider<A extends Api<any, {}, any, any>>(props: {\r\n  children: any\r\n  api: A\r\n  setupListeners?: Parameters<typeof setupListeners>[1] | false\r\n  context?: Context<ReactReduxContextValue>\r\n}) {\r\n  const [store] = React.useState(() =>\r\n    configureStore({\r\n      reducer: {\r\n        [props.api.reducerPath]: props.api.reducer,\r\n      },\r\n      middleware: (gDM) => gDM().concat(props.api.middleware),\r\n    })\r\n  )\r\n  // Adds the event listeners for online/offline/focus/etc\r\n  useEffect(\r\n    (): undefined | (() => void) =>\r\n      props.setupListeners === false\r\n        ? undefined\r\n        : setupListeners(store.dispatch, props.setupListeners),\r\n    [props.setupListeners, store.dispatch]\r\n  )\r\n\r\n  return (\r\n    <Provider store={store} context={props.context}>\r\n      {props.children}\r\n    </Provider>\r\n  )\r\n}\r\n", null]}