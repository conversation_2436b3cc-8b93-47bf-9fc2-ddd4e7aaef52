import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import './Footer.scss';

// 导航项配置
const navItems = [
  {
    path: '/power',
    icon: '/images/power.png',
    label: 'Power',
    alt: 'Power'
  },
  {
    path: '/earn',
    icon: '/images/earn.png',
    label: 'Earn',
    alt: 'Earn'
  },
  {
    path: '/',
    icon: '/images/miner.png',
    label: 'Miner',
    alt: 'Miner'
  },
  {
    path: '/withdraw',
    icon: '/images/withdraw.png',
    label: 'Withdraw',
    alt: 'Withdraw'
  },
  {
    path: '/tasks',
    icon: '/images/tasks.png',
    label: 'Tasks',
    alt: 'Tasks'
  }
];

const Footer: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const handleNavClick = (path: string) => {
    navigate(path);
  };

  return (
    <footer className="footer">
      <div className="container">
        <nav className="footer__nav">
          <ul className="footer__list">
            {navItems.map((item) => {
              const isActive = location.pathname === item.path;
              
              return (
                <li key={item.path} className="footer__item">
                  <button
                    className={`footer__link ${isActive ? 'footer__link--active' : ''}`}
                    onClick={() => handleNavClick(item.path)}
                  >
                    <img
                      className="footer__icon"
                      src={item.icon}
                      alt={item.alt}
                    />
                    <span className="footer__text">{item.label}</span>
                  </button>
                </li>
              );
            })}
          </ul>
        </nav>
      </div>
    </footer>
  );
};

export default Footer; 