{"name": "nano-memoize", "version": "3.0.16", "description": "Faster than fast, smaller than micro ... a nano speed and nano size memoizer.", "type:": "module", "source": "src/index.js", "main": "dist/nano-memoize.js", "typings": "./index.d.ts", "module": "./index.js", "sideEffects": false, "license": "MIT", "scripts": {"test": "npm run build && mocha ./test/index.js", "prepublish": "npm run build", "build": "parcel build", "benchmark": "npm run build && node ./benchmark/index.js"}, "repository": {"type": "git", "url": "git+https://github.com/anywhichway/nano-memoize.git"}, "keywords": ["memoize", "moize", "fast-memoize", "micro-memoize", "addy-o<PERSON>i"], "author": "<PERSON> <<EMAIL>> (http://www.github.com/anywhichway)", "bugs": {"url": "https://github.com/anywhichway/nano-memoize/issues"}, "homepage": "https://github.com/anywhichway/nano-memoize#readme", "devDependencies": {"benchmark": "^2.1.3", "blanket": "^1.2.3", "bread-compressor-cli": "^1.0.5", "browserify": "^16.5.1", "chai": "^3.4.1", "cli-table": "^0.3.11", "fast-deep-equal": "^3.1.3", "fast-equals": "^1.6.3", "fast-memoize": "^2.5.2", "growl": "^1.10.5", "hash-it": "^4.0.4", "iMemoized": "^1.1.8", "istanbul": "^0.4.2", "lodash": "^4.17.21", "lru-memoize": "^1.1.0", "memize": "^2.1.0", "memoizee": "^0.4.15", "memoizerific": "^1.11.3", "micro-memoize": "^4.1.2", "mocha": "^10.0.0", "moize": "^6.1.5", "ora": "^1.4.0", "parcel": "^2.9.2", "ramda": "^0.26.1", "uglify-es": "^3.3.9", "underscore": "^1.9.1"}, "dependencies": {}}