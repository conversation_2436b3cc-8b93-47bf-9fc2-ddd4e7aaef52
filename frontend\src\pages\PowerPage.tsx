import React, { useState } from 'react';
import './PowerPage.scss';

// 算力包配置
const powerPackages = [
  {
    id: 1,
    name: 'Starter Pack',
    power: 500,
    price: 10,
    bonus: 0,
    popular: false,
  },
  {
    id: 2,
    name: 'Advanced Pack',
    power: 1500,
    price: 25,
    bonus: 10,
    popular: true,
  },
  {
    id: 3,
    name: 'Pro Pack',
    power: 3000,
    price: 45,
    bonus: 25,
    popular: false,
  },
  {
    id: 4,
    name: 'Ultimate Pack',
    power: 5000,
    price: 70,
    bonus: 50,
    popular: false,
  },
];

const PowerPage: React.FC = () => {
  const [selectedPackage, setSelectedPackage] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // 处理购买算力
  const handlePurchase = async (packageId: number) => {
    setIsLoading(true);
    try {
      // TODO: 调用购买API
      await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟API调用
      alert('购买成功！');
      setSelectedPackage(null);
    } catch (error) {
      alert('购买失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <header className="header">
        <div className="container">
          <p className="header__text">CoreX MINER</p>
          <a href="https://t.me/Core_xbot" className="header__link">
            Cloud Mining in Telegram
          </a>
        </div>
      </header>
      
      <main>
        <section>
          <div className="container">
            {/* 当前算力状态 */}
            <div className="power-status-card">
              <div className="power-status__header">
                <div className="power-icon">⚡</div>
                <h2>Current Power</h2>
              </div>
              <div className="power-status__stats">
                <div className="power-stat">
                  <span className="power-value">1,250</span>
                  <span className="power-unit">GPU</span>
                </div>
                <div className="power-stat">
                  <span className="hash-rate">0.000222 H/s</span>
                  <span className="hash-label">Hash Rate</span>
                </div>
              </div>
            </div>

            {/* 促销活动 */}
            <div className="promo-card">
              <div className="promo__content">
                <div className="promo__icon">🎁</div>
                <div className="promo__info">
                  <h4>Power Sale</h4>
                  <p>UP to 200% MORE POWER</p>
                  <span className="promo__timer">Limited Time Offer</span>
                </div>
              </div>
            </div>

            {/* 算力包列表 */}
            <div className="packages-section">
              <div className="packages__header">
                <h3>Power Packages</h3>
                <p>Choose your mining power package</p>
              </div>
              
              <div className="packages__list">
                {powerPackages.map((pkg) => (
                  <div
                    key={pkg.id}
                    className={`package-card ${selectedPackage === pkg.id ? 'selected' : ''} ${pkg.popular ? 'popular' : ''}`}
                    onClick={() => setSelectedPackage(pkg.id)}
                  >
                    {pkg.popular && (
                      <div className="popular-badge">
                        🎁
                        <span>Popular</span>
                      </div>
                    )}
                    
                    <div className="package__header">
                      <h4>{pkg.name}</h4>
                      <div className="package__power">
                        <span className="power-amount">{pkg.power.toLocaleString()}</span>
                        <span className="power-unit">GPU</span>
                      </div>
                    </div>
                    
                    <div className="package__details">
                      <div className="package__price">
                        <span className="price-amount">${pkg.price}</span>
                        <span className="price-currency">USDT</span>
                      </div>
                      
                      {pkg.bonus > 0 && (
                        <div className="package__bonus">
                          🎁
                          <span>+{pkg.bonus}% Bonus</span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 购买按钮 */}
            {selectedPackage && (
              <div className="purchase-section">
                <button
                  className="btn purchase-button"
                  disabled={isLoading}
                  onClick={() => handlePurchase(selectedPackage)}
                >
                  {isLoading ? '处理中...' : 'Purchase Power'}
                </button>
              </div>
            )}

            {/* 说明文本 */}
            <p className="text">
              *Purchase power to increase your mining efficiency and earnings
            </p>
          </div>
        </section>
      </main>
    </>
  );
};

export default PowerPage; 