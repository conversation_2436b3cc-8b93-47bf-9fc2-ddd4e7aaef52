import React, { useState } from 'react';
import { use<PERSON><PERSON>gate, Link } from 'react-router-dom';
import { Form, Input, Button, Toast } from 'antd-mobile';
import { UserOutline, LockOutline, EyeOutline, EyeInvisibleOutline } from 'antd-mobile-icons';
import { useAuth } from '../hooks/useAuth';
import './LoginPage.scss';

const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const { login, isLoading } = useAuth();
  const [showPassword, setShowPassword] = useState(false);

  const onFinish = async (values: { username: string; password: string }) => {
    try {
      const result = await login(values);
      if (result.success) {
        Toast.show('登录成功');
        navigate('/');
      } else {
        Toast.show(result.error || '登录失败');
      }
    } catch (error) {
      Toast.show('登录失败，请重试');
    }
  };

  return (
    <div className="login-page">
      <div className="login-container">
        <div className="login-header">
          <h1>CoreX MINER</h1>
          <p>Welcome back to mining</p>
        </div>

        <Form
          className="login-form"
          onFinish={onFinish}
          layout="vertical"
          footer={
            <Button
              block
              type="submit"
              color="primary"
              size="large"
              loading={isLoading}
            >
              Sign In
            </Button>
          }
        >
          <Form.Item
            name="username"
            label="Username"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input
              placeholder="Enter your username"
              prefix={<UserOutline />}
            />
          </Form.Item>

          <Form.Item
            name="password"
            label="Password"
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input
              placeholder="Enter your password"
              type={showPassword ? 'text' : 'password'}
              prefix={<LockOutline />}
              extra={
                <Button
                  fill="none"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeInvisibleOutline /> : <EyeOutline />}
                </Button>
              }
            />
          </Form.Item>
        </Form>

        <div className="login-footer">
          <p>
            Don't have an account?{' '}
            <Link to="/register" className="register-link">
              Sign up
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default LoginPage; 